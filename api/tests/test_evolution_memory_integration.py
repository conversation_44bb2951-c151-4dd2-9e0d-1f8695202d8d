"""
Unit tests for Memory Evolution Integration module.

Tests the integration wrapper for enhancing add_memories() with evolution intelligence
while preserving existing functionality and providing graceful degradation.
"""

import pytest
import asyncio
import time
import os
import uuid
from unittest.mock import Mock, MagicMock, patch, AsyncMock
from typing import List, Dict, Any, Tuple

# Import test subjects
from app.evolution.memory_integration import (
    MemoryEvolutionIntegration,
    get_memory_evolution_integration,
    enhance_add_memories
)


class TestMemoryEvolutionIntegration:
    """Test MemoryEvolutionIntegration class."""
    
    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = Mock()
        session.query = Mock()
        return session
    
    @pytest.fixture
    def mock_evolution_service(self):
        """Mock evolution service."""
        service = Mock()
        service.process_memory_evolution = AsyncMock()
        service.get_evolution_statistics = AsyncMock()
        service.health_check = AsyncMock()
        return service
    
    @pytest.fixture
    def integration_enabled(self, mock_db_session):
        """Create integration instance with evolution enabled."""
        with patch.dict(os.environ, {"EVOLUTION_INTELLIGENCE_ENABLED": "true", "EVOLUTION_TIMEOUT": "30.0"}):
            with patch('app.evolution.memory_integration.EvolutionService') as mock_service_class:
                mock_service_class.return_value = Mock()
                integration = MemoryEvolutionIntegration(db_session=mock_db_session)
                return integration
    
    @pytest.fixture
    def integration_disabled(self, mock_db_session):
        """Create integration instance with evolution disabled."""
        with patch.dict(os.environ, {"EVOLUTION_INTELLIGENCE_ENABLED": "false"}):
            integration = MemoryEvolutionIntegration(db_session=mock_db_session)
            return integration
    
    def test_initialization_enabled(self, mock_db_session):
        """Test initialization with evolution intelligence enabled."""
        with patch.dict(os.environ, {"EVOLUTION_INTELLIGENCE_ENABLED": "true", "EVOLUTION_TIMEOUT": "45.0"}):
            with patch('app.evolution.memory_integration.EvolutionService') as mock_service_class:
                mock_service = Mock()
                mock_service_class.return_value = mock_service
                
                integration = MemoryEvolutionIntegration(db_session=mock_db_session)
                
                assert integration.feature_flag_enabled is True
                assert integration.evolution_timeout == 45.0
                assert integration.evolution_service == mock_service
                assert integration.db_session == mock_db_session
    
    def test_initialization_disabled(self, mock_db_session):
        """Test initialization with evolution intelligence disabled."""
        with patch.dict(os.environ, {"EVOLUTION_INTELLIGENCE_ENABLED": "false"}):
            integration = MemoryEvolutionIntegration(db_session=mock_db_session)
            
            assert integration.feature_flag_enabled is False
            assert integration.evolution_service is None
            assert integration.evolution_timeout == 30.0  # default
    
    def test_initialization_service_error(self, mock_db_session):
        """Test initialization with evolution service initialization error."""
        with patch.dict(os.environ, {"EVOLUTION_INTELLIGENCE_ENABLED": "true"}):
            with patch('app.evolution.memory_integration.EvolutionService') as mock_service_class:
                mock_service_class.side_effect = Exception("Service init error")
                
                integration = MemoryEvolutionIntegration(db_session=mock_db_session)
                
                assert integration.feature_flag_enabled is False
                assert integration.evolution_service is None
    
    @pytest.mark.asyncio
    async def test_enhance_memory_operation_disabled(self, integration_disabled):
        """Test enhance_memory_operation when evolution is disabled."""
        result = await integration_disabled.enhance_memory_operation(
            text="Test text",
            user_id=str(uuid.uuid4()),
            client_name="test_client"
        )
        
        evolution_processed, evolution_result = result
        assert evolution_processed is False
        assert evolution_result["status"] == "disabled"
        assert evolution_result["reason"] == "feature_flag_disabled"
    
    @pytest.mark.asyncio
    async def test_enhance_memory_operation_success(self, integration_enabled):
        """Test successful memory enhancement."""
        # Mock the evolution service
        mock_evolution_result = Mock()
        mock_evolution_result.metrics.to_dict.return_value = {
            "total_operations": 3,
            "add_operations": 1,
            "update_operations": 1,
            "delete_operations": 0,
            "noop_operations": 1,
            "conflicts_detected": 0,
            "learning_efficiency": 0.33,
            "processing_time": 2.5
        }
        mock_evolution_result.operations = [
            {
                "operation_type": "ADD",
                "candidate_fact": "Test fact 1",
                "confidence_score": 0.9
            },
            {
                "operation_type": "UPDATE", 
                "candidate_fact": "Test fact 2",
                "confidence_score": 0.8
            }
        ]
        
        integration_enabled.evolution_service.process_memory_evolution = AsyncMock(return_value=mock_evolution_result)
        
        user_id = str(uuid.uuid4())
        client_name = "test_client"
        
        result = await integration_enabled.enhance_memory_operation(
            text="Test text",
            user_id=user_id,
            client_name=client_name,
            existing_memories=[{"id": "1", "content": "existing"}],
            context={"test": "context"}
        )
        
        evolution_processed, evolution_result = result
        assert evolution_processed is True
        assert evolution_result["status"] == "success"
        assert "evolution_result" in evolution_result
        assert "processing_time" in evolution_result
        
        # Verify evolution service was called correctly
        integration_enabled.evolution_service.process_memory_evolution.assert_called_once_with(
            text="Test text",
            user_id=user_id,
            client_name=client_name,
            existing_memories=[{"id": "1", "content": "existing"}],
            context={"test": "context"}
        )
    
    @pytest.mark.asyncio
    async def test_enhance_memory_operation_timeout(self, integration_enabled):
        """Test memory enhancement with timeout."""
        # Make evolution service timeout
        integration_enabled.evolution_service.process_memory_evolution = AsyncMock(
            side_effect=asyncio.TimeoutError("Evolution timeout")
        )
        integration_enabled.evolution_timeout = 0.1  # Very short timeout
        
        result = await integration_enabled.enhance_memory_operation(
            text="Test text",
            user_id=str(uuid.uuid4()),
            client_name="test_client"
        )
        
        evolution_processed, evolution_result = result
        assert evolution_processed is False
        assert evolution_result["status"] == "timeout"
        assert evolution_result["reason"] == "evolution_timeout"
        assert "processing_time" in evolution_result
    
    @pytest.mark.asyncio
    async def test_enhance_memory_operation_error(self, integration_enabled):
        """Test memory enhancement with error."""
        # Make evolution service raise error
        integration_enabled.evolution_service.process_memory_evolution = AsyncMock(
            side_effect=Exception("Evolution processing error")
        )
        
        result = await integration_enabled.enhance_memory_operation(
            text="Test text",
            user_id=str(uuid.uuid4()),
            client_name="test_client"
        )
        
        evolution_processed, evolution_result = result
        assert evolution_processed is False
        assert evolution_result["status"] == "error"
        assert evolution_result["reason"] == "evolution_error"
        assert "error" in evolution_result
        assert "processing_time" in evolution_result
    
    def test_log_evolution_metrics(self, integration_enabled, caplog):
        """Test logging of evolution metrics."""
        # Mock evolution result with metrics
        mock_evolution_result = Mock()
        mock_evolution_result.metrics.to_dict.return_value = {
            "total_operations": 5,
            "add_operations": 2,
            "update_operations": 1,
            "delete_operations": 1,
            "noop_operations": 1,
            "conflicts_detected": 2,
            "learning_efficiency": 0.4,
            "processing_time": 3.2
        }
        mock_evolution_result.operations = [
            {
                "operation_type": "ADD",
                "candidate_fact": "This is a test fact that is longer than 50 characters",
                "confidence_score": 0.95
            },
            {
                "operation_type": "UPDATE",
                "candidate_fact": "Short fact",
                "confidence_score": 0.85
            }
        ]
        
        user_id = str(uuid.uuid4())
        client_name = "test_client"
        
        with caplog.at_level(logging.INFO):
            integration_enabled._log_evolution_metrics(mock_evolution_result, user_id, client_name)
        
        # Check main metrics log
        assert "EVOLUTION_METRICS:" in caplog.text
        assert f"user={user_id}" in caplog.text
        assert f"client={client_name}" in caplog.text
        assert "total_ops=5" in caplog.text
        assert "efficiency=0.400" in caplog.text
        
        # Check individual operations debug logs
        assert "EVOLUTION_OP_1:" in caplog.text
        assert "EVOLUTION_OP_2:" in caplog.text
    
    @pytest.mark.asyncio
    async def test_get_user_memories_for_context_success(self, integration_enabled):
        """Test retrieving user memories for context."""
        user_id = str(uuid.uuid4())
        client_name = "test_client"
        
        # Mock database models and queries
        mock_user = Mock()
        mock_user.id = 1
        
        mock_app = Mock()
        mock_app.id = 2
        
        mock_memory1 = Mock()
        mock_memory1.id = uuid.uuid4()
        mock_memory1.content = "Memory content 1"
        mock_memory1.metadata_ = {"test": "metadata1"}
        mock_memory1.created_at = datetime.datetime.now(datetime.UTC)
        
        mock_memory2 = Mock()
        mock_memory2.id = uuid.uuid4()
        mock_memory2.content = "Memory content 2"
        mock_memory2.metadata_ = None
        mock_memory2.created_at = None
        
        # Setup query chain
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.first.side_effect = [mock_user, mock_app]  # User then App
        mock_query.all.return_value = [mock_memory1, mock_memory2]
        
        integration_enabled.db_session.query.return_value = mock_query
        
        with patch.dict('sys.modules', {'app.models': Mock(), 'app.database': Mock()}):
            result = await integration_enabled.get_user_memories_for_context(
                user_id=user_id,
                client_name=client_name,
                limit=50
            )
            
            assert len(result) == 2
            
            # Check first memory
            memory1 = result[0]
            assert str(memory1["id"]) == str(mock_memory1.id)
            assert memory1["content"] == "Memory content 1"
            assert memory1["metadata"] == {"test": "metadata1"}
            assert memory1["created_at"] is not None
            
            # Check second memory (with None values)
            memory2 = result[1]
            assert str(memory2["id"]) == str(mock_memory2.id)
            assert memory2["content"] == "Memory content 2"
            assert memory2["metadata"] == {}
            assert memory2["created_at"] is None
    
    @pytest.mark.asyncio
    async def test_get_user_memories_for_context_no_user(self, integration_enabled):
        """Test retrieving memories when user not found."""
        user_id = str(uuid.uuid4())
        client_name = "test_client"
        
        # Mock user not found
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        integration_enabled.db_session.query.return_value = mock_query
        
        with patch.dict('sys.modules', {'app.models': Mock(), 'app.database': Mock()}):
            result = await integration_enabled.get_user_memories_for_context(
                user_id=user_id,
                client_name=client_name
            )
            
            assert result == []
    
    @pytest.mark.asyncio
    async def test_get_user_memories_for_context_no_db_session(self):
        """Test retrieving memories without database session."""
        integration = MemoryEvolutionIntegration(db_session=None)
        
        result = await integration.get_user_memories_for_context(
            user_id=str(uuid.uuid4()),
            client_name="test_client"
        )
        
        assert result == []
    
    def test_is_evolution_enabled(self, integration_enabled, integration_disabled):
        """Test checking if evolution is enabled."""
        assert integration_enabled.is_evolution_enabled() is True
        assert integration_disabled.is_evolution_enabled() is False
    
    @pytest.mark.asyncio
    async def test_get_evolution_statistics(self, integration_enabled):
        """Test getting evolution statistics."""
        user_id = str(uuid.uuid4())
        expected_stats = {"total_operations": 100, "learning_efficiency": 0.75}
        
        integration_enabled.evolution_service.get_evolution_statistics = AsyncMock(
            return_value=expected_stats
        )
        
        result = await integration_enabled.get_evolution_statistics(user_id=user_id, days=30)
        
        assert result == expected_stats
        integration_enabled.evolution_service.get_evolution_statistics.assert_called_once_with(user_id, 30)
    
    @pytest.mark.asyncio
    async def test_get_evolution_statistics_no_service(self, integration_disabled):
        """Test getting evolution statistics without service."""
        result = await integration_disabled.get_evolution_statistics(
            user_id=str(uuid.uuid4()),
            days=30
        )
        
        assert result == {"error": "Evolution service not available"}
    
    @pytest.mark.asyncio
    async def test_health_check_healthy(self, integration_enabled):
        """Test health check when service is healthy."""
        mock_evolution_health = {
            "service": "evolution_service",
            "status": "healthy"
        }
        
        integration_enabled.evolution_service.health_check = AsyncMock(return_value=mock_evolution_health)
        
        result = await integration_enabled.health_check()
        
        assert result["service"] == "memory_evolution_integration"
        assert result["status"] == "healthy"
        assert result["feature_flag_enabled"] is True
        assert result["evolution_timeout"] == 30.0
        assert result["evolution_service"] == mock_evolution_health
    
    @pytest.mark.asyncio
    async def test_health_check_unhealthy_evolution_service(self, integration_enabled):
        """Test health check when evolution service is unhealthy."""
        mock_evolution_health = {
            "service": "evolution_service",
            "status": "unhealthy"
        }
        
        integration_enabled.evolution_service.health_check = AsyncMock(return_value=mock_evolution_health)
        
        result = await integration_enabled.health_check()
        
        assert result["status"] == "unhealthy"
        assert result["evolution_service"] == mock_evolution_health
    
    @pytest.mark.asyncio
    async def test_health_check_evolution_service_error(self, integration_enabled):
        """Test health check when evolution service raises error."""
        integration_enabled.evolution_service.health_check = AsyncMock(
            side_effect=Exception("Health check error")
        )
        
        result = await integration_enabled.health_check()
        
        assert result["status"] == "unhealthy"
        assert "evolution_service_error" in result
        assert result["evolution_service_error"] == "Health check error"
    
    @pytest.mark.asyncio
    async def test_health_check_disabled(self, integration_disabled):
        """Test health check when evolution is disabled."""
        result = await integration_disabled.health_check()
        
        assert result["service"] == "memory_evolution_integration"
        assert result["status"] == "healthy"
        assert result["feature_flag_enabled"] is False
        assert result["evolution_service"] == "disabled"


class TestMemoryEvolutionIntegrationGlobalFunctions:
    """Test module-level global functions."""
    
    @patch('app.evolution.memory_integration._memory_evolution_integration', None)
    def test_get_memory_evolution_integration_singleton(self):
        """Test get_memory_evolution_integration creates singleton instance."""
        mock_session = Mock()
        
        with patch.dict(os.environ, {"EVOLUTION_INTELLIGENCE_ENABLED": "false"}):
            # First call creates instance
            integration1 = get_memory_evolution_integration(mock_session)
            assert integration1 is not None
            assert isinstance(integration1, MemoryEvolutionIntegration)
            
            # Second call returns same instance
            integration2 = get_memory_evolution_integration(mock_session)
            assert integration1 is integration2
    
    @pytest.mark.asyncio
    async def test_enhance_add_memories_with_existing_memories(self):
        """Test enhance_add_memories convenience function with existing memories."""
        mock_session = Mock()
        existing_memories = [{"id": "1", "content": "existing"}]
        
        with patch('app.evolution.memory_integration.get_memory_evolution_integration') as mock_get_integration:
            mock_integration = Mock()
            mock_integration.is_evolution_enabled.return_value = True
            mock_integration.enhance_memory_operation = AsyncMock(return_value=(True, {"status": "success"}))
            mock_get_integration.return_value = mock_integration
            
            result = await enhance_add_memories(
                text="Test text",
                user_id=str(uuid.uuid4()),
                client_name="test_client",
                db_session=mock_session,
                existing_memories=existing_memories,
                context={"test": "context"}
            )
            
            evolution_processed, evolution_result = result
            assert evolution_processed is True
            assert evolution_result["status"] == "success"
            
            # Verify enhance_memory_operation was called with existing memories
            mock_integration.enhance_memory_operation.assert_called_once()
            call_args = mock_integration.enhance_memory_operation.call_args
            assert call_args[1]["existing_memories"] == existing_memories
    
    @pytest.mark.asyncio
    async def test_enhance_add_memories_fetch_memories(self):
        """Test enhance_add_memories fetches memories when not provided."""
        mock_session = Mock()
        user_id = str(uuid.uuid4())
        client_name = "test_client"
        
        with patch('app.evolution.memory_integration.get_memory_evolution_integration') as mock_get_integration:
            mock_integration = Mock()
            mock_integration.is_evolution_enabled.return_value = True
            mock_integration.get_user_memories_for_context = AsyncMock(
                return_value=[{"id": "fetched", "content": "memory"}]
            )
            mock_integration.enhance_memory_operation = AsyncMock(return_value=(False, {"status": "disabled"}))
            mock_get_integration.return_value = mock_integration
            
            result = await enhance_add_memories(
                text="Test text",
                user_id=user_id,
                client_name=client_name,
                db_session=mock_session,
                existing_memories=None,
                context=None
            )
            
            # Verify memories were fetched
            mock_integration.get_user_memories_for_context.assert_called_once_with(user_id, client_name)
            
            # Verify enhance_memory_operation was called with fetched memories
            call_args = mock_integration.enhance_memory_operation.call_args
            assert call_args[1]["existing_memories"] == [{"id": "fetched", "content": "memory"}]
    
    @pytest.mark.asyncio
    async def test_enhance_add_memories_evolution_disabled(self):
        """Test enhance_add_memories when evolution is disabled."""
        mock_session = Mock()
        
        with patch('app.evolution.memory_integration.get_memory_evolution_integration') as mock_get_integration:
            mock_integration = Mock()
            mock_integration.is_evolution_enabled.return_value = False
            mock_integration.enhance_memory_operation = AsyncMock(return_value=(False, {"status": "disabled"}))
            mock_get_integration.return_value = mock_integration
            
            result = await enhance_add_memories(
                text="Test text",
                user_id=str(uuid.uuid4()),
                client_name="test_client",
                db_session=mock_session
            )
            
            # Should not try to fetch memories if evolution is disabled
            mock_integration.get_user_memories_for_context.assert_not_called()
            
            evolution_processed, evolution_result = result
            assert evolution_processed is False
            assert evolution_result["status"] == "disabled"


# Integration test markers for future implementation
@pytest.mark.integration
class TestMemoryEvolutionIntegrationIntegration:
    """Integration tests for MemoryEvolutionIntegration with real components."""
    
    @pytest.mark.skip(reason="Integration test - requires full system setup")
    async def test_full_memory_enhancement_workflow_integration(self):
        """Test complete memory enhancement workflow with real components."""
        pass
    
    @pytest.mark.skip(reason="Integration test - requires database setup")
    async def test_memory_context_retrieval_integration(self):
        """Test memory context retrieval with real database."""
        pass


# Performance test markers for future implementation
@pytest.mark.performance
class TestMemoryEvolutionIntegrationPerformance:
    """Performance tests for MemoryEvolutionIntegration."""
    
    @pytest.mark.skip(reason="Performance test - requires specialized setup")
    async def test_memory_enhancement_performance(self):
        """Test memory enhancement performance with large datasets."""
        pass
    
    @pytest.mark.skip(reason="Performance test - requires specialized setup")
    async def test_context_retrieval_performance(self):
        """Test memory context retrieval performance."""
        pass