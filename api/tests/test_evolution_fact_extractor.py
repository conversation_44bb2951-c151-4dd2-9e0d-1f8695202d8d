"""
Unit tests for the Fact Extractor module.

Tests the LLM-based fact extraction functionality including:
- Fact extraction from technical conversations
- Context handling and processing
- Response parsing and validation
- Error handling and circuit breaker integration
- Performance and timeout scenarios
"""

import pytest
import json
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime

from app.evolution.fact_extractor import (
    FactExtractor, 
    ExtractedFact, 
    FactExtractionResponse, 
    ExtractionContext
)
from pydantic import ValidationError


class TestExtractionContext:
    """Test the ExtractionContext dataclass."""
    
    def test_extraction_context_creation(self):
        """Test basic context creation."""
        context = ExtractionContext(
            latest_message="How do I set up TypeScript with React?",
            recent_messages=["I want to build a web app", "What's the best frontend framework?"]
        )
        
        assert context.latest_message == "How do I set up TypeScript with React?"
        assert len(context.recent_messages) == 2
        assert context.user_background is None
    
    def test_extraction_context_with_background(self):
        """Test context creation with user background."""
        context = ExtractionContext(
            latest_message="I prefer functional programming",
            recent_messages=["Let's discuss code style"],
            user_background="Senior developer with 10 years experience"
        )
        
        assert context.user_background == "Senior developer with 10 years experience"


class TestExtractedFact:
    """Test the ExtractedFact Pydantic model."""
    
    def test_valid_fact_creation(self):
        """Test creating a valid extracted fact."""
        fact = ExtractedFact(
            fact="Prefers TypeScript over JavaScript for large projects",
            category="technical_preference",
            confidence=0.9,
            relevance_score=0.85
        )
        
        assert fact.fact == "Prefers TypeScript over JavaScript for large projects"
        assert fact.category == "technical_preference"
        assert fact.confidence == 0.9
        assert fact.relevance_score == 0.85
    
    def test_fact_validation_errors(self):
        """Test validation errors for invalid fact data."""
        # Invalid confidence (> 1.0)
        with pytest.raises(ValidationError):
            ExtractedFact(
                fact="Some fact",
                category="technical_preference",
                confidence=1.5,
                relevance_score=0.8
            )
        
        # Invalid relevance score (< 0.0)
        with pytest.raises(ValidationError):
            ExtractedFact(
                fact="Some fact", 
                category="technical_preference",
                confidence=0.9,
                relevance_score=-0.1
            )
    
    def test_fact_serialization(self):
        """Test fact serialization to JSON."""
        fact = ExtractedFact(
            fact="Uses Docker for containerisation",
            category="methodology",
            confidence=0.8,
            relevance_score=0.75
        )
        
        fact_dict = fact.model_dump()
        assert fact_dict["fact"] == "Uses Docker for containerisation"
        assert fact_dict["category"] == "methodology"
        assert fact_dict["confidence"] == 0.8
        assert fact_dict["relevance_score"] == 0.75


class TestFactExtractionResponse:
    """Test the FactExtractionResponse model."""
    
    def test_response_creation(self):
        """Test creating a valid fact extraction response."""
        facts = [
            ExtractedFact(
                fact="Prefers VS Code for development",
                category="technical_preference", 
                confidence=0.9,
                relevance_score=0.8
            )
        ]
        
        response = FactExtractionResponse(
            facts=facts,
            processing_time_ms=150.5,
            model_used="claude-sonnet-4"
        )
        
        assert len(response.facts) == 1
        assert response.processing_time_ms == 150.5
        assert response.model_used == "claude-sonnet-4"
    
    def test_empty_facts_response(self):
        """Test response with no facts extracted."""
        response = FactExtractionResponse(
            facts=[],
            processing_time_ms=75.2,
            model_used="claude-sonnet-4"
        )
        
        assert len(response.facts) == 0
        assert response.processing_time_ms == 75.2


@pytest.fixture
def mock_llm_pool_manager():
    """Mock LLM pool manager."""
    manager = Mock()
    manager.get_pool.return_value = AsyncMock()
    return manager


@pytest.fixture
def mock_circuit_breaker_manager():
    """Mock circuit breaker manager."""
    manager = Mock()
    manager.get_breaker.return_value = AsyncMock()
    return manager


@pytest.fixture
def fact_extractor():
    """Create a FactExtractor instance for testing."""
    with patch('app.evolution.fact_extractor.get_settings') as mock_settings:
        mock_settings.return_value.anthropic_api_key = "test-key"
        mock_settings.return_value.openai_api_key = "test-key"
        return FactExtractor()


class TestFactExtractor:
    """Test the FactExtractor class."""
    
    def test_initialization_with_api_keys(self):
        """Test extractor initialization with API keys."""
        with patch('app.evolution.fact_extractor.get_settings') as mock_settings:
            mock_settings.return_value.anthropic_api_key = "test-anthropic-key"
            mock_settings.return_value.openai_api_key = "test-openai-key"
            
            extractor = FactExtractor(
                anthropic_api_key="custom-anthropic",
                openai_api_key="custom-openai"
            )
            
            assert extractor.anthropic_api_key == "custom-anthropic"
            assert extractor.openai_api_key == "custom-openai"
    
    def test_initialization_from_settings(self):
        """Test extractor initialization from settings."""
        with patch('app.evolution.fact_extractor.get_settings') as mock_settings:
            mock_settings.return_value.anthropic_api_key = "settings-anthropic-key"
            mock_settings.return_value.openai_api_key = "settings-openai-key"
            
            extractor = FactExtractor()
            
            assert extractor.anthropic_api_key == "settings-anthropic-key"
            assert extractor.openai_api_key == "settings-openai-key"
    
    @pytest.mark.asyncio
    async def test_extract_facts_success(self, fact_extractor):
        """Test successful fact extraction."""
        # Mock LLM response
        mock_response = {
            "facts": [
                {
                    "fact": "Prefers React for frontend development",
                    "category": "technical_preference",
                    "confidence": 0.9,
                    "relevance_score": 0.85
                },
                {
                    "fact": "Working on an e-commerce project",
                    "category": "project_decision", 
                    "confidence": 0.8,
                    "relevance_score": 0.9
                }
            ]
        }
        
        context = ExtractionContext(
            latest_message="I'm building an e-commerce site with React",
            recent_messages=["What's the best frontend framework?"]
        )
        
        with patch.object(fact_extractor, '_call_llm_api', new_callable=AsyncMock) as mock_call:
            mock_call.return_value = json.dumps(mock_response)
            
            result = await fact_extractor.extract_facts(context)
            
            assert isinstance(result, FactExtractionResponse)
            assert len(result.facts) == 2
            assert result.facts[0].fact == "Prefers React for frontend development"
            assert result.facts[0].category == "technical_preference"
            assert result.facts[1].category == "project_decision"
            assert result.processing_time_ms > 0
    
    @pytest.mark.asyncio
    async def test_extract_facts_empty_response(self, fact_extractor):
        """Test fact extraction with no facts found."""
        mock_response = {"facts": []}
        
        context = ExtractionContext(
            latest_message="Hello, how are you?",
            recent_messages=[]
        )
        
        with patch.object(fact_extractor, '_call_llm_api', new_callable=AsyncMock) as mock_call:
            mock_call.return_value = json.dumps(mock_response)
            
            result = await fact_extractor.extract_facts(context)
            
            assert isinstance(result, FactExtractionResponse)
            assert len(result.facts) == 0
    
    @pytest.mark.asyncio
    async def test_extract_facts_invalid_json(self, fact_extractor):
        """Test handling of invalid JSON response."""
        context = ExtractionContext(
            latest_message="Test message",
            recent_messages=[]
        )
        
        with patch.object(fact_extractor, '_call_llm_api', new_callable=AsyncMock) as mock_call:
            mock_call.return_value = "Invalid JSON response"
            
            with pytest.raises(json.JSONDecodeError):
                await fact_extractor.extract_facts(context)
    
    @pytest.mark.asyncio
    async def test_extract_facts_validation_error(self, fact_extractor):
        """Test handling of validation errors in response."""
        # Response with invalid confidence score
        mock_response = {
            "facts": [
                {
                    "fact": "Some fact",
                    "category": "technical_preference",
                    "confidence": 1.5,  # Invalid: > 1.0
                    "relevance_score": 0.8
                }
            ]
        }
        
        context = ExtractionContext(
            latest_message="Test message",
            recent_messages=[]
        )
        
        with patch.object(fact_extractor, '_call_llm_api', new_callable=AsyncMock) as mock_call:
            mock_call.return_value = json.dumps(mock_response)
            
            with pytest.raises(ValidationError):
                await fact_extractor.extract_facts(context)
    
    @pytest.mark.asyncio
    async def test_extract_facts_circuit_breaker_open(self, fact_extractor):
        """Test behavior when circuit breaker is open."""
        context = ExtractionContext(
            latest_message="Test message",
            recent_messages=[]
        )
        
        with patch('app.evolution.fact_extractor.get_circuit_breaker_manager') as mock_cb_manager:
            from aiobreaker import CircuitBreakerError
            mock_cb_manager.return_value.get_breaker.return_value.__aenter__.side_effect = CircuitBreakerError()
            
            with pytest.raises(CircuitBreakerError):
                await fact_extractor.extract_facts(context)
    
    def test_format_prompt(self, fact_extractor):
        """Test prompt formatting with context."""
        context = ExtractionContext(
            latest_message="I prefer TypeScript",
            recent_messages=["Let's discuss languages", "What about static typing?"],
            user_background="Senior developer"
        )
        
        prompt = fact_extractor._format_prompt(context)
        
        assert "I prefer TypeScript" in prompt
        assert "Let's discuss languages" in prompt
        assert "Senior developer" in prompt
        assert "technical_preference" in prompt  # Categories should be included
    
    def test_format_prompt_no_background(self, fact_extractor):
        """Test prompt formatting without user background."""
        context = ExtractionContext(
            latest_message="I prefer TypeScript",
            recent_messages=["Let's discuss languages"]
        )
        
        prompt = fact_extractor._format_prompt(context)
        
        assert "I prefer TypeScript" in prompt
        assert "Let's discuss languages" in prompt
        assert "None" in prompt or "Not provided" in prompt  # Background should be handled gracefully


@pytest.mark.integration
class TestFactExtractorIntegration:
    """Integration tests for FactExtractor."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_fact_extraction(self):
        """Test end-to-end fact extraction with mock LLM."""
        # This would test the full flow with mocked external dependencies
        # but real internal logic
        pass
    
    @pytest.mark.asyncio 
    async def test_concurrent_fact_extraction(self):
        """Test concurrent fact extraction requests."""
        # Test multiple simultaneous extraction requests
        pass


@pytest.mark.slow
class TestFactExtractorPerformance:
    """Performance tests for FactExtractor."""
    
    @pytest.mark.asyncio
    async def test_extraction_timeout_handling(self):
        """Test that extraction handles timeouts properly."""
        pass
    
    @pytest.mark.asyncio
    async def test_large_context_processing(self):
        """Test processing of large conversation contexts."""
        pass