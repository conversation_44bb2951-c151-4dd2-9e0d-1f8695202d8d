"""
Integration tests for Evolution Intelligence System.

Tests the complete evolution workflow integration including:
- End-to-end evolution pipeline with real data flow
- Integration between fact extraction and similarity analysis
- Decision engine integration with memory operations
- Evolution tracking and insights aggregation
- MCP server evolution tool integration
- Performance validation under realistic loads
"""

import pytest
import asyncio
import uuid
import time
import json
from unittest.mock import patch, Mock, AsyncMock
from datetime import date, datetime
from typing import List, Dict, Any

from app.evolution.evolution_service import EvolutionService, EvolutionConfig
from app.evolution.fact_extractor import FactExtractor, ExtractedFact
from app.evolution.similarity_analyzer import Similar<PERSON><PERSON><PERSON><PERSON><PERSON>, SimilarityR<PERSON>ult
from app.evolution.decision_engine import EvolutionDecisionE<PERSON>ine, EvolutionOperation
from app.evolution.evolution_tracker import EvolutionTracker
from app.evolution.insights_aggregator import EvolutionInsightsAggregator
from app.evolution.memory_integration import MemoryEvolutionIntegration
# MCP server functions are imported individually for testing
from app.utils.memory import MemoryClientSingleton


class TestEvolutionWorkflowIntegration:
    """Test complete evolution workflow integration."""
    
    @pytest.fixture
    def evolution_config(self):
        """Create test evolution configuration."""
        return EvolutionConfig(
            enable_evolution=True,
            evolution_timeout=30.0,
            similarity_threshold=0.7,
            confidence_threshold=0.6,
            max_facts_per_text=5
        )
    
    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        session = Mock()
        session.commit = Mock()
        session.rollback = Mock()
        session.close = Mock()
        return session
    
    @pytest.fixture
    def evolution_service(self, evolution_config, mock_db_session):
        """Create evolution service for testing."""
        return EvolutionService(config=evolution_config, db_session=mock_db_session)
    
    @pytest.fixture
    def sample_technical_conversations(self):
        """Sample technical conversation data for testing."""
        return [
            {
                "text": "I'm working with React hooks and found that useState can cause performance issues if not used properly. The key is to avoid creating objects in the dependency array.",
                "user_id": "dev_user_1",
                "client_name": "vscode",
                "expected_facts": ["React hooks performance", "useState optimization", "dependency array best practices"]
            },
            {
                "text": "Just learned about Python's asyncio library. The async/await syntax makes concurrent programming much easier compared to threading. Make sure to use asyncio.gather for parallel execution.",
                "user_id": "dev_user_2", 
                "client_name": "cursor",
                "expected_facts": ["Python asyncio", "async/await syntax", "asyncio.gather for parallel execution"]
            },
            {
                "text": "Database indexing is crucial for query performance. B-tree indexes work well for range queries, while hash indexes are better for equality checks. Always analyze your query patterns first.",
                "user_id": "dev_user_3",
                "client_name": "claude",
                "expected_facts": ["Database indexing performance", "B-tree vs hash indexes", "Query pattern analysis"]
            }
        ]
    
    @pytest.mark.asyncio
    async def test_end_to_end_evolution_workflow(self, evolution_service, sample_technical_conversations):
        """Test complete end-to-end evolution workflow."""
        conversation = sample_technical_conversations[0]
        
        # Mock existing memories for similarity analysis
        existing_memories = [
            {
                "id": "mem_1",
                "text": "React performance optimization techniques",
                "user_id": conversation["user_id"],
                "embedding": [0.1] * 1536
            },
            {
                "id": "mem_2", 
                "text": "JavaScript state management patterns",
                "user_id": conversation["user_id"],
                "embedding": [0.2] * 1536
            }
        ]
        
        # Mock component responses
        mock_facts = [
            ExtractedFact(
                fact="React hooks performance optimization",
                confidence=0.9,
                category="technical_preference",
                relevance_score=0.85
            ),
            ExtractedFact(
                fact="useState dependency array best practices",
                confidence=0.85,
                category="technical_preference",
                relevance_score=0.8
            )
        ]
        
        mock_similarities = [
            SimilarityResult(
                memory_id="mem_1",
                content="React performance optimization techniques",
                similarity_score=0.8,
                metadata={"type": "semantic_match"}
            )
        ]
        
        # Mock the components to control workflow
        with patch.object(evolution_service.fact_extractor, 'extract_facts', new_callable=AsyncMock) as mock_extract:
            with patch.object(evolution_service.similarity_analyzer, 'find_similar_memories', new_callable=AsyncMock) as mock_analyze:
                with patch.object(evolution_service.decision_engine, 'decide_operation', new_callable=AsyncMock) as mock_decide:
                    
                    mock_extract.return_value = mock_facts
                    mock_analyze.return_value = mock_similarities
                    mock_decide.return_value = Mock(operation=EvolutionOperation.UPDATE, confidence=0.85, target_memory_id="mem_1")
                    
                    # Execute end-to-end workflow
                    result = await evolution_service.process_memory_evolution(
                        text=conversation["text"],
                        user_id=conversation["user_id"],
                        client_name=conversation["client_name"],
                        existing_memories=existing_memories
                    )
                    
                    # Verify workflow execution
                    assert result.success is True
                    assert len(result.operations) > 0
                    assert result.processing_time > 0
                    
                    # Verify component integration
                    mock_extract.assert_called_once()
                    assert mock_analyze.call_count == 2  # Called once for each fact
                    assert mock_decide.call_count == 2   # Called once for each fact
                    
                    # Verify data flow between components
                    extract_call_args = mock_extract.call_args
                    assert conversation["text"] in str(extract_call_args)

                    # Verify that facts were passed to similarity analyzer
                    # Check that each fact was passed individually to find_similar_memories
                    all_calls = mock_analyze.call_args_list
                    assert len(all_calls) == 2
                    # Check positional arguments (candidate_fact is the first argument)
                    assert all_calls[0][0][0] == mock_facts[0].fact  # First fact
                    assert all_calls[1][0][0] == mock_facts[1].fact  # Second fact
                    
                    # Verify decision engine was called with individual facts
                    decide_calls = mock_decide.call_args_list
                    assert len(decide_calls) == 2
    
    @pytest.mark.asyncio
    async def test_evolution_pipeline_with_database_integration(self, evolution_service, mock_db_session):
        """Test evolution pipeline with real database operations."""
        
        # Mock successful database operations
        mock_db_session.execute.return_value.fetchall.return_value = []
        mock_db_session.commit.return_value = None
        
        # Mock evolution tracker for database operations
        with patch('app.evolution.evolution_tracker.EvolutionTracker') as mock_tracker_class:
            mock_tracker = Mock()
            mock_tracker.track_operation = AsyncMock()
            mock_tracker_class.return_value = mock_tracker
            
            # Execute workflow with database integration
            result = await evolution_service.process_memory_evolution(
                text="Python async programming best practices",
                user_id="test_user",
                client_name="test_client"
            )
            
            # Verify database session usage
            assert mock_db_session.execute.called or mock_db_session.commit.called
            assert result.success is True
    
    @pytest.mark.asyncio
    async def test_concurrent_evolution_processing(self, evolution_service, sample_technical_conversations):
        """Test concurrent evolution processing for multiple users."""
        
        # Mock fast responses for concurrent testing
        with patch.object(evolution_service.fact_extractor, 'extract_facts', new_callable=AsyncMock) as mock_extract:
            with patch.object(evolution_service.similarity_analyzer, 'find_similar_memories', new_callable=AsyncMock) as mock_analyze:
                with patch.object(evolution_service.decision_engine, 'decide_operation', new_callable=AsyncMock) as mock_decide:
                    
                    # Configure fast mock responses
                    mock_extract.return_value = []
                    mock_analyze.return_value = []
                    mock_decide.return_value = Mock(operation=EvolutionOperation.NOOP, confidence=0.7)
                    
                    # Create concurrent tasks
                    tasks = []
                    for conversation in sample_technical_conversations:
                        task = evolution_service.process_memory_evolution(
                            text=conversation["text"],
                            user_id=conversation["user_id"],
                            client_name=conversation["client_name"]
                        )
                        tasks.append(task)
                    
                    # Execute concurrently
                    start_time = time.time()
                    results = await asyncio.gather(*tasks)
                    end_time = time.time()
                    
                    # Verify all succeeded
                    assert all(result.success for result in results)
                    assert len(results) == len(sample_technical_conversations)
                    
                    # Verify reasonable performance (concurrent should be faster than sequential)
                    total_time = end_time - start_time
                    assert total_time < 5.0  # Should complete within 5 seconds
    
    @pytest.mark.asyncio
    async def test_evolution_error_handling_and_fallback(self, evolution_service):
        """Test evolution error handling and fallback mechanisms."""
        
        # Test fact extraction failure
        with patch.object(evolution_service.fact_extractor, 'extract_facts', side_effect=Exception("LLM API error")):
            result = await evolution_service.process_memory_evolution(
                text="Test input for error handling",
                user_id="test_user",
                client_name="test_client"
            )
            
            assert result.success is True  # Should fallback gracefully
            assert result.fallback_mode is True
            assert len(result.errors) > 0
            assert "LLM API error" in str(result.errors)
        
        # Test similarity analysis failure
        with patch.object(evolution_service.fact_extractor, 'extract_facts', new_callable=AsyncMock) as mock_extract:
            with patch.object(evolution_service.similarity_analyzer, 'find_similar_memories', side_effect=Exception("Vector DB error")):
                mock_extract.return_value = [Mock()]  # Fact extraction succeeds
                
                result = await evolution_service.process_memory_evolution(
                    text="Test input for similarity error",
                    user_id="test_user", 
                    client_name="test_client"
                )
                
                assert result.success is True
                assert result.fallback_mode is True
                assert "Vector DB error" in str(result.errors)
    
    @pytest.mark.asyncio
    async def test_evolution_with_memory_operations_integration(self, evolution_service):
        """Test evolution integration with actual memory operations."""
        
        # Mock memory client operations
        with patch.object(MemoryClientSingleton, 'get_instance') as mock_get_instance:
            mock_memory_client = Mock()
            mock_memory_client.search_memories = AsyncMock(return_value=[])
            mock_memory_client.add_memory = AsyncMock(return_value={"id": str(uuid.uuid4())})
            mock_memory_client.update_memory = AsyncMock(return_value={"success": True})
            mock_get_instance.return_value = mock_memory_client
            
            # Mock evolution components to trigger memory operations
            with patch.object(evolution_service.fact_extractor, 'extract_facts', new_callable=AsyncMock) as mock_extract:
                with patch.object(evolution_service.decision_engine, 'decide_operation', new_callable=AsyncMock) as mock_decide:
                    
                    mock_extract.return_value = [
                        ExtractedFact(
                            fact="New programming insight",
                            confidence=0.9,
                            category="learning_goal",
                            relevance_score=0.85
                        )
                    ]
                    
                    mock_decide.return_value = Mock(operation=EvolutionOperation.ADD, confidence=0.9, target_memory_id=None)
                    
                    result = await evolution_service.process_memory_evolution(
                        text="I discovered a new programming technique",
                        user_id="test_user",
                        client_name="test_client"
                    )
                    
                    assert result.success is True
                    assert len(result.operations) > 0
                    assert result.operations[0]["operation"] == "ADD"


class TestEvolutionMCPIntegration:
    """Test evolution MCP server integration."""
    
    @pytest.fixture
    def mock_mcp_server(self):
        """Create mock MCP server."""
        return Mock()
    
    @pytest.mark.asyncio
    async def test_add_memories_with_evolution(self, mock_mcp_server):
        """Test add_memories MCP tool with evolution integration."""
        
        # Mock the enhanced add_memories function
        with patch('app.mcp_server.add_memories_with_evolution') as mock_add_memories:
            mock_add_memories.return_value = {
                "success": True,
                "memories_added": 2,
                "evolution_operations": [
                    {"operation": "UPDATE", "confidence": 0.8},
                    {"operation": "ADD", "confidence": 0.9}
                ],
                "processing_time": 1.5
            }
            
            # Simulate MCP tool call
            result = await mock_add_memories(
                text="New technical knowledge to add",
                user_id="mcp_user",
                client_name="cursor"
            )
            
            assert result["success"] is True
            assert result["memories_added"] == 2
            assert len(result["evolution_operations"]) == 2
            assert result["processing_time"] > 0
    
    @pytest.mark.asyncio  
    async def test_get_evolution_metrics_mcp_tool(self, mock_mcp_server):
        """Test get_evolution_metrics MCP tool."""
        
        with patch('app.mcp_server.get_evolution_metrics') as mock_get_metrics:
            mock_get_metrics.return_value = {
                "total_operations": 150,
                "learning_efficiency": 0.65,
                "average_confidence": 0.78,
                "operations_by_type": {
                    "ADD": 60,
                    "UPDATE": 70, 
                    "DELETE": 15,
                    "NOOP": 5
                },
                "trend_analysis": {
                    "efficiency_trend": 0.1,
                    "confidence_trend": 0.05
                }
            }
            
            result = await mock_get_metrics(user_id="mcp_user", days=30)
            
            assert result["total_operations"] == 150
            assert result["learning_efficiency"] == 0.65
            assert "trend_analysis" in result
    
    @pytest.mark.asyncio
    async def test_get_learning_insights_mcp_tool(self, mock_mcp_server):
        """Test get_learning_insights MCP tool."""
        
        with patch('app.mcp_server.get_learning_insights') as mock_get_insights:
            mock_get_insights.return_value = {
                "user_patterns": {
                    "most_common_topics": ["react", "python", "databases"],
                    "learning_velocity": 0.75,
                    "knowledge_retention": 0.85
                },
                "recommendations": [
                    "Focus on database optimization patterns",
                    "Explore advanced React hooks"
                ],
                "quality_score": 0.82
            }
            
            result = await mock_get_insights(user_id="mcp_user")
            
            assert "user_patterns" in result
            assert "recommendations" in result  
            assert result["quality_score"] == 0.82


class TestEvolutionSystemPerformance:
    """Test evolution system performance under load."""
    
    @pytest.mark.asyncio
    async def test_evolution_processing_performance(self, evolution_service):
        """Test evolution processing performance benchmarks."""
        
        # Mock fast components for performance testing
        with patch.object(evolution_service.fact_extractor, 'extract_facts', new_callable=AsyncMock) as mock_extract:
            with patch.object(evolution_service.similarity_analyzer, 'find_similar_memories', new_callable=AsyncMock) as mock_analyze:
                with patch.object(evolution_service.decision_engine, 'decide_operation', new_callable=AsyncMock) as mock_decide:
                    
                    # Configure realistic response times
                    async def mock_extract_with_delay(*args, **kwargs):
                        await asyncio.sleep(0.1)  # Simulate LLM API call
                        return [Mock()]
                    
                    async def mock_analyze_with_delay(*args, **kwargs):
                        await asyncio.sleep(0.05)  # Simulate vector similarity
                        return [Mock()]
                    
                    async def mock_decide_with_delay(*args, **kwargs):
                        await asyncio.sleep(0.02)  # Simulate decision logic
                        return Mock(operation=EvolutionOperation.ADD, confidence=0.8)
                    
                    mock_extract.side_effect = mock_extract_with_delay
                    mock_analyze.side_effect = mock_analyze_with_delay
                    mock_decide.side_effect = mock_decide_with_delay
                    
                    # Measure processing time
                    start_time = time.time()
                    result = await evolution_service.process_memory_evolution(
                        text="Performance test input with technical content",
                        user_id="perf_user",
                        client_name="test_client"
                    )
                    end_time = time.time()
                    
                    processing_time = end_time - start_time
                    
                    # Verify performance requirements
                    assert result.success is True
                    assert processing_time < 1.0  # Should complete within 1 second
                    assert result.processing_time > 0
    
    @pytest.mark.asyncio
    async def test_batch_evolution_processing(self, evolution_service):
        """Test batch processing multiple evolution requests."""
        
        batch_size = 10
        conversations = [
            f"Technical insight number {i} about programming best practices"
            for i in range(batch_size)
        ]
        
        # Mock fast responses
        with patch.object(evolution_service.fact_extractor, 'extract_facts', new_callable=AsyncMock) as mock_extract:
            mock_extract.return_value = [Mock()]
            
            # Process batch
            start_time = time.time()
            tasks = [
                evolution_service.process_memory_evolution(
                    text=text,
                    user_id=f"batch_user_{i}",
                    client_name="batch_client"
                )
                for i, text in enumerate(conversations)
            ]
            
            results = await asyncio.gather(*tasks)
            end_time = time.time()
            
            # Verify batch performance
            total_time = end_time - start_time
            avg_time_per_request = total_time / batch_size
            
            assert all(result.success for result in results)
            assert len(results) == batch_size
            assert total_time < 10.0  # Batch should complete within 10 seconds
            assert avg_time_per_request < 1.0  # Average per request should be under 1 second


class TestEvolutionDataFlowIntegration:
    """Test data flow integration across evolution components."""
    
    @pytest.mark.asyncio
    async def test_fact_to_similarity_data_flow(self):
        """Test data flow from fact extraction to similarity analysis."""
        
        # Create real components for integration testing
        fact_extractor = FactExtractor()
        similarity_analyzer = SimilarityAnalyzer()
        
        # Mock LLM response for fact extraction
        mock_llm_response = {
            "facts": [
                {
                    "fact": "React useEffect cleanup prevents memory leaks",
                    "confidence": 0.9,
                    "category": "technical_preference",
                    "relevance_score": 0.85
                }
            ]
        }
        
        with patch.object(fact_extractor, '_call_llm', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value = json.dumps(mock_llm_response)
            
            # Extract facts
            facts = await fact_extractor.extract_facts("React useEffect cleanup patterns")
            
            # Verify facts can be passed to similarity analyzer
            assert len(facts) > 0
            assert hasattr(facts[0], 'fact')
            assert hasattr(facts[0], 'confidence')
            
            # Mock similarity analysis with extracted facts
            with patch.object(similarity_analyzer, '_compute_embeddings', new_callable=AsyncMock) as mock_embeddings:
                mock_embeddings.return_value = [[0.1] * 1536]  # Mock embedding
                
                # Test similarity analysis with real fact data
                similarities = await similarity_analyzer.find_similar_memories(
                    candidate_fact=facts[0].fact,
                    existing_memories=[
                        {"id": "mem1", "text": "React hooks best practices", "embedding": [0.2] * 1536}
                    ]
                )
                
                # Verify similarity analysis works with fact data
                assert isinstance(similarities, list)
    
    @pytest.mark.asyncio
    async def test_similarity_to_decision_data_flow(self):
        """Test data flow from similarity analysis to decision engine."""
        
        # Create test data that flows through the pipeline
        extracted_facts = [
            ExtractedFact(
                fact="Database indexing improves query performance",
                confidence=0.85,
                category="technical_preference",
                relevance_score=0.8
            )
        ]
        
        similarity_results = [
            SimilarityResult(
                memory_id="existing_mem_1",
                content="Database performance optimization techniques",
                similarity_score=0.7,
                metadata={"match_type": "semantic"}
            )
        ]
        
        # Test decision engine with real similarity data
        decision_engine = EvolutionDecisionEngine()
        
        with patch.object(decision_engine, '_analyze_relationship', new_callable=AsyncMock) as mock_analyze:
            mock_analyze.return_value = Mock(operation=EvolutionOperation.UPDATE, confidence=0.8)

            decision = await decision_engine.decide_operation(
                candidate_fact=extracted_facts[0].fact,
                similar_memories=similarity_results
            )
            
            # Verify decision engine processes similarity data correctly
            assert decision is not None
            assert hasattr(decision, 'operation')
            assert hasattr(decision, 'confidence')
            assert decision.confidence >= 0.6