"""
Unit tests for the Similarity Analyzer module.

Tests the semantic similarity analysis functionality including:
- Vector embedding generation and comparison
- Memory record processing and similarity scoring
- FAISS integration for efficient similarity search
- Caching of embeddings and similarity results
- Performance optimization and error handling
"""

import pytest
import numpy as np
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import List, Dict, Any

from app.evolution.similarity_analyzer import (
    SimilarityAnalyzer,
    MemoryRecord,
    SimilarityResult
)


class TestMemoryRecord:
    """Test the MemoryRecord dataclass."""
    
    def test_memory_record_creation(self):
        """Test basic memory record creation."""
        record = MemoryRecord(
            id="mem_123",
            content="User prefers React for frontend development"
        )
        
        assert record.id == "mem_123"
        assert record.content == "User prefers React for frontend development"
        assert record.embedding is None
        assert record.metadata is None
    
    def test_memory_record_with_embedding(self):
        """Test memory record with embedding."""
        embedding = np.array([0.1, 0.2, 0.3, 0.4])
        metadata = {"category": "technical_preference", "confidence": 0.9}
        
        record = MemoryRecord(
            id="mem_456",
            content="Uses TypeScript for large projects",
            embedding=embedding,
            metadata=metadata
        )
        
        assert record.id == "mem_456"
        assert np.array_equal(record.embedding, embedding)
        assert record.metadata["category"] == "technical_preference"
        assert record.metadata["confidence"] == 0.9


class TestSimilarityResult:
    """Test the SimilarityResult dataclass."""
    
    def test_similarity_result_creation(self):
        """Test basic similarity result creation."""
        result = SimilarityResult(
            memory_id="mem_789",
            content="Prefers Vue over React",
            similarity_score=0.85
        )
        
        assert result.memory_id == "mem_789"
        assert result.content == "Prefers Vue over React"
        assert result.similarity_score == 0.85
        assert result.metadata is None
    
    def test_similarity_result_with_metadata(self):
        """Test similarity result with metadata."""
        metadata = {"last_updated": "2024-01-01", "source": "conversation"}
        
        result = SimilarityResult(
            memory_id="mem_101",
            content="Uses Docker for development",
            similarity_score=0.92,
            metadata=metadata
        )
        
        assert result.metadata["last_updated"] == "2024-01-01"
        assert result.metadata["source"] == "conversation"


@pytest.fixture
def sample_memories():
    """Sample memory records for testing."""
    return [
        MemoryRecord(
            id="mem_1",
            content="Prefers TypeScript over JavaScript for large projects",
            metadata={"category": "technical_preference"}
        ),
        MemoryRecord(
            id="mem_2", 
            content="Working on an e-commerce platform using React",
            metadata={"category": "project_decision"}
        ),
        MemoryRecord(
            id="mem_3",
            content="Uses Docker for containerisation in development",
            metadata={"category": "methodology"}
        )
    ]


@pytest.fixture
def sample_embeddings():
    """Sample embeddings for testing."""
    return {
        "mem_1": np.array([0.1, 0.2, 0.3, 0.4, 0.5]),
        "mem_2": np.array([0.2, 0.3, 0.4, 0.5, 0.6]),
        "mem_3": np.array([0.8, 0.7, 0.6, 0.5, 0.4])
    }


@pytest.fixture
def similarity_analyzer():
    """Create a SimilarityAnalyzer instance for testing."""
    with patch('app.evolution.similarity_analyzer.get_llm_pool_manager'), \
         patch('app.evolution.similarity_analyzer.get_circuit_breaker_manager'):
        return SimilarityAnalyzer(
            openai_api_key="test-key",
            anthropic_api_key="test-key",
            similarity_threshold=0.85
        )


class TestSimilarityAnalyzer:
    """Test the SimilarityAnalyzer class."""
    
    def test_initialization_default_params(self):
        """Test analyzer initialization with default parameters."""
        with patch('app.evolution.similarity_analyzer.get_llm_pool_manager'), \
             patch('app.evolution.similarity_analyzer.get_circuit_breaker_manager'):
            analyzer = SimilarityAnalyzer()
            
            assert analyzer.embedding_model == "text-embedding-3-large"
            assert analyzer.similarity_threshold == 0.85
            assert analyzer.use_faiss is True
    
    def test_initialization_custom_params(self):
        """Test analyzer initialization with custom parameters."""
        with patch('app.evolution.similarity_analyzer.get_llm_pool_manager'), \
             patch('app.evolution.similarity_analyzer.get_circuit_breaker_manager'):
            analyzer = SimilarityAnalyzer(
                embedding_model="text-embedding-ada-002",
                similarity_threshold=0.7,
                use_faiss=False
            )
            
            assert analyzer.embedding_model == "text-embedding-ada-002"
            assert analyzer.similarity_threshold == 0.7
            assert analyzer.use_faiss is False
    
    def test_cosine_similarity_calculation(self, similarity_analyzer):
        """Test cosine similarity calculation."""
        vec1 = np.array([1.0, 0.0, 0.0])
        vec2 = np.array([0.0, 1.0, 0.0])
        vec3 = np.array([1.0, 0.0, 0.0])
        
        # Test orthogonal vectors (should be 0)
        sim1 = similarity_analyzer._cosine_similarity(vec1, vec2)
        assert abs(sim1 - 0.0) < 1e-10
        
        # Test identical vectors (should be 1)
        sim2 = similarity_analyzer._cosine_similarity(vec1, vec3)
        assert abs(sim2 - 1.0) < 1e-10
        
        # Test partial similarity
        vec4 = np.array([0.5, 0.5, 0.0])
        sim3 = similarity_analyzer._cosine_similarity(vec1, vec4)
        assert 0 < sim3 < 1
    
    def test_cosine_similarity_edge_cases(self, similarity_analyzer):
        """Test cosine similarity with edge cases."""
        # Zero vectors
        zero_vec = np.array([0.0, 0.0, 0.0])
        normal_vec = np.array([1.0, 0.0, 0.0])
        
        sim = similarity_analyzer._cosine_similarity(zero_vec, normal_vec)
        assert np.isnan(sim) or sim == 0.0  # Should handle gracefully
    
    @pytest.mark.asyncio
    async def test_generate_embedding_success(self, similarity_analyzer):
        """Test successful embedding generation."""
        text = "User prefers TypeScript for development"
        expected_embedding = np.array([0.1, 0.2, 0.3, 0.4, 0.5])
        
        with patch.object(similarity_analyzer, '_call_openai_embedding', new_callable=AsyncMock) as mock_call:
            mock_call.return_value = expected_embedding
            
            result = await similarity_analyzer.generate_embedding(text)
            
            assert np.array_equal(result, expected_embedding)
            mock_call.assert_called_once_with(text)
    
    @pytest.mark.asyncio
    async def test_generate_embedding_caching(self, similarity_analyzer):
        """Test embedding caching functionality."""
        text = "User prefers TypeScript for development"
        expected_embedding = np.array([0.1, 0.2, 0.3, 0.4, 0.5])
        
        with patch('app.evolution.similarity_analyzer.cache_service') as mock_cache, \
             patch.object(similarity_analyzer, '_call_openai_embedding', new_callable=AsyncMock) as mock_call:
            
            # First call - cache miss
            mock_cache.get.return_value = None
            mock_call.return_value = expected_embedding
            
            result1 = await similarity_analyzer.generate_embedding(text)
            
            # Second call - cache hit
            mock_cache.get.return_value = expected_embedding.tolist()
            
            result2 = await similarity_analyzer.generate_embedding(text)
            
            # Should only call API once
            mock_call.assert_called_once()
            assert np.array_equal(result1, result2)
    
    @pytest.mark.asyncio
    async def test_compare_texts_success(self, similarity_analyzer):
        """Test successful text comparison."""
        text1 = "Prefers TypeScript over JavaScript"
        text2 = "Uses TypeScript for large projects"
        
        embedding1 = np.array([0.1, 0.2, 0.3])
        embedding2 = np.array([0.15, 0.25, 0.35])
        
        with patch.object(similarity_analyzer, 'generate_embedding', new_callable=AsyncMock) as mock_embed:
            mock_embed.side_effect = [embedding1, embedding2]
            
            similarity = await similarity_analyzer.compare_texts(text1, text2)
            
            assert 0.0 <= similarity <= 1.0
            assert mock_embed.call_count == 2
    
    @pytest.mark.asyncio
    async def test_find_similar_memories_success(self, similarity_analyzer, sample_memories):
        """Test finding similar memories."""
        query_text = "Uses JavaScript for frontend development"
        query_embedding = np.array([0.1, 0.2, 0.3, 0.4, 0.5])
        
        # Mock embeddings for memories
        memory_embeddings = {
            "mem_1": np.array([0.12, 0.22, 0.32, 0.42, 0.52]),  # High similarity
            "mem_2": np.array([0.5, 0.6, 0.7, 0.8, 0.9]),      # Low similarity
            "mem_3": np.array([0.8, 0.7, 0.6, 0.5, 0.4])       # Medium similarity
        }
        
        with patch.object(similarity_analyzer, 'generate_embedding', new_callable=AsyncMock) as mock_embed, \
             patch.object(similarity_analyzer, '_get_memory_embeddings', new_callable=AsyncMock) as mock_get_embeddings:
            
            mock_embed.return_value = query_embedding
            mock_get_embeddings.return_value = memory_embeddings
            
            results = await similarity_analyzer.find_similar_memories(query_text, sample_memories, top_k=2)
            
            assert len(results) <= 2
            assert all(isinstance(r, SimilarityResult) for r in results)
            # Results should be sorted by similarity (highest first)
            if len(results) > 1:
                assert results[0].similarity_score >= results[1].similarity_score
    
    @pytest.mark.asyncio
    async def test_find_similar_memories_threshold_filtering(self, similarity_analyzer, sample_memories):
        """Test similarity threshold filtering."""
        query_text = "Uses JavaScript for frontend development"
        query_embedding = np.array([0.1, 0.2, 0.3, 0.4, 0.5])
        
        # All embeddings have low similarity (below threshold)
        memory_embeddings = {
            "mem_1": np.array([0.9, 0.8, 0.7, 0.6, 0.5]),  # Low similarity
            "mem_2": np.array([0.8, 0.9, 0.6, 0.7, 0.4]),  # Low similarity
            "mem_3": np.array([0.7, 0.6, 0.9, 0.8, 0.3])   # Low similarity
        }
        
        with patch.object(similarity_analyzer, 'generate_embedding', new_callable=AsyncMock) as mock_embed, \
             patch.object(similarity_analyzer, '_get_memory_embeddings', new_callable=AsyncMock) as mock_get_embeddings:
            
            mock_embed.return_value = query_embedding
            mock_get_embeddings.return_value = memory_embeddings
            
            results = await similarity_analyzer.find_similar_memories(
                query_text, sample_memories, similarity_threshold=0.9
            )
            
            # Should return empty list as no memories meet the threshold
            assert len(results) == 0
    
    @pytest.mark.asyncio
    async def test_batch_similarity_analysis(self, similarity_analyzer, sample_memories):
        """Test batch similarity analysis."""
        query_texts = [
            "Prefers React for frontend",
            "Uses Docker for development"
        ]
        
        with patch.object(similarity_analyzer, 'find_similar_memories', new_callable=AsyncMock) as mock_find:
            mock_find.return_value = [
                SimilarityResult("mem_1", "Similar content", 0.9)
            ]
            
            results = await similarity_analyzer.batch_similarity_analysis(query_texts, sample_memories)
            
            assert len(results) == len(query_texts)
            assert mock_find.call_count == len(query_texts)


@pytest.mark.integration
class TestSimilarityAnalyzerIntegration:
    """Integration tests for SimilarityAnalyzer."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_similarity_analysis(self):
        """Test end-to-end similarity analysis with mocked embeddings."""
        # Test full similarity analysis pipeline
        pass
    
    @pytest.mark.asyncio
    async def test_faiss_integration(self):
        """Test FAISS integration for large-scale similarity search."""
        # Test FAISS index creation and search
        pass


@pytest.mark.slow
class TestSimilarityAnalyzerPerformance:
    """Performance tests for SimilarityAnalyzer."""
    
    @pytest.mark.asyncio
    async def test_large_memory_set_performance(self):
        """Test performance with large sets of memories."""
        # Test handling of thousands of memories
        pass
    
    @pytest.mark.asyncio
    async def test_embedding_caching_performance(self):
        """Test that caching improves performance."""
        # Test performance improvement from caching
        pass
    
    @pytest.mark.asyncio
    async def test_concurrent_similarity_requests(self):
        """Test concurrent similarity analysis requests."""
        # Test multiple simultaneous similarity analysis requests
        pass


class TestSimilarityAnalyzerErrorHandling:
    """Test error handling in SimilarityAnalyzer."""
    
    @pytest.mark.asyncio
    async def test_embedding_api_failure(self, similarity_analyzer):
        """Test handling of embedding API failures."""
        text = "Test text"
        
        with patch.object(similarity_analyzer, '_call_openai_embedding', new_callable=AsyncMock) as mock_call:
            from aiobreaker import CircuitBreakerError
            mock_call.side_effect = CircuitBreakerError()
            
            with pytest.raises(CircuitBreakerError):
                await similarity_analyzer.generate_embedding(text)
    
    @pytest.mark.asyncio
    async def test_invalid_embedding_dimensions(self, similarity_analyzer):
        """Test handling of mismatched embedding dimensions."""
        vec1 = np.array([0.1, 0.2, 0.3])
        vec2 = np.array([0.1, 0.2])  # Different dimension
        
        # Should handle gracefully
        result = similarity_analyzer._cosine_similarity(vec1, vec2)
        assert result is not None  # Should not crash
    
    @pytest.mark.asyncio
    async def test_empty_memory_list(self, similarity_analyzer):
        """Test handling of empty memory list."""
        query_text = "Test query"
        empty_memories = []
        
        results = await similarity_analyzer.find_similar_memories(query_text, empty_memories)
        
        assert len(results) == 0
        assert isinstance(results, list)