"""
Unit tests for Evolution Tracker module.

Tests the comprehensive tracking and logging system for evolution operations
with audit trail capabilities, batch processing, and data retention policies.
"""

import pytest
import asyncio
import uuid
import datetime
from unittest.mock import Mock, MagicMock, patch, AsyncMock
from typing import List, Dict, Any

# Import test subjects
from app.evolution.evolution_tracker import (
    EvolutionTracker,
    EvolutionTrackingEntry
)


class TestEvolutionTrackingEntry:
    """Test EvolutionTrackingEntry dataclass."""
    
    def test_evolution_tracking_entry_creation(self):
        """Test creating an EvolutionTrackingEntry."""
        operation_id = str(uuid.uuid4())
        memory_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())
        timestamp = datetime.datetime.now(datetime.UTC)
        metadata = {"test": "data"}
        
        entry = EvolutionTrackingEntry(
            operation_id=operation_id,
            memory_id=memory_id,
            user_id=user_id,
            operation_type="ADD",
            candidate_fact="Test fact",
            existing_memory_content="Existing content",
            similarity_score=0.85,
            confidence_score=0.9,
            reasoning="Test reasoning",
            metadata=metadata,
            timestamp=timestamp
        )
        
        assert entry.operation_id == operation_id
        assert entry.memory_id == memory_id
        assert entry.user_id == user_id
        assert entry.operation_type == "ADD"
        assert entry.candidate_fact == "Test fact"
        assert entry.existing_memory_content == "Existing content"
        assert entry.similarity_score == 0.85
        assert entry.confidence_score == 0.9
        assert entry.reasoning == "Test reasoning"
        assert entry.metadata == metadata
        assert entry.timestamp == timestamp
    
    def test_evolution_tracking_entry_optional_fields(self):
        """Test EvolutionTrackingEntry with optional fields as None."""
        operation_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())
        timestamp = datetime.datetime.now(datetime.UTC)
        
        entry = EvolutionTrackingEntry(
            operation_id=operation_id,
            memory_id=None,
            user_id=user_id,
            operation_type="NOOP",
            candidate_fact="Test fact",
            existing_memory_content=None,
            similarity_score=None,
            confidence_score=None,
            reasoning=None,
            metadata={},
            timestamp=timestamp
        )
        
        assert entry.memory_id is None
        assert entry.existing_memory_content is None
        assert entry.similarity_score is None
        assert entry.confidence_score is None
        assert entry.reasoning is None
        assert entry.metadata == {}


class TestEvolutionTracker:
    """Test EvolutionTracker class."""
    
    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = Mock()
        session.bulk_save_objects = Mock()
        session.commit = Mock()
        session.rollback = Mock()
        session.query = Mock()
        session.execute = Mock()
        return session
    
    @pytest.fixture
    def tracker(self, mock_db_session):
        """Create EvolutionTracker instance for testing."""
        return EvolutionTracker(db_session=mock_db_session, batch_size=5)
    
    def test_tracker_initialization(self, mock_db_session):
        """Test EvolutionTracker initialization."""
        tracker = EvolutionTracker(db_session=mock_db_session, batch_size=10)
        
        assert tracker.db_session == mock_db_session
        assert tracker.batch_size == 10
        assert tracker.pending_entries == []
        assert tracker.tracking_enabled is True
    
    @pytest.mark.asyncio
    async def test_track_operation_success(self, tracker):
        """Test successful operation tracking."""
        operation_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())
        memory_id = str(uuid.uuid4())
        
        result = await tracker.track_operation(
            operation_id=operation_id,
            user_id=user_id,
            operation_type="ADD",
            candidate_fact="Test fact",
            memory_id=memory_id,
            similarity_score=0.8,
            confidence_score=0.9,
            reasoning="Test reasoning",
            metadata={"test": "metadata"}
        )
        
        assert result is True
        assert len(tracker.pending_entries) == 1
        
        entry = tracker.pending_entries[0]
        assert entry.operation_id == operation_id
        assert entry.user_id == user_id
        assert entry.operation_type == "ADD"
        assert entry.candidate_fact == "Test fact"
        assert entry.memory_id == memory_id
        assert entry.similarity_score == 0.8
        assert entry.confidence_score == 0.9
        assert entry.reasoning == "Test reasoning"
        assert entry.metadata == {"test": "metadata"}
    
    @pytest.mark.asyncio
    async def test_track_operation_disabled(self, tracker):
        """Test operation tracking when disabled."""
        tracker.tracking_enabled = False
        
        result = await tracker.track_operation(
            operation_id=str(uuid.uuid4()),
            user_id=str(uuid.uuid4()),
            operation_type="ADD",
            candidate_fact="Test fact"
        )
        
        assert result is False
        assert len(tracker.pending_entries) == 0
    
    @pytest.mark.asyncio
    async def test_track_operation_with_batch_flush(self, tracker):
        """Test operation tracking triggers batch flush."""
        tracker.flush_batch = AsyncMock(return_value=True)
        
        # Add operations to reach batch size (5)
        for i in range(5):
            await tracker.track_operation(
                operation_id=str(uuid.uuid4()),
                user_id=str(uuid.uuid4()),
                operation_type="ADD",
                candidate_fact=f"Test fact {i}"
            )
        
        # Should trigger flush on 5th operation
        tracker.flush_batch.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_track_operation_error_handling(self, tracker):
        """Test operation tracking error handling."""
        # Force an error by passing invalid data
        with patch('app.evolution.evolution_tracker.EvolutionTrackingEntry') as mock_entry:
            mock_entry.side_effect = Exception("Test error")
            
            result = await tracker.track_operation(
                operation_id=str(uuid.uuid4()),
                user_id=str(uuid.uuid4()),
                operation_type="ADD",
                candidate_fact="Test fact"
            )
            
            assert result is False
            assert len(tracker.pending_entries) == 0
    
    @pytest.mark.asyncio
    async def test_track_batch_operations_success(self, tracker):
        """Test batch operation tracking."""
        operations = [
            {
                "id": str(uuid.uuid4()),
                "user_id": str(uuid.uuid4()),
                "operation_type": "ADD",
                "candidate_fact": "Fact 1",
                "similarity_score": 0.8
            },
            {
                "id": str(uuid.uuid4()),
                "user_id": str(uuid.uuid4()),
                "operation_type": "UPDATE",
                "candidate_fact": "Fact 2",
                "confidence_score": 0.9
            }
        ]
        
        tracker.flush_batch = AsyncMock(return_value=True)
        
        result = await tracker.track_batch_operations(operations)
        
        assert result == 2
        assert len(tracker.pending_entries) == 2
        tracker.flush_batch.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_track_operations_batch_with_decisions(self, tracker):
        """Test tracking batch of EvolutionDecision objects."""
        # Mock EvolutionDecision objects
        decision1 = Mock()
        decision1.operation.value = "ADD"
        decision1.candidate_fact = "Test fact 1"
        decision1.target_memory_id = str(uuid.uuid4())
        decision1.confidence = 0.9
        decision1.reasoning = "Test reasoning 1"
        decision1.conflicts = []
        decision1.suggested_changes = ["change1"]
        
        decision2 = Mock()
        decision2.operation.value = "UPDATE"
        decision2.candidate_fact = "Test fact 2"
        decision2.target_memory_id = None
        decision2.confidence = 0.8
        decision2.reasoning = "Test reasoning 2"
        decision2.conflicts = ["conflict1"]
        decision2.suggested_changes = []
        
        decisions = [decision1, decision2]
        user_id = str(uuid.uuid4())
        
        tracker.flush_batch = AsyncMock(return_value=True)
        
        result = await tracker.track_operations_batch(decisions, user_id)
        
        assert result == 2
        assert len(tracker.pending_entries) == 2
        tracker.flush_batch.assert_called_once()
        
        # Check metadata was properly extracted
        entry1 = tracker.pending_entries[0]
        assert entry1.metadata['conflicts_detected'] is False
        assert entry1.metadata['conflict_count'] == 0
        assert entry1.metadata['suggested_changes_count'] == 1
        
        entry2 = tracker.pending_entries[1]
        assert entry2.metadata['conflicts_detected'] is True
        assert entry2.metadata['conflict_count'] == 1
        assert entry2.metadata['suggested_changes_count'] == 0
    
    @pytest.mark.asyncio
    async def test_flush_batch_success(self, tracker):
        """Test successful batch flush."""
        # Add some entries
        for i in range(3):
            await tracker.track_operation(
                operation_id=str(uuid.uuid4()),
                user_id=str(uuid.uuid4()),
                operation_type="ADD",
                candidate_fact=f"Test fact {i}"
            )
        
        # Mock the models import and database operations
        with patch('app.evolution.evolution_tracker.uuid.UUID') as mock_uuid, \
             patch.dict('sys.modules', {'app.models': Mock()}):
            
            mock_uuid.side_effect = lambda x: x  # Return input as-is
            
            # Mock EvolutionOperation and EvolutionOperationType
            mock_models = Mock()
            mock_models.EvolutionOperation = Mock()
            mock_models.EvolutionOperationType = Mock(side_effect=lambda x: x)
            
            with patch.dict('sys.modules', {'app.models': mock_models}):
                result = await tracker.flush_batch()
                
                assert result is True
                assert len(tracker.pending_entries) == 0
                tracker.db_session.bulk_save_objects.assert_called_once()
                tracker.db_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_flush_batch_empty(self, tracker):
        """Test flush batch with no pending entries."""
        result = await tracker.flush_batch()
        
        assert result is True
        tracker.db_session.bulk_save_objects.assert_not_called()
        tracker.db_session.commit.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_flush_batch_error(self, tracker):
        """Test flush batch error handling."""
        # Add an entry
        await tracker.track_operation(
            operation_id=str(uuid.uuid4()),
            user_id=str(uuid.uuid4()),
            operation_type="ADD",
            candidate_fact="Test fact"
        )
        
        # Mock database error
        tracker.db_session.bulk_save_objects.side_effect = Exception("Database error")
        
        with patch.dict('sys.modules', {'app.models': Mock()}):
            result = await tracker.flush_batch()
            
            assert result is False
            tracker.db_session.rollback.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_operation_audit_trail(self, tracker):
        """Test retrieving operation audit trail."""
        user_id = str(uuid.uuid4())
        start_date = datetime.datetime.now(datetime.UTC) - datetime.timedelta(days=7)
        end_date = datetime.datetime.now(datetime.UTC)
        
        # Mock database response
        mock_operation = Mock()
        mock_operation.id = uuid.uuid4()
        mock_operation.memory_id = uuid.uuid4()
        mock_operation.operation_type.value = "ADD"
        mock_operation.candidate_fact = "Test fact"
        mock_operation.existing_memory_content = "Existing content"
        mock_operation.similarity_score = 0.8
        mock_operation.confidence_score = 0.9
        mock_operation.reasoning = "Test reasoning"
        mock_operation.created_at = datetime.datetime.now(datetime.UTC)
        mock_operation.metadata_ = {"test": "metadata"}
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [mock_operation]
        
        tracker.db_session.query.return_value = mock_query
        
        with patch.dict('sys.modules', {'app.models': Mock()}):
            result = await tracker.get_operation_audit_trail(
                user_id=user_id,
                start_date=start_date,
                end_date=end_date,
                operation_types=["ADD", "UPDATE"],
                limit=50
            )
            
            assert len(result) == 1
            audit_entry = result[0]
            assert audit_entry["operation_type"] == "ADD"
            assert audit_entry["candidate_fact"] == "Test fact"
            assert audit_entry["similarity_score"] == 0.8
            assert audit_entry["confidence_score"] == 0.9
            assert audit_entry["metadata"] == {"test": "metadata"}
    
    @pytest.mark.asyncio
    async def test_get_operation_statistics(self, tracker):
        """Test getting operation statistics."""
        user_id = str(uuid.uuid4())
        
        # Mock database response for statistics query
        mock_stat = Mock()
        mock_stat.operation_type.value = "ADD"
        mock_stat.count = 10
        mock_stat.avg_confidence = 0.85
        mock_stat.avg_similarity = 0.75
        
        tracker.db_session.execute.return_value.fetchall.return_value = [mock_stat]
        
        with patch.dict('sys.modules', {'app.models': Mock()}):
            result = await tracker.get_operation_statistics(user_id=user_id, days=30)
            
            assert isinstance(result, dict)
            assert "total_operations" in result
            assert "operation_counts" in result
            assert "learning_efficiency" in result
            assert "average_confidence" in result
            assert "metrics" in result
            
            assert result["total_operations"] == 10
            assert result["operation_counts"]["ADD"] == 10
            assert result["average_confidence"] == 0.85
    
    @pytest.mark.asyncio
    async def test_cleanup_old_operations(self, tracker):
        """Test cleaning up old operations."""
        retention_days = 90
        
        # Mock count query
        mock_count_query = Mock()
        mock_count_query.scalar.return_value = 100
        tracker.db_session.query.return_value.filter.return_value = mock_count_query
        
        # Mock batch deletion
        mock_ids_query = Mock()
        mock_ids_query.limit.return_value.all.side_effect = [
            [(uuid.uuid4(),)] * 10,  # First batch
            [(uuid.uuid4(),)] * 10,  # Second batch  
            []  # No more results
        ]
        tracker.db_session.query.return_value.filter.return_value.limit = mock_ids_query.limit
        
        mock_delete_query = Mock()
        mock_delete_query.delete.side_effect = [10, 10, 0]  # Batch delete counts
        tracker.db_session.query.return_value.filter.return_value = mock_delete_query
        
        with patch.dict('sys.modules', {'app.models': Mock()}):
            result = await tracker.cleanup_old_operations(
                retention_days=retention_days,
                batch_size=10
            )
            
            assert result == 20  # Total deleted
            assert tracker.db_session.commit.call_count >= 2
    
    @pytest.mark.asyncio
    async def test_export_operations_json(self, tracker):
        """Test exporting operations in JSON format."""
        user_id = str(uuid.uuid4())
        
        # Mock audit trail data
        mock_operations = [
            {
                "id": str(uuid.uuid4()),
                "operation_type": "ADD",
                "candidate_fact": "Test fact 1",
                "created_at": "2024-01-01T12:00:00"
            },
            {
                "id": str(uuid.uuid4()),
                "operation_type": "UPDATE", 
                "candidate_fact": "Test fact 2",
                "created_at": "2024-01-01T13:00:00"
            }
        ]
        
        tracker.get_operation_audit_trail = AsyncMock(return_value=mock_operations)
        
        result = await tracker.export_operations_for_analysis(
            user_id=user_id,
            format="json"
        )
        
        assert result is not None
        assert '"operations_count": 2' in result
        assert '"Test fact 1"' in result
        assert '"Test fact 2"' in result
    
    @pytest.mark.asyncio
    async def test_export_operations_csv(self, tracker):
        """Test exporting operations in CSV format."""
        user_id = str(uuid.uuid4())
        
        # Mock audit trail data
        mock_operations = [
            {
                "id": str(uuid.uuid4()),
                "memory_id": str(uuid.uuid4()),
                "operation_type": "ADD",
                "candidate_fact": "Test fact",
                "existing_memory_content": "Content",
                "similarity_score": "0.8",
                "confidence_score": "0.9",
                "reasoning": "Reasoning",
                "created_at": "2024-01-01T12:00:00"
            }
        ]
        
        tracker.get_operation_audit_trail = AsyncMock(return_value=mock_operations)
        
        result = await tracker.export_operations_for_analysis(
            user_id=user_id,
            format="csv"
        )
        
        assert result is not None
        assert "id,memory_id,operation_type" in result
        assert "Test fact" in result
        assert "ADD" in result
    
    def test_enable_disable_tracking(self, tracker):
        """Test enabling and disabling tracking."""
        # Test disable
        tracker.disable_tracking()
        assert tracker.tracking_enabled is False
        
        # Test enable
        tracker.enable_tracking()
        assert tracker.tracking_enabled is True
    
    @pytest.mark.asyncio
    async def test_health_check_healthy(self, tracker):
        """Test health check when service is healthy."""
        # Mock successful database query
        tracker.db_session.query.return_value.scalar.return_value = 100
        
        with patch.dict('sys.modules', {'app.models': Mock()}):
            result = await tracker.health_check()
            
            assert result["service"] == "evolution_tracker"
            assert result["status"] == "healthy"
            assert result["tracking_enabled"] is True
            assert result["batch_size"] == 5
            assert result["pending_entries"] == 0
            assert result["total_tracked_operations"] == 100
    
    @pytest.mark.asyncio
    async def test_health_check_unhealthy(self, tracker):
        """Test health check when service is unhealthy."""
        # Mock database error
        tracker.db_session.query.side_effect = Exception("Database error")
        
        with patch.dict('sys.modules', {'app.models': Mock()}):
            result = await tracker.health_check()
            
            assert result["service"] == "evolution_tracker"
            assert result["status"] == "unhealthy"
            assert "database_error" in result


class TestEvolutionTrackerGlobalFunctions:
    """Test module-level global functions."""
    
    @patch('app.evolution.evolution_tracker._evolution_tracker', None)
    def test_get_evolution_tracker_singleton(self):
        """Test get_evolution_tracker creates singleton instance."""
        mock_session = Mock()
        
        # First call creates instance
        tracker1 = get_evolution_tracker(mock_session)
        assert tracker1 is not None
        assert isinstance(tracker1, EvolutionTracker)
        
        # Second call returns same instance
        tracker2 = get_evolution_tracker(mock_session)
        assert tracker1 is tracker2


# Integration test markers for future implementation
@pytest.mark.integration
class TestEvolutionTrackerIntegration:
    """Integration tests for EvolutionTracker with real database."""
    
    @pytest.mark.skip(reason="Integration test - requires database setup")
    async def test_full_tracking_workflow_integration(self):
        """Test complete tracking workflow with real database."""
        pass
    
    @pytest.mark.skip(reason="Integration test - requires database setup")
    async def test_batch_processing_performance_integration(self):
        """Test batch processing performance with large datasets."""
        pass


# Performance test markers for future implementation
@pytest.mark.performance
class TestEvolutionTrackerPerformance:
    """Performance tests for EvolutionTracker."""
    
    @pytest.mark.skip(reason="Performance test - requires specialized setup")
    async def test_batch_flush_performance(self):
        """Test batch flush performance with large batches."""
        pass
    
    @pytest.mark.skip(reason="Performance test - requires specialized setup")
    async def test_audit_trail_query_performance(self):
        """Test audit trail query performance with large datasets."""
        pass