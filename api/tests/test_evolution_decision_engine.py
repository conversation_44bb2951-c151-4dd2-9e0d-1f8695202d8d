"""
Unit tests for the Evolution Decision Engine module.

Tests the decision-making logic for memory evolution including:
- Evolution operation determination (add, update, delete, noop)
- Conflict resolution strategies
- Confidence threshold processing
- Decision validation and error handling
- Integration with similarity analysis results
"""

import pytest
from unittest.mock import Mock, patch
from enum import Enum
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from app.evolution.decision_engine import (
    EvolutionDecisionEngine,
    EvolutionOperation,
    EvolutionDecision
)
from app.evolution.similarity_analyzer import SimilarityResult
from app.evolution.fact_extractor import ExtractedFact


class TestEvolutionOperation:
    """Test the EvolutionOperation enum."""
    
    def test_evolution_operation_values(self):
        """Test that all expected operation types exist."""
        assert EvolutionOperation.ADD.value == "add"
        assert EvolutionOperation.UPDATE.value == "update"
        assert EvolutionOperation.DELETE.value == "delete"
        assert EvolutionOperation.NOOP.value == "noop"
    
    def test_evolution_operation_string_representation(self):
        """Test string representation of operations."""
        assert str(EvolutionOperation.ADD) == "EvolutionOperation.ADD"
        assert EvolutionOperation.ADD.value == "add"


class TestEvolutionDecision:
    """Test the EvolutionDecision dataclass."""
    
    def test_decision_creation_add_operation(self):
        """Test creating an ADD decision."""
        decision = EvolutionDecision(
            operation=EvolutionOperation.ADD,
            confidence=0.9,
            reasoning="No similar memories found, adding new fact",
            target_memory_id=None,
            new_content="User prefers TypeScript for large projects"
        )
        
        assert decision.operation == EvolutionOperation.ADD
        assert decision.confidence == 0.9
        assert decision.target_memory_id is None
        assert decision.new_content == "User prefers TypeScript for large projects"
        assert "No similar memories found" in decision.reasoning
    
    def test_decision_creation_update_operation(self):
        """Test creating an UPDATE decision."""
        decision = EvolutionDecision(
            operation=EvolutionOperation.UPDATE,
            confidence=0.85,
            reasoning="Found conflicting information, updating existing memory",
            target_memory_id="mem_123",
            new_content="User now prefers React over Vue for frontend development"
        )
        
        assert decision.operation == EvolutionOperation.UPDATE
        assert decision.confidence == 0.85
        assert decision.target_memory_id == "mem_123"
        assert decision.new_content == "User now prefers React over Vue for frontend development"
    
    def test_decision_creation_delete_operation(self):
        """Test creating a DELETE decision."""
        decision = EvolutionDecision(
            operation=EvolutionOperation.DELETE,
            confidence=0.95,
            reasoning="Information is no longer relevant",
            target_memory_id="mem_456",
            new_content=None
        )
        
        assert decision.operation == EvolutionOperation.DELETE
        assert decision.confidence == 0.95
        assert decision.target_memory_id == "mem_456"
        assert decision.new_content is None
    
    def test_decision_creation_noop_operation(self):
        """Test creating a NOOP decision.""" 
        decision = EvolutionDecision(
            operation=EvolutionOperation.NOOP,
            confidence=0.7,
            reasoning="Information already exists and is current",
            target_memory_id=None,
            new_content=None
        )
        
        assert decision.operation == EvolutionOperation.NOOP
        assert decision.confidence == 0.7
        assert decision.target_memory_id is None
        assert decision.new_content is None


@pytest.fixture
def sample_extracted_fact():
    """Sample extracted fact for testing."""
    return ExtractedFact(
        fact="User prefers TypeScript over JavaScript for large projects",
        category="technical_preference",
        confidence=0.9,
        relevance_score=0.85
    )


@pytest.fixture
def sample_similarity_results():
    """Sample similarity results for testing."""
    return [
        SimilarityResult(
            memory_id="mem_1",
            content="User prefers JavaScript for small projects",
            similarity_score=0.75,
            metadata={"category": "technical_preference"}
        ),
        SimilarityResult(
            memory_id="mem_2",
            content="User likes TypeScript type safety features", 
            similarity_score=0.65,
            metadata={"category": "technical_preference"}
        )
    ]


@pytest.fixture
def decision_engine():
    """Create a EvolutionDecisionEngine instance for testing."""
    return EvolutionDecisionEngine(
        similarity_threshold=0.8,
        confidence_threshold=0.7,
        update_threshold=0.85
    )


class TestEvolutionDecisionEngine:
    """Test the EvolutionDecisionEngine class."""
    
    def test_initialization_default_params(self):
        """Test engine initialization with default parameters."""
        engine = EvolutionDecisionEngine()
        
        assert engine.similarity_threshold == 0.8
        assert engine.confidence_threshold == 0.7
        assert engine.update_threshold == 0.85
        assert engine.conflict_resolution_strategy == "confidence_based"
    
    def test_initialization_custom_params(self):
        """Test engine initialization with custom parameters."""
        engine = EvolutionDecisionEngine(
            similarity_threshold=0.75,
            confidence_threshold=0.6,
            update_threshold=0.9,
            conflict_resolution_strategy="recency_based"
        )
        
        assert engine.similarity_threshold == 0.75
        assert engine.confidence_threshold == 0.6
        assert engine.update_threshold == 0.9
        assert engine.conflict_resolution_strategy == "recency_based"
    
    def test_decide_add_operation_no_similar_memories(self, decision_engine, sample_extracted_fact):
        """Test ADD decision when no similar memories exist."""
        empty_similarities = []
        
        decision = decision_engine.decide(sample_extracted_fact, empty_similarities)
        
        assert decision.operation == EvolutionOperation.ADD
        assert decision.confidence == sample_extracted_fact.confidence
        assert decision.target_memory_id is None
        assert decision.new_content == sample_extracted_fact.fact
        assert "no similar memories" in decision.reasoning.lower()
    
    def test_decide_add_operation_low_similarity(self, decision_engine, sample_extracted_fact):
        """Test ADD decision when similarity is below threshold."""
        low_similarity_results = [
            SimilarityResult(
                memory_id="mem_1",
                content="User prefers Python for data science",
                similarity_score=0.3,  # Below threshold
                metadata={"category": "technical_preference"}
            )
        ]
        
        decision = decision_engine.decide(sample_extracted_fact, low_similarity_results)
        
        assert decision.operation == EvolutionOperation.ADD
        assert decision.confidence == sample_extracted_fact.confidence
        assert "below similarity threshold" in decision.reasoning.lower()
    
    def test_decide_noop_operation_exact_match(self, decision_engine, sample_extracted_fact):
        """Test NOOP decision when exact or very similar content exists."""
        high_similarity_results = [
            SimilarityResult(
                memory_id="mem_1",
                content="User prefers TypeScript over JavaScript for large projects",  # Exact match
                similarity_score=0.98,
                metadata={"category": "technical_preference"}
            )
        ]
        
        decision = decision_engine.decide(sample_extracted_fact, high_similarity_results)
        
        assert decision.operation == EvolutionOperation.NOOP
        assert decision.target_memory_id is None
        assert "already exists" in decision.reasoning.lower()
    
    def test_decide_update_operation_conflicting_info(self, decision_engine):
        """Test UPDATE decision when conflicting information is found."""
        conflicting_fact = ExtractedFact(
            fact="User now prefers React over Vue for frontend development",
            category="technical_preference",
            confidence=0.9,
            relevance_score=0.85
        )
        
        conflicting_similarities = [
            SimilarityResult(
                memory_id="mem_1",
                content="User prefers Vue over React for frontend development",  # Conflicting
                similarity_score=0.88,  # Above update threshold
                metadata={"category": "technical_preference"}
            )
        ]
        
        decision = decision_engine.decide(conflicting_fact, conflicting_similarities)
        
        assert decision.operation == EvolutionOperation.UPDATE
        assert decision.target_memory_id == "mem_1"
        assert decision.new_content == conflicting_fact.fact
        assert "conflicting" in decision.reasoning.lower() or "update" in decision.reasoning.lower()
    
    def test_decide_confidence_threshold_filtering(self, decision_engine):
        """Test that low confidence facts are handled appropriately."""
        low_confidence_fact = ExtractedFact(
            fact="User might prefer some frontend framework",
            category="technical_preference",
            confidence=0.3,  # Below confidence threshold
            relevance_score=0.5
        )
        
        empty_similarities = []
        
        decision = decision_engine.decide(low_confidence_fact, empty_similarities)
        
        # Should still make a decision but with lower confidence
        assert decision.confidence <= 0.3
        assert "low confidence" in decision.reasoning.lower() or decision.operation == EvolutionOperation.NOOP
    
    def test_decide_multiple_similar_memories(self, decision_engine, sample_extracted_fact):
        """Test decision making with multiple similar memories."""
        multiple_similarities = [
            SimilarityResult(
                memory_id="mem_1",
                content="User likes TypeScript type safety",
                similarity_score=0.75,
                metadata={"category": "technical_preference"}
            ),
            SimilarityResult(
                memory_id="mem_2", 
                content="User prefers static typing in large projects",
                similarity_score=0.82,
                metadata={"category": "technical_preference"}
            ),
            SimilarityResult(
                memory_id="mem_3",
                content="User avoids JavaScript for complex applications",
                similarity_score=0.78,
                metadata={"category": "technical_preference"}
            )
        ]
        
        decision = decision_engine.decide(sample_extracted_fact, multiple_similarities)
        
        # Should make a reasonable decision based on the multiple similar memories
        assert decision.operation in [EvolutionOperation.ADD, EvolutionOperation.UPDATE, EvolutionOperation.NOOP]
        assert isinstance(decision.confidence, float)
        assert 0.0 <= decision.confidence <= 1.0
    
    def test_batch_decision_processing(self, decision_engine):
        """Test processing multiple facts in batch."""
        facts = [
            ExtractedFact(
                fact="User prefers TypeScript",
                category="technical_preference",
                confidence=0.9,
                relevance_score=0.8
            ),
            ExtractedFact(
                fact="Working on e-commerce project",
                category="project_decision",
                confidence=0.85,
                relevance_score=0.9
            )
        ]
        
        similarities_per_fact = [
            [],  # No similarities for first fact
            [SimilarityResult("mem_1", "Building online store", 0.7)]  # Some similarity for second
        ]
        
        decisions = decision_engine.batch_decide(facts, similarities_per_fact)
        
        assert len(decisions) == len(facts)
        assert all(isinstance(d, EvolutionDecision) for d in decisions)
        assert decisions[0].operation == EvolutionOperation.ADD  # No similarities
        assert decisions[1].operation in [EvolutionOperation.ADD, EvolutionOperation.NOOP]  # Some similarity
    
    def test_conflict_detection(self, decision_engine):
        """Test conflict detection between facts and memories."""
        fact = ExtractedFact(
            fact="User prefers React for frontend",
            category="technical_preference", 
            confidence=0.9,
            relevance_score=0.85
        )
        
        conflicting_memory = SimilarityResult(
            memory_id="mem_1",
            content="User prefers Vue for frontend development",
            similarity_score=0.85,
            metadata={"category": "technical_preference"}
        )
        
        is_conflict = decision_engine._detect_conflict(fact, conflicting_memory)
        
        assert is_conflict is True
    
    def test_no_conflict_detection(self, decision_engine):
        """Test no conflict detection for compatible information."""
        fact = ExtractedFact(
            fact="User prefers TypeScript for type safety",
            category="technical_preference",
            confidence=0.9,
            relevance_score=0.85
        )
        
        compatible_memory = SimilarityResult(
            memory_id="mem_1", 
            content="User likes static typing features",
            similarity_score=0.75,
            metadata={"category": "technical_preference"}
        )
        
        is_conflict = decision_engine._detect_conflict(fact, compatible_memory)
        
        assert is_conflict is False
    
    def test_decision_validation(self, decision_engine):
        """Test decision validation."""
        # Valid ADD decision
        valid_add = EvolutionDecision(
            operation=EvolutionOperation.ADD,
            confidence=0.9,
            reasoning="Valid reasoning",
            target_memory_id=None,
            new_content="New content"
        )
        
        assert decision_engine._validate_decision(valid_add) is True
        
        # Invalid UPDATE decision (missing target_memory_id)
        invalid_update = EvolutionDecision(
            operation=EvolutionOperation.UPDATE,
            confidence=0.9,
            reasoning="Valid reasoning",
            target_memory_id=None,  # Should not be None for UPDATE
            new_content="New content"
        )
        
        assert decision_engine._validate_decision(invalid_update) is False
    
    def test_reasoning_generation(self, decision_engine, sample_extracted_fact, sample_similarity_results):
        """Test that reasoning is properly generated for decisions."""
        decision = decision_engine.decide(sample_extracted_fact, sample_similarity_results)
        
        assert isinstance(decision.reasoning, str)
        assert len(decision.reasoning) > 0
        assert decision.operation.value in decision.reasoning.lower() or \
               "similar" in decision.reasoning.lower() or \
               "memory" in decision.reasoning.lower()


class TestEvolutionDecisionEngineEdgeCases:
    """Test edge cases and error handling."""
    
    def test_empty_fact_content(self, decision_engine):
        """Test handling of fact with empty content."""
        empty_fact = ExtractedFact(
            fact="",
            category="technical_preference",
            confidence=0.8,
            relevance_score=0.7
        )
        
        decision = decision_engine.decide(empty_fact, [])
        
        # Should handle gracefully
        assert isinstance(decision, EvolutionDecision)
        assert decision.operation == EvolutionOperation.NOOP
    
    def test_invalid_confidence_values(self, decision_engine):
        """Test handling of invalid confidence values."""
        # This would typically be caught by Pydantic validation in ExtractedFact
        # but we test the decision engine's robustness
        decision_engine.confidence_threshold = 0.7
        
        # Test with various threshold scenarios
        assert decision_engine.confidence_threshold == 0.7
    
    def test_similarity_results_edge_cases(self, decision_engine, sample_extracted_fact):
        """Test handling of edge cases in similarity results."""
        edge_case_similarities = [
            SimilarityResult(
                memory_id="",  # Empty ID
                content="",    # Empty content  
                similarity_score=1.0,
                metadata=None
            )
        ]
        
        decision = decision_engine.decide(sample_extracted_fact, edge_case_similarities)
        
        # Should handle gracefully without crashing
        assert isinstance(decision, EvolutionDecision)


@pytest.mark.integration  
class TestEvolutionDecisionEngineIntegration:
    """Integration tests for EvolutionDecisionEngine."""
    
    def test_integration_with_similarity_analyzer(self):
        """Test integration with SimilarityAnalyzer output."""
        # Test that decision engine correctly processes SimilarityAnalyzer results
        pass
    
    def test_integration_with_fact_extractor(self):
        """Test integration with FactExtractor output."""
        # Test that decision engine correctly processes FactExtractor results  
        pass


@pytest.mark.slow
class TestEvolutionDecisionEnginePerformance:
    """Performance tests for EvolutionDecisionEngine."""
    
    def test_large_batch_processing_performance(self):
        """Test performance with large batches of facts."""
        # Test processing hundreds of facts efficiently
        pass
    
    def test_complex_similarity_set_performance(self):
        """Test performance with many similar memories."""
        # Test decision making with hundreds of similar memories
        pass