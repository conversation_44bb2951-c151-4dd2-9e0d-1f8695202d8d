"""
Unit tests for the Evolution Service module.

Tests the orchestration of the two-phase evolution pipeline including:
- Service initialization and configuration
- Evolution processing workflow
- Metrics tracking and aggregation
- Error handling and fallback modes
- Component integration and coordination
- Performance monitoring
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
import time

from app.evolution.evolution_service import (
    EvolutionService,
    EvolutionConfig,
    EvolutionMetrics,
    EvolutionResult,
    PromptVersion
)
from app.evolution.fact_extractor import ExtractedFact, ExtractionContext
from app.evolution.similarity_analyzer import SimilarityResult
from app.evolution.decision_engine import EvolutionDecision, EvolutionOperation


class TestEvolutionConfig:
    """Test the EvolutionConfig dataclass."""
    
    def test_evolution_config_defaults(self):
        """Test default configuration values."""
        config = EvolutionConfig()
        
        assert config.enable_evolution is True
        assert config.evolution_timeout == 30.0
        assert config.similarity_threshold == 0.7
        assert config.confidence_threshold == 0.6
        assert config.max_facts_per_text == 10
        assert config.prompt_version == PromptVersion.V3_TECHNICAL
        assert config.feature_flag == "evolution_intelligence_v1"
    
    def test_evolution_config_custom_values(self):
        """Test configuration with custom values."""
        config = EvolutionConfig(
            enable_evolution=False,
            evolution_timeout=60.0,
            similarity_threshold=0.8,
            confidence_threshold=0.7,
            max_facts_per_text=5,
            prompt_version=PromptVersion.V2_ENHANCED,
            feature_flag="test_flag"
        )
        
        assert config.enable_evolution is False
        assert config.evolution_timeout == 60.0
        assert config.similarity_threshold == 0.8
        assert config.confidence_threshold == 0.7
        assert config.max_facts_per_text == 5
        assert config.prompt_version == PromptVersion.V2_ENHANCED
        assert config.feature_flag == "test_flag"


class TestEvolutionMetrics:
    """Test the EvolutionMetrics class."""
    
    def test_metrics_initialization(self):
        """Test metrics initialization with default values."""
        metrics = EvolutionMetrics()
        
        assert metrics.total_operations == 0
        assert metrics.add_operations == 0
        assert metrics.update_operations == 0
        assert metrics.delete_operations == 0
        assert metrics.noop_operations == 0
        assert metrics.conflicts_detected == 0
        assert metrics.processing_time == 0.0
        assert metrics.facts_extracted == 0
        assert metrics.similarities_computed == 0
    
    def test_metrics_reset(self):
        """Test metrics reset functionality."""
        metrics = EvolutionMetrics()
        
        # Set some values
        metrics.total_operations = 10
        metrics.add_operations = 5
        metrics.processing_time = 15.0
        
        # Reset
        metrics.reset()
        
        assert metrics.total_operations == 0
        assert metrics.add_operations == 0
        assert metrics.processing_time == 0.0
    
    def test_metrics_to_dict(self):
        """Test metrics conversion to dictionary."""
        metrics = EvolutionMetrics()
        metrics.total_operations = 10
        metrics.add_operations = 3
        metrics.update_operations = 4
        metrics.delete_operations = 2
        metrics.noop_operations = 1
        metrics.conflicts_detected = 1
        metrics.processing_time = 25.5
        metrics.facts_extracted = 8
        metrics.similarities_computed = 15
        
        result = metrics.to_dict()
        
        assert result["total_operations"] == 10
        assert result["add_operations"] == 3
        assert result["update_operations"] == 4
        assert result["delete_operations"] == 2
        assert result["noop_operations"] == 1
        assert result["conflicts_detected"] == 1
        assert result["processing_time"] == 25.5
        assert result["facts_extracted"] == 8
        assert result["similarities_computed"] == 15
        assert result["learning_efficiency"] == 0.6  # (4+2)/10
    
    def test_learning_efficiency_calculation(self):
        """Test learning efficiency calculation."""
        metrics = EvolutionMetrics()
        
        # Test with zero operations
        result = metrics.to_dict()
        assert result["learning_efficiency"] == 0.0
        
        # Test with normal operations
        metrics.total_operations = 20
        metrics.update_operations = 8
        metrics.delete_operations = 4
        
        result = metrics.to_dict()
        assert result["learning_efficiency"] == 0.6  # (8+4)/20


class TestEvolutionResult:
    """Test the EvolutionResult dataclass."""
    
    def test_evolution_result_creation(self):
        """Test creating evolution result."""
        metrics = EvolutionMetrics()
        operations = [{"operation": "ADD", "fact": "test"}]
        errors = ["test error"]
        
        result = EvolutionResult(
            success=True,
            metrics=metrics,
            operations=operations,
            errors=errors,
            processing_time=15.5,
            fallback_mode=True
        )
        
        assert result.success is True
        assert result.metrics == metrics
        assert result.operations == operations
        assert result.errors == errors
        assert result.processing_time == 15.5
        assert result.fallback_mode is True


class TestEvolutionService:
    """Test the EvolutionService class."""
    
    def test_service_initialization_default_config(self):
        """Test service initialization with default configuration."""
        service = EvolutionService()
        
        assert service.config.enable_evolution is True
        assert service.config.prompt_version == PromptVersion.V3_TECHNICAL
        assert service.db_session is None
        assert service.fact_extractor is not None
        assert service.similarity_analyzer is not None
        assert service.decision_engine is not None
        assert service.prompt_manager is not None
        assert isinstance(service.session_metrics, EvolutionMetrics)
    
    def test_service_initialization_custom_config(self):
        """Test service initialization with custom configuration."""
        custom_config = EvolutionConfig(
            enable_evolution=False,
            similarity_threshold=0.8
        )
        mock_db = Mock()
        
        service = EvolutionService(config=custom_config, db_session=mock_db)
        
        assert service.config == custom_config
        assert service.db_session == mock_db
        assert service.config.similarity_threshold == 0.8
    
    @pytest.mark.asyncio
    async def test_process_memory_evolution_disabled(self):
        """Test evolution processing when disabled."""
        config = EvolutionConfig(enable_evolution=False)
        service = EvolutionService(config=config)
        
        result = await service.process_memory_evolution(
            text="Test input",
            user_id="user123",
            client_name="test_client"
        )
        
        assert result.success is True
        assert len(result.operations) == 0
        assert len(result.errors) == 0
        assert result.processing_time >= 0
    
    @pytest.mark.asyncio
    async def test_process_memory_evolution_success(self):
        """Test successful evolution processing."""
        service = EvolutionService()
        
        # Mock the components
        mock_facts = [
            ExtractedFact(
                content="Test fact",
                confidence=0.8,
                category="technical",
                tags=["test"],
                context={}
            )
        ]
        
        mock_similarity = SimilarityResult(
            memory_id="mem123",
            similarity_score=0.5,
            fact_similarity=0.6,
            metadata={}
        )
        
        mock_decision = EvolutionDecision(
            operation=EvolutionOperation.ADD,
            confidence=0.8,
            target_memory_id=None,
            reasoning="Should add new memory",
            metadata={}
        )
        
        with patch.object(service.fact_extractor, 'extract_facts', new_callable=AsyncMock) as mock_extract:
            with patch.object(service.similarity_analyzer, 'analyze_similarities', new_callable=AsyncMock) as mock_analyze:
                with patch.object(service.decision_engine, 'make_decisions', new_callable=AsyncMock) as mock_decide:
                    mock_extract.return_value = mock_facts
                    mock_analyze.return_value = [mock_similarity]
                    mock_decide.return_value = [mock_decision]
                    
                    result = await service.process_memory_evolution(
                        text="Test input about Python programming",
                        user_id="user123",
                        client_name="test_client",
                        existing_memories=[{"id": "mem123", "text": "Old Python info"}]
                    )
                    
                    assert result.success is True
                    assert len(result.operations) == 1
                    assert result.operations[0]["operation"] == "ADD"
                    assert len(result.errors) == 0
                    assert result.processing_time > 0
                    
                    # Verify component calls
                    mock_extract.assert_called_once()
                    mock_analyze.assert_called_once()
                    mock_decide.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_memory_evolution_timeout(self):
        """Test evolution processing with timeout."""
        config = EvolutionConfig(evolution_timeout=0.1)  # Very short timeout
        service = EvolutionService(config=config)
        
        # Mock a slow fact extraction
        async def slow_extract(*args, **kwargs):
            await asyncio.sleep(0.2)  # Longer than timeout
            return []
        
        with patch.object(service.fact_extractor, 'extract_facts', side_effect=slow_extract):
            result = await service.process_memory_evolution(
                text="Test input",
                user_id="user123",
                client_name="test_client"
            )
            
            assert result.success is True
            assert result.fallback_mode is True
            assert "timeout" in " ".join(result.errors).lower()
    
    @pytest.mark.asyncio
    async def test_process_memory_evolution_exception_handling(self):
        """Test evolution processing with exception handling."""
        service = EvolutionService()
        
        # Mock an exception during fact extraction
        with patch.object(service.fact_extractor, 'extract_facts', side_effect=Exception("Test error")):
            result = await service.process_memory_evolution(
                text="Test input",
                user_id="user123",
                client_name="test_client"
            )
            
            assert result.success is True
            assert result.fallback_mode is True
            assert len(result.errors) > 0
            assert "Test error" in result.errors[0]
    
    def test_get_session_metrics(self):
        """Test getting session metrics."""
        service = EvolutionService()
        
        # Modify some metrics
        service.session_metrics.total_operations = 5
        service.session_metrics.add_operations = 3
        
        metrics = service.get_session_metrics()
        
        assert metrics.total_operations == 5
        assert metrics.add_operations == 3
    
    def test_reset_session_metrics(self):
        """Test resetting session metrics."""
        service = EvolutionService()
        
        # Set some metrics
        service.session_metrics.total_operations = 10
        service.session_metrics.processing_time = 25.0
        
        service.reset_session_metrics()
        
        assert service.session_metrics.total_operations == 0
        assert service.session_metrics.processing_time == 0.0
    
    def test_is_evolution_enabled(self):
        """Test evolution enabled check."""
        # Test enabled
        service_enabled = EvolutionService()
        assert service_enabled.is_evolution_enabled() is True
        
        # Test disabled
        config_disabled = EvolutionConfig(enable_evolution=False)
        service_disabled = EvolutionService(config=config_disabled)
        assert service_disabled.is_evolution_enabled() is False
    
    @pytest.mark.asyncio
    async def test_process_facts_integration(self):
        """Test the _process_facts method integration."""
        service = EvolutionService()
        
        # Create mock facts
        mock_facts = [
            ExtractedFact(
                content="Test fact 1",
                confidence=0.9,
                category="technical",
                tags=["python"],
                context={}
            ),
            ExtractedFact(
                content="Test fact 2", 
                confidence=0.7,
                category="general",
                tags=["coding"],
                context={}
            )
        ]
        
        mock_similarities = [
            SimilarityResult(
                memory_id="mem1",
                similarity_score=0.8,
                fact_similarity=0.7,
                metadata={}
            )
        ]
        
        mock_decisions = [
            EvolutionDecision(
                operation=EvolutionOperation.UPDATE,
                confidence=0.85,
                target_memory_id="mem1",
                reasoning="Update existing memory",
                metadata={}
            )
        ]
        
        with patch.object(service.similarity_analyzer, 'analyze_similarities', new_callable=AsyncMock) as mock_analyze:
            with patch.object(service.decision_engine, 'make_decisions', new_callable=AsyncMock) as mock_decide:
                mock_analyze.return_value = mock_similarities
                mock_decide.return_value = mock_decisions
                
                # Test the internal method (would need to be made protected/public in actual code)
                # This tests integration between similarity analysis and decision making
                result = await service.process_memory_evolution(
                    text="Test",
                    user_id="user123", 
                    client_name="test",
                    existing_memories=[{"id": "mem1", "text": "existing"}]
                )
                
                assert result.success is True
    
    def test_validate_input_parameters(self):
        """Test input parameter validation."""
        service = EvolutionService()
        
        # Test with valid parameters - should not raise
        try:
            # This would be called internally in process_memory_evolution
            assert isinstance(service.config.similarity_threshold, float)
            assert 0.0 <= service.config.similarity_threshold <= 1.0
            assert isinstance(service.config.confidence_threshold, float)
            assert 0.0 <= service.config.confidence_threshold <= 1.0
        except Exception as e:
            pytest.fail(f"Valid parameters should not raise exception: {e}")
    
    @pytest.mark.asyncio
    async def test_memory_evolution_with_context(self):
        """Test evolution processing with additional context."""
        service = EvolutionService()
        
        context = {
            "conversation_history": ["Previous message"],
            "application_context": "coding_session",
            "user_preferences": {"verbosity": "high"}
        }
        
        with patch.object(service.fact_extractor, 'extract_facts', new_callable=AsyncMock) as mock_extract:
            mock_extract.return_value = []
            
            result = await service.process_memory_evolution(
                text="Test input",
                user_id="user123",
                client_name="test_client",
                context=context
            )
            
            # Verify context was passed to fact extractor
            call_args = mock_extract.call_args
            assert call_args is not None
            # The context should be included in the extraction context
            assert result.success is True
    
    def test_service_component_integration(self):
        """Test that all service components are properly integrated."""
        service = EvolutionService()
        
        # Verify all components are initialized
        assert service.fact_extractor is not None
        assert service.similarity_analyzer is not None
        assert service.decision_engine is not None
        assert service.prompt_manager is not None
        
        # Verify prompt manager has correct version
        assert service.prompt_manager.default_version == service.config.prompt_version
    
    @pytest.mark.asyncio
    async def test_concurrent_evolution_processing(self):
        """Test concurrent evolution processing."""
        service = EvolutionService()
        
        # Mock quick responses
        with patch.object(service.fact_extractor, 'extract_facts', new_callable=AsyncMock) as mock_extract:
            mock_extract.return_value = []
            
            # Run multiple concurrent operations
            tasks = [
                service.process_memory_evolution(f"Test {i}", f"user{i}", "client")
                for i in range(3)
            ]
            
            results = await asyncio.gather(*tasks)
            
            # All should succeed
            assert all(result.success for result in results)
            assert len(results) == 3