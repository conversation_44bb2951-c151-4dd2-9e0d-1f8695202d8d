"""
Unit tests for the Evolution Pipeline module.

Tests the orchestration of the evolution pipeline including:
- Pipeline input validation and processing
- Coordination between fact extraction and similarity analysis
- Evolution decision execution and tracking
- Batch processing capabilities
- Error handling and recovery mechanisms
- Performance monitoring and metrics collection
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from app.evolution.pipeline import (
    EvolutionPipeline,
    PipelineInput,
    PipelineResult,
    BatchPipelineResult
)
from app.evolution.fact_extractor import ExtractedFact, FactExtractionResponse, ExtractionContext
from app.evolution.similarity_analyzer import SimilarityR<PERSON>ult
from app.evolution.decision_engine import EvolutionDecision, EvolutionOperation


class TestPipelineInput:
    """Test the PipelineInput dataclass."""
    
    def test_pipeline_input_creation(self):
        """Test basic pipeline input creation."""
        input_data = PipelineInput(
            user_id="user_123",
            latest_message="I prefer TypeScript for large projects",
            recent_messages=["What's your favorite language?", "Let's discuss programming"],
            existing_memories=[]
        )
        
        assert input_data.user_id == "user_123"
        assert input_data.latest_message == "I prefer TypeScript for large projects"
        assert len(input_data.recent_messages) == 2
        assert len(input_data.existing_memories) == 0
        assert input_data.user_background is None
    
    def test_pipeline_input_with_memories_and_background(self):
        """Test pipeline input with existing memories and user background."""
        existing_memories = [
            {"id": "mem_1", "content": "User likes Python", "metadata": {}},
            {"id": "mem_2", "content": "User works in data science", "metadata": {}}
        ]
        
        input_data = PipelineInput(
            user_id="user_456",
            latest_message="I'm switching to TypeScript",
            recent_messages=["Considering a language change"],
            existing_memories=existing_memories,
            user_background="Senior developer with 10 years experience"
        )
        
        assert len(input_data.existing_memories) == 2
        assert input_data.user_background == "Senior developer with 10 years experience"


class TestPipelineResult:
    """Test the PipelineResult dataclass."""
    
    def test_pipeline_result_creation(self):
        """Test basic pipeline result creation."""
        extracted_facts = [
            ExtractedFact(
                fact="Prefers TypeScript",
                category="technical_preference",
                confidence=0.9,
                relevance_score=0.85
            )
        ]
        
        decisions = [
            EvolutionDecision(
                operation=EvolutionOperation.ADD,
                confidence=0.9,
                reasoning="No similar memories found",
                target_memory_id=None,
                new_content="Prefers TypeScript"
            )
        ]
        
        result = PipelineResult(
            user_id="user_123",
            success=True,
            extracted_facts=extracted_facts,
            evolution_decisions=decisions,
            processing_time_ms=250.5,
            error_message=None
        )
        
        assert result.user_id == "user_123"
        assert result.success is True
        assert len(result.extracted_facts) == 1
        assert len(result.evolution_decisions) == 1
        assert result.processing_time_ms == 250.5
        assert result.error_message is None
    
    def test_pipeline_result_with_error(self):
        """Test pipeline result with error."""
        result = PipelineResult(
            user_id="user_456",
            success=False,
            extracted_facts=[],
            evolution_decisions=[],
            processing_time_ms=100.0,
            error_message="LLM API timeout"
        )
        
        assert result.success is False
        assert len(result.extracted_facts) == 0
        assert len(result.evolution_decisions) == 0
        assert result.error_message == "LLM API timeout"


class TestBatchPipelineResult:
    """Test the BatchPipelineResult dataclass."""
    
    def test_batch_result_creation(self):
        """Test batch pipeline result creation."""
        individual_results = [
            PipelineResult(
                user_id="user_1",
                success=True,
                extracted_facts=[],
                evolution_decisions=[],
                processing_time_ms=150.0
            ),
            PipelineResult(
                user_id="user_2", 
                success=True,
                extracted_facts=[],
                evolution_decisions=[],
                processing_time_ms=200.0
            )
        ]
        
        batch_result = BatchPipelineResult(
            results=individual_results,
            total_processing_time_ms=350.0,
            successful_count=2,
            failed_count=0
        )
        
        assert len(batch_result.results) == 2
        assert batch_result.total_processing_time_ms == 350.0
        assert batch_result.successful_count == 2
        assert batch_result.failed_count == 0


@pytest.fixture
def mock_fact_extractor():
    """Mock fact extractor for testing."""
    extractor = Mock()
    extractor.extract_facts = AsyncMock()
    return extractor


@pytest.fixture
def mock_similarity_analyzer():
    """Mock similarity analyzer for testing."""
    analyzer = Mock()
    analyzer.find_similar_memories = AsyncMock()
    return analyzer


@pytest.fixture
def mock_decision_engine():
    """Mock decision engine for testing."""
    engine = Mock()
    engine.decide = Mock()
    engine.batch_decide = Mock()
    return engine


@pytest.fixture
def evolution_pipeline(mock_fact_extractor, mock_similarity_analyzer, mock_decision_engine):
    """Create an EvolutionPipeline instance for testing."""
    return EvolutionPipeline(
        fact_extractor=mock_fact_extractor,
        similarity_analyzer=mock_similarity_analyzer,
        decision_engine=mock_decision_engine
    )


@pytest.fixture
def sample_pipeline_input():
    """Sample pipeline input for testing."""
    return PipelineInput(
        user_id="user_123",
        latest_message="I prefer TypeScript over JavaScript for large projects",
        recent_messages=["What's your favorite programming language?"],
        existing_memories=[
            {"id": "mem_1", "content": "User likes Python", "metadata": {}},
            {"id": "mem_2", "content": "User works with React", "metadata": {}}
        ]
    )


class TestEvolutionPipeline:
    """Test the EvolutionPipeline class."""
    
    def test_initialization_with_components(self, mock_fact_extractor, mock_similarity_analyzer, mock_decision_engine):
        """Test pipeline initialization with components."""
        pipeline = EvolutionPipeline(
            fact_extractor=mock_fact_extractor,
            similarity_analyzer=mock_similarity_analyzer,
            decision_engine=mock_decision_engine
        )
        
        assert pipeline.fact_extractor == mock_fact_extractor
        assert pipeline.similarity_analyzer == mock_similarity_analyzer
        assert pipeline.decision_engine == mock_decision_engine
        assert pipeline.enable_metrics is True
        assert pipeline.timeout_seconds == 30.0
    
    def test_initialization_with_custom_config(self, mock_fact_extractor, mock_similarity_analyzer, mock_decision_engine):
        """Test pipeline initialization with custom configuration."""
        pipeline = EvolutionPipeline(
            fact_extractor=mock_fact_extractor,
            similarity_analyzer=mock_similarity_analyzer,
            decision_engine=mock_decision_engine,
            enable_metrics=False,
            timeout_seconds=60.0
        )
        
        assert pipeline.enable_metrics is False
        assert pipeline.timeout_seconds == 60.0
    
    @pytest.mark.asyncio
    async def test_process_success_flow(self, evolution_pipeline, sample_pipeline_input):
        """Test successful pipeline processing."""
        # Mock fact extraction
        extracted_facts = [
            ExtractedFact(
                fact="Prefers TypeScript for large projects",
                category="technical_preference",
                confidence=0.9,
                relevance_score=0.85
            )
        ]
        
        fact_response = FactExtractionResponse(
            facts=extracted_facts,
            processing_time_ms=150.0,
            model_used="claude-sonnet-4"
        )
        
        evolution_pipeline.fact_extractor.extract_facts.return_value = fact_response
        
        # Mock similarity analysis
        similarity_results = [
            SimilarityResult(
                memory_id="mem_1",
                content="User likes Python",
                similarity_score=0.6,
                metadata={}
            )
        ]
        
        evolution_pipeline.similarity_analyzer.find_similar_memories.return_value = similarity_results
        
        # Mock evolution decision
        evolution_decision = EvolutionDecision(
            operation=EvolutionOperation.ADD,
            confidence=0.9,
            reasoning="No highly similar memories found",
            target_memory_id=None,
            new_content="Prefers TypeScript for large projects"
        )
        
        evolution_pipeline.decision_engine.decide.return_value = evolution_decision
        
        # Process pipeline
        result = await evolution_pipeline.process(sample_pipeline_input)
        
        # Verify result
        assert result.success is True
        assert result.user_id == "user_123"
        assert len(result.extracted_facts) == 1
        assert len(result.evolution_decisions) == 1
        assert result.processing_time_ms > 0
        assert result.error_message is None
        
        # Verify component calls
        evolution_pipeline.fact_extractor.extract_facts.assert_called_once()
        evolution_pipeline.similarity_analyzer.find_similar_memories.assert_called_once()
        evolution_pipeline.decision_engine.decide.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_no_facts_extracted(self, evolution_pipeline, sample_pipeline_input):
        """Test pipeline processing when no facts are extracted."""
        # Mock empty fact extraction
        fact_response = FactExtractionResponse(
            facts=[],
            processing_time_ms=100.0,
            model_used="claude-sonnet-4"
        )
        
        evolution_pipeline.fact_extractor.extract_facts.return_value = fact_response
        
        # Process pipeline
        result = await evolution_pipeline.process(sample_pipeline_input)
        
        # Verify result
        assert result.success is True
        assert len(result.extracted_facts) == 0
        assert len(result.evolution_decisions) == 0
        
        # Similarity analysis and decision engine should not be called
        evolution_pipeline.similarity_analyzer.find_similar_memories.assert_not_called()
        evolution_pipeline.decision_engine.decide.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_process_fact_extraction_failure(self, evolution_pipeline, sample_pipeline_input):
        """Test pipeline processing when fact extraction fails."""
        # Mock fact extraction failure
        evolution_pipeline.fact_extractor.extract_facts.side_effect = Exception("LLM API timeout")
        
        # Process pipeline
        result = await evolution_pipeline.process(sample_pipeline_input)
        
        # Verify error handling
        assert result.success is False
        assert "LLM API timeout" in result.error_message
        assert len(result.extracted_facts) == 0
        assert len(result.evolution_decisions) == 0
    
    @pytest.mark.asyncio
    async def test_process_similarity_analysis_failure(self, evolution_pipeline, sample_pipeline_input):
        """Test pipeline processing when similarity analysis fails."""
        # Mock successful fact extraction
        extracted_facts = [
            ExtractedFact(
                fact="Prefers TypeScript",
                category="technical_preference", 
                confidence=0.9,
                relevance_score=0.85
            )
        ]
        
        fact_response = FactExtractionResponse(
            facts=extracted_facts,
            processing_time_ms=150.0,
            model_used="claude-sonnet-4"
        )
        
        evolution_pipeline.fact_extractor.extract_facts.return_value = fact_response
        
        # Mock similarity analysis failure
        evolution_pipeline.similarity_analyzer.find_similar_memories.side_effect = Exception("Vector store unavailable")
        
        # Process pipeline
        result = await evolution_pipeline.process(sample_pipeline_input)
        
        # Verify error handling
        assert result.success is False
        assert "Vector store unavailable" in result.error_message
        assert len(result.extracted_facts) == 1  # Facts were extracted successfully
        assert len(result.evolution_decisions) == 0  # But no decisions made due to failure
    
    @pytest.mark.asyncio
    async def test_process_decision_engine_failure(self, evolution_pipeline, sample_pipeline_input):
        """Test pipeline processing when decision engine fails."""
        # Mock successful fact extraction and similarity analysis
        extracted_facts = [
            ExtractedFact(
                fact="Prefers TypeScript",
                category="technical_preference",
                confidence=0.9,
                relevance_score=0.85
            )
        ]
        
        fact_response = FactExtractionResponse(
            facts=extracted_facts,
            processing_time_ms=150.0,
            model_used="claude-sonnet-4"
        )
        
        evolution_pipeline.fact_extractor.extract_facts.return_value = fact_response
        evolution_pipeline.similarity_analyzer.find_similar_memories.return_value = []
        
        # Mock decision engine failure
        evolution_pipeline.decision_engine.decide.side_effect = Exception("Decision logic error")
        
        # Process pipeline
        result = await evolution_pipeline.process(sample_pipeline_input)
        
        # Verify error handling
        assert result.success is False
        assert "Decision logic error" in result.error_message
    
    @pytest.mark.asyncio
    async def test_process_multiple_facts(self, evolution_pipeline, sample_pipeline_input):
        """Test pipeline processing with multiple extracted facts."""
        # Mock multiple fact extraction
        extracted_facts = [
            ExtractedFact(
                fact="Prefers TypeScript",
                category="technical_preference",
                confidence=0.9,
                relevance_score=0.85
            ),
            ExtractedFact(
                fact="Working on e-commerce project",
                category="project_decision",
                confidence=0.8,
                relevance_score=0.9
            )
        ]
        
        fact_response = FactExtractionResponse(
            facts=extracted_facts,
            processing_time_ms=200.0,
            model_used="claude-sonnet-4"
        )
        
        evolution_pipeline.fact_extractor.extract_facts.return_value = fact_response
        evolution_pipeline.similarity_analyzer.find_similar_memories.return_value = []
        
        # Mock decisions for both facts
        decisions = [
            EvolutionDecision(
                operation=EvolutionOperation.ADD,
                confidence=0.9,
                reasoning="No similar memories found",
                target_memory_id=None,
                new_content="Prefers TypeScript"
            ),
            EvolutionDecision(
                operation=EvolutionOperation.ADD,
                confidence=0.8,
                reasoning="No similar memories found",
                target_memory_id=None,
                new_content="Working on e-commerce project"
            )
        ]
        
        evolution_pipeline.decision_engine.decide.side_effect = decisions
        
        # Process pipeline
        result = await evolution_pipeline.process(sample_pipeline_input)
        
        # Verify result
        assert result.success is True
        assert len(result.extracted_facts) == 2
        assert len(result.evolution_decisions) == 2
        assert evolution_pipeline.decision_engine.decide.call_count == 2
    
    @pytest.mark.asyncio
    async def test_process_timeout_handling(self, evolution_pipeline, sample_pipeline_input):
        """Test pipeline timeout handling."""
        # Set short timeout
        evolution_pipeline.timeout_seconds = 0.1
        
        # Mock slow fact extraction
        async def slow_extract_facts(*args, **kwargs):
            await asyncio.sleep(0.2)  # Longer than timeout
            return FactExtractionResponse(facts=[], processing_time_ms=200, model_used="claude-sonnet-4")
        
        evolution_pipeline.fact_extractor.extract_facts = slow_extract_facts
        
        # Process pipeline
        result = await evolution_pipeline.process(sample_pipeline_input)
        
        # Verify timeout handling
        assert result.success is False
        assert "timeout" in result.error_message.lower()
    
    @pytest.mark.asyncio
    async def test_batch_process_success(self, evolution_pipeline):
        """Test successful batch processing."""
        inputs = [
            PipelineInput(
                user_id="user_1",
                latest_message="I like TypeScript",
                recent_messages=[],
                existing_memories=[]
            ),
            PipelineInput(
                user_id="user_2",
                latest_message="I prefer Python",
                recent_messages=[],
                existing_memories=[]
            )
        ]
        
        # Mock successful processing for both inputs
        success_result = PipelineResult(
            user_id="test_user",
            success=True,
            extracted_facts=[],
            evolution_decisions=[],
            processing_time_ms=100.0
        )
        
        with patch.object(evolution_pipeline, 'process', new_callable=AsyncMock) as mock_process:
            mock_process.return_value = success_result
            
            batch_result = await evolution_pipeline.batch_process(inputs)
            
            assert len(batch_result.results) == 2
            assert batch_result.successful_count == 2
            assert batch_result.failed_count == 0
            assert batch_result.total_processing_time_ms > 0
            assert mock_process.call_count == 2
    
    @pytest.mark.asyncio
    async def test_batch_process_mixed_results(self, evolution_pipeline):
        """Test batch processing with mixed success/failure results."""
        inputs = [
            PipelineInput(user_id="user_1", latest_message="Message 1", recent_messages=[], existing_memories=[]),
            PipelineInput(user_id="user_2", latest_message="Message 2", recent_messages=[], existing_memories=[])
        ]
        
        # Mock mixed results
        success_result = PipelineResult(
            user_id="user_1", success=True, extracted_facts=[], evolution_decisions=[], processing_time_ms=100.0
        )
        failure_result = PipelineResult(
            user_id="user_2", success=False, extracted_facts=[], evolution_decisions=[], 
            processing_time_ms=50.0, error_message="Processing failed"
        )
        
        with patch.object(evolution_pipeline, 'process', new_callable=AsyncMock) as mock_process:
            mock_process.side_effect = [success_result, failure_result]
            
            batch_result = await evolution_pipeline.batch_process(inputs)
            
            assert len(batch_result.results) == 2
            assert batch_result.successful_count == 1
            assert batch_result.failed_count == 1
    
    def test_metrics_collection(self, evolution_pipeline):
        """Test pipeline metrics collection."""
        # Test that metrics are properly collected when enabled
        assert evolution_pipeline.enable_metrics is True
        
        # Metrics would be tested in integration tests
        pass


@pytest.mark.integration
class TestEvolutionPipelineIntegration:
    """Integration tests for EvolutionPipeline."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_pipeline_execution(self):
        """Test end-to-end pipeline execution with real components."""
        # Test full pipeline with real (but mocked) components
        pass


@pytest.mark.slow  
class TestEvolutionPipelinePerformance:
    """Performance tests for EvolutionPipeline."""
    
    @pytest.mark.asyncio
    async def test_concurrent_pipeline_processing(self):
        """Test concurrent pipeline processing performance."""
        # Test multiple simultaneous pipeline executions
        pass
    
    @pytest.mark.asyncio
    async def test_large_batch_processing_performance(self):
        """Test performance with large batch sizes."""
        # Test processing of hundreds of inputs
        pass