"""
Unit tests for the Custom Prompts Development and Optimization module.

Tests the versioned prompt system for memory evolution including:
- Prompt template structure and validation
- Prompt version management and A/B testing
- Domain-specific optimization for technical conversations
- Template variable substitution
- Output validation and schema compliance
- Performance and caching mechanisms
"""

import pytest
from unittest.mock import Mock, patch
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
import json
import hashlib

from app.evolution.prompts import (
    PromptManager,
    PromptTemplate,
    PromptVersion
)


class TestPromptVersion:
    """Test the PromptVersion enum."""
    
    def test_prompt_version_values(self):
        """Test that all prompt versions have correct values."""
        assert PromptVersion.V1_BASIC.value == "v1_basic"
        assert PromptVersion.V2_ENHANCED.value == "v2_enhanced"
        assert PromptVersion.V3_TECHNICAL.value == "v3_technical"
    
    def test_prompt_version_iteration(self):
        """Test iterating over prompt versions."""
        versions = list(PromptVersion)
        assert len(versions) == 3
        assert PromptVersion.V1_BASIC in versions
        assert PromptVersion.V2_ENHANCED in versions
        assert PromptVersion.V3_TECHNICAL in versions


class TestPromptTemplate:
    """Test the PromptTemplate dataclass."""
    
    def test_prompt_template_creation_minimal(self):
        """Test creating a prompt template with minimal fields."""
        template = PromptTemplate(
            name="test_template",
            version=PromptVersion.V1_BASIC,
            template="Test template with {variable}",
            description="A test template",
            variables=["variable"],
            examples=[]
        )
        
        assert template.name == "test_template"
        assert template.version == PromptVersion.V1_BASIC
        assert template.template == "Test template with {variable}"
        assert template.description == "A test template"
        assert template.variables == ["variable"]
        assert template.examples == []
        assert template.validation_schema is None
    
    def test_prompt_template_creation_complete(self):
        """Test creating a prompt template with all fields."""
        validation_schema = {
            "type": "object",
            "properties": {
                "confidence": {"type": "number", "minimum": 0, "maximum": 1}
            }
        }
        
        examples = [
            {"input": "test input", "output": "test output"},
            {"input": "another input", "output": "another output"}
        ]
        
        template = PromptTemplate(
            name="comprehensive_template",
            version=PromptVersion.V3_TECHNICAL,
            template="Analyze: {text}\nContext: {context}\nOutput format: {format}",
            description="A comprehensive template for testing",
            variables=["text", "context", "format"],
            examples=examples,
            validation_schema=validation_schema
        )
        
        assert template.name == "comprehensive_template"
        assert template.version == PromptVersion.V3_TECHNICAL
        assert "Analyze:" in template.template
        assert len(template.variables) == 3
        assert template.examples == examples
        assert template.validation_schema == validation_schema
    
    def test_prompt_template_variable_detection(self):
        """Test that template variables are correctly identified."""
        template = PromptTemplate(
            name="multi_var_template",
            version=PromptVersion.V2_ENHANCED,
            template="Process {input_text} using {method} with {confidence_threshold} threshold",
            description="Template with multiple variables",
            variables=["input_text", "method", "confidence_threshold"],
            examples=[]
        )
        
        # Variables should match what's declared
        expected_vars = {"input_text", "method", "confidence_threshold"}
        declared_vars = set(template.variables)
        
        assert declared_vars == expected_vars


class TestPromptManager:
    """Test the PromptManager class."""
    
    def test_prompt_manager_initialization_default(self):
        """Test prompt manager initialization with default version."""
        manager = PromptManager()
        
        assert manager.default_version == PromptVersion.V3_TECHNICAL
        assert manager.prompts is not None
        assert isinstance(manager.prompts, dict)
    
    def test_prompt_manager_initialization_custom_version(self):
        """Test prompt manager initialization with custom version."""
        manager = PromptManager(default_version=PromptVersion.V1_BASIC)
        
        assert manager.default_version == PromptVersion.V1_BASIC
    
    def test_get_fact_extraction_prompt_default_version(self):
        """Test getting fact extraction prompt with default version."""
        manager = PromptManager()
        
        prompt = manager.get_fact_extraction_prompt()
        
        assert prompt is not None
        assert isinstance(prompt, PromptTemplate)
        assert prompt.version == manager.default_version
        assert "fact" in prompt.name.lower() or "extract" in prompt.name.lower()
    
    def test_get_fact_extraction_prompt_specific_version(self):
        """Test getting fact extraction prompt with specific version."""
        manager = PromptManager()
        
        for version in PromptVersion:
            prompt = manager.get_fact_extraction_prompt(version=version)
            
            assert prompt is not None
            assert prompt.version == version
    
    def test_get_decision_prompt_default_version(self):
        """Test getting decision prompt with default version."""
        manager = PromptManager()
        
        prompt = manager.get_decision_prompt()
        
        assert prompt is not None
        assert isinstance(prompt, PromptTemplate)
        assert prompt.version == manager.default_version
        assert "decision" in prompt.name.lower() or "evolution" in prompt.name.lower()
    
    def test_get_decision_prompt_specific_version(self):
        """Test getting decision prompt with specific version."""
        manager = PromptManager()
        
        for version in PromptVersion:
            prompt = manager.get_decision_prompt(version=version)
            
            assert prompt is not None
            assert prompt.version == version
    
    def test_format_prompt_basic_substitution(self):
        """Test basic prompt formatting with variable substitution."""
        manager = PromptManager()
        
        # Create a test template
        template = PromptTemplate(
            name="test_format",
            version=PromptVersion.V1_BASIC,
            template="Analyze this text: {text}\nWith confidence: {confidence}",
            description="Test formatting",
            variables=["text", "confidence"],
            examples=[]
        )
        
        variables = {
            "text": "Sample text for analysis",
            "confidence": "0.8"
        }
        
        formatted = manager.format_prompt(template, variables)
        
        assert "Sample text for analysis" in formatted
        assert "0.8" in formatted
        assert "{text}" not in formatted
        assert "{confidence}" not in formatted
    
    def test_format_prompt_missing_variables(self):
        """Test prompt formatting with missing variables."""
        manager = PromptManager()
        
        template = PromptTemplate(
            name="test_missing",
            version=PromptVersion.V1_BASIC,
            template="Process {input} with {method} and {threshold}",
            description="Test missing variables",
            variables=["input", "method", "threshold"],
            examples=[]
        )
        
        # Only provide some variables
        variables = {
            "input": "test input",
            "method": "analysis"
            # missing 'threshold'
        }
        
        # Should either raise an error or handle gracefully
        with pytest.raises(KeyError):
            manager.format_prompt(template, variables)
    
    def test_format_prompt_extra_variables(self):
        """Test prompt formatting with extra variables."""
        manager = PromptManager()
        
        template = PromptTemplate(
            name="test_extra",
            version=PromptVersion.V1_BASIC,
            template="Simple template with {variable}",
            description="Test extra variables",
            variables=["variable"],
            examples=[]
        )
        
        variables = {
            "variable": "test value",
            "extra_var": "not used",
            "another_extra": "also not used"
        }
        
        formatted = manager.format_prompt(template, variables)
        
        assert "test value" in formatted
        assert "not used" not in formatted
        assert "also not used" not in formatted
    
    def test_validate_prompt_output_valid_json(self):
        """Test validating prompt output with valid JSON."""
        manager = PromptManager()
        
        validation_schema = {
            "type": "object",
            "properties": {
                "confidence": {"type": "number", "minimum": 0, "maximum": 1},
                "category": {"type": "string"}
            },
            "required": ["confidence", "category"]
        }
        
        template = PromptTemplate(
            name="test_validation",
            version=PromptVersion.V1_BASIC,
            template="Test",
            description="Test validation",
            variables=[],
            examples=[],
            validation_schema=validation_schema
        )
        
        valid_output = '{"confidence": 0.8, "category": "technical"}'
        
        is_valid, errors = manager.validate_prompt_output(template, valid_output)
        
        assert is_valid is True
        assert len(errors) == 0
    
    def test_validate_prompt_output_invalid_json(self):
        """Test validating prompt output with invalid JSON."""
        manager = PromptManager()
        
        template = PromptTemplate(
            name="test_invalid_json",
            version=PromptVersion.V1_BASIC,
            template="Test",
            description="Test invalid JSON",
            variables=[],
            examples=[],
            validation_schema={"type": "object"}
        )
        
        invalid_output = '{"confidence": 0.8, "category": incomplete'
        
        is_valid, errors = manager.validate_prompt_output(template, invalid_output)
        
        assert is_valid is False
        assert len(errors) > 0
        assert "json" in errors[0].lower() or "parse" in errors[0].lower()
    
    def test_validate_prompt_output_schema_violation(self):
        """Test validating prompt output with schema violations."""
        manager = PromptManager()
        
        validation_schema = {
            "type": "object",
            "properties": {
                "confidence": {"type": "number", "minimum": 0, "maximum": 1},
                "category": {"type": "string"}
            },
            "required": ["confidence", "category"]
        }
        
        template = PromptTemplate(
            name="test_schema_violation",
            version=PromptVersion.V1_BASIC,
            template="Test",
            description="Test schema violation",
            variables=[],
            examples=[],
            validation_schema=validation_schema
        )
        
        # Missing required field and invalid confidence value
        invalid_output = '{"confidence": 1.5}'
        
        is_valid, errors = manager.validate_prompt_output(template, invalid_output)
        
        assert is_valid is False
        assert len(errors) > 0
    
    def test_validate_prompt_output_no_schema(self):
        """Test validating prompt output with no validation schema."""
        manager = PromptManager()
        
        template = PromptTemplate(
            name="test_no_schema",
            version=PromptVersion.V1_BASIC,
            template="Test",
            description="Test no schema",
            variables=[],
            examples=[],
            validation_schema=None
        )
        
        output = "Any output without schema validation"
        
        is_valid, errors = manager.validate_prompt_output(template, output)
        
        assert is_valid is True
        assert len(errors) == 0
    
    def test_get_prompt_hash(self):
        """Test generating hash for prompt templates."""
        manager = PromptManager()
        
        template1 = PromptTemplate(
            name="test1",
            version=PromptVersion.V1_BASIC,
            template="Template content",
            description="Test",
            variables=[],
            examples=[]
        )
        
        template2 = PromptTemplate(
            name="test2",
            version=PromptVersion.V1_BASIC,
            template="Different content",
            description="Test",
            variables=[],
            examples=[]
        )
        
        hash1 = manager.get_prompt_hash(template1)
        hash2 = manager.get_prompt_hash(template2)
        
        assert hash1 != hash2
        assert isinstance(hash1, str)
        assert isinstance(hash2, str)
        assert len(hash1) > 0
        assert len(hash2) > 0
    
    def test_get_prompt_hash_consistency(self):
        """Test that prompt hash is consistent for same template."""
        manager = PromptManager()
        
        template = PromptTemplate(
            name="consistency_test",
            version=PromptVersion.V2_ENHANCED,
            template="Consistent template content",
            description="Test consistency",
            variables=["var1", "var2"],
            examples=[]
        )
        
        hash1 = manager.get_prompt_hash(template)
        hash2 = manager.get_prompt_hash(template)
        
        assert hash1 == hash2
    
    def test_get_available_versions(self):
        """Test getting available versions for a prompt type."""
        manager = PromptManager()
        
        fact_versions = manager.get_available_versions("fact_extraction")
        decision_versions = manager.get_available_versions("decision")
        
        assert isinstance(fact_versions, list)
        assert isinstance(decision_versions, list)
        assert len(fact_versions) > 0
        assert len(decision_versions) > 0
        
        # Should include all version types
        version_values = [v.value for v in PromptVersion]
        for version in fact_versions:
            assert version.value in version_values
    
    def test_get_available_versions_invalid_type(self):
        """Test getting available versions for invalid prompt type."""
        manager = PromptManager()
        
        invalid_versions = manager.get_available_versions("nonexistent_type")
        
        assert invalid_versions == []
    
    def test_compare_prompts_same_version(self):
        """Test comparing prompts of the same version."""
        manager = PromptManager()
        
        prompt1 = manager.get_fact_extraction_prompt(PromptVersion.V1_BASIC)
        prompt2 = manager.get_fact_extraction_prompt(PromptVersion.V1_BASIC)
        
        comparison = manager.compare_prompts(prompt1, prompt2)
        
        assert comparison["same_version"] is True
        assert comparison["same_template"] is True
        assert comparison["template_similarity"] == 1.0
    
    def test_compare_prompts_different_versions(self):
        """Test comparing prompts of different versions."""
        manager = PromptManager()
        
        prompt_v1 = manager.get_fact_extraction_prompt(PromptVersion.V1_BASIC)
        prompt_v3 = manager.get_fact_extraction_prompt(PromptVersion.V3_TECHNICAL)
        
        comparison = manager.compare_prompts(prompt_v1, prompt_v3)
        
        assert comparison["same_version"] is False
        assert comparison["version_1"] == PromptVersion.V1_BASIC.value
        assert comparison["version_2"] == PromptVersion.V3_TECHNICAL.value
        assert "template_similarity" in comparison
    
    def test_a_b_testing_support(self):
        """Test A/B testing functionality."""
        manager = PromptManager()
        
        # Test getting different versions for A/B testing
        version_a = PromptVersion.V1_BASIC
        version_b = PromptVersion.V3_TECHNICAL
        
        prompt_a = manager.get_fact_extraction_prompt(version_a)
        prompt_b = manager.get_fact_extraction_prompt(version_b)
        
        assert prompt_a.version != prompt_b.version
        assert prompt_a.template != prompt_b.template
        
        # Both should be valid for the same purpose
        assert "fact" in prompt_a.name.lower() or "extract" in prompt_a.name.lower()
        assert "fact" in prompt_b.name.lower() or "extract" in prompt_b.name.lower()
    
    def test_prompt_caching(self):
        """Test that prompts are cached efficiently."""
        manager = PromptManager()
        
        # Getting the same prompt multiple times should be efficient
        prompt1 = manager.get_fact_extraction_prompt(PromptVersion.V1_BASIC)
        prompt2 = manager.get_fact_extraction_prompt(PromptVersion.V1_BASIC)
        
        # Should return the same object (cached)
        assert prompt1 is prompt2
    
    def test_technical_domain_optimization(self):
        """Test that technical domain prompts are optimized."""
        manager = PromptManager()
        
        technical_prompt = manager.get_fact_extraction_prompt(PromptVersion.V3_TECHNICAL)
        
        # Technical prompts should contain domain-specific keywords
        template_lower = technical_prompt.template.lower()
        technical_keywords = ["code", "function", "api", "database", "algorithm", "technical", "implementation"]
        
        # At least some technical keywords should be present
        found_keywords = [keyword for keyword in technical_keywords if keyword in template_lower]
        assert len(found_keywords) > 0
    
    def test_prompt_examples_structure(self):
        """Test that prompt examples have proper structure."""
        manager = PromptManager()
        
        prompt = manager.get_fact_extraction_prompt()
        
        if prompt.examples:
            for example in prompt.examples:
                assert isinstance(example, dict)
                assert "input" in example or "output" in example
                
                if "input" in example and "output" in example:
                    assert isinstance(example["input"], str)
                    assert isinstance(example["output"], str)
                    assert len(example["input"]) > 0
                    assert len(example["output"]) > 0
    
    def test_prompt_variables_consistency(self):
        """Test that prompt variables are consistent with template."""
        manager = PromptManager()
        
        for prompt_type in ["fact_extraction", "decision"]:
            if prompt_type == "fact_extraction":
                prompt = manager.get_fact_extraction_prompt()
            else:
                prompt = manager.get_decision_prompt()
            
            # Check that all declared variables appear in template
            template_content = prompt.template
            for variable in prompt.variables:
                assert f"{{{variable}}}" in template_content
    
    def test_error_handling_invalid_template(self):
        """Test error handling for invalid template operations."""
        manager = PromptManager()
        
        # Test with malformed template
        invalid_template = PromptTemplate(
            name="invalid",
            version=PromptVersion.V1_BASIC,
            template="Template with {unclosed_variable",
            description="Invalid template",
            variables=["unclosed_variable"],
            examples=[]
        )
        
        variables = {"unclosed_variable": "test"}
        
        # Should handle gracefully or raise appropriate error
        with pytest.raises((KeyError, ValueError)):
            manager.format_prompt(invalid_template, variables)
    
    def test_performance_large_templates(self):
        """Test performance with large templates."""
        manager = PromptManager()
        
        # Create a large template
        large_content = "Process this: {text}\n" * 1000
        large_template = PromptTemplate(
            name="large_template",
            version=PromptVersion.V1_BASIC,
            template=large_content,
            description="Large template for performance testing",
            variables=["text"],
            examples=[]
        )
        
        variables = {"text": "test content"}
        
        import time
        start_time = time.time()
        formatted = manager.format_prompt(large_template, variables)
        end_time = time.time()
        
        # Should complete in reasonable time (less than 1 second)
        assert end_time - start_time < 1.0
        assert "test content" in formatted
        assert len(formatted) > len(large_content)