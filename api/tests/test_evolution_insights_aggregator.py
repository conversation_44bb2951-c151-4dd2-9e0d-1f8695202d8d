"""
Unit tests for the Evolution Insights Aggregation System.

Tests the daily aggregation system for evolution insights including:
- Daily insight data structure and validation
- Trend analysis calculations and accuracy
- Batch processing capabilities
- Learning efficiency computations
- Conflict resolution tracking
- Performance metrics aggregation
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import date, datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from decimal import Decimal

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from app.evolution.insights_aggregator import (
    EvolutionInsightsAggregator,
    DailyInsight,
    TrendAnalysis
)


class TestDailyInsight:
    """Test the DailyInsight dataclass."""
    
    def test_daily_insight_creation(self):
        """Test creating a daily insight with all fields."""
        insight_date = date(2024, 1, 15)
        
        insight = DailyInsight(
            user_id="user123",
            date=insight_date,
            total_operations=10,
            add_operations=4,
            update_operations=3,
            delete_operations=2,
            noop_operations=1,
            learning_efficiency=0.5,
            conflict_resolution_count=2,
            average_confidence=0.75,
            average_similarity=0.65
        )
        
        assert insight.user_id == "user123"
        assert insight.date == insight_date
        assert insight.total_operations == 10
        assert insight.add_operations == 4
        assert insight.update_operations == 3
        assert insight.delete_operations == 2
        assert insight.noop_operations == 1
        assert insight.learning_efficiency == 0.5
        assert insight.conflict_resolution_count == 2
        assert insight.average_confidence == 0.75
        assert insight.average_similarity == 0.65
    
    def test_daily_insight_operation_sum_validation(self):
        """Test that operation counts sum correctly."""
        insight = DailyInsight(
            user_id="user123",
            date=date.today(),
            total_operations=10,
            add_operations=4,
            update_operations=3,
            delete_operations=2,
            noop_operations=1,
            learning_efficiency=0.5,
            conflict_resolution_count=0,
            average_confidence=0.8,
            average_similarity=0.7
        )
        
        operation_sum = (insight.add_operations + insight.update_operations + 
                        insight.delete_operations + insight.noop_operations)
        
        assert operation_sum == insight.total_operations
    
    def test_daily_insight_default_values(self):
        """Test daily insight with minimal required fields."""
        insight = DailyInsight(
            user_id="test_user",
            date=date.today(),
            total_operations=0,
            add_operations=0,
            update_operations=0,
            delete_operations=0,
            noop_operations=0,
            learning_efficiency=0.0,
            conflict_resolution_count=0,
            average_confidence=0.0,
            average_similarity=0.0
        )
        
        assert insight.user_id == "test_user"
        assert insight.total_operations == 0
        assert insight.learning_efficiency == 0.0


class TestTrendAnalysis:
    """Test the TrendAnalysis dataclass."""
    
    def test_trend_analysis_creation(self):
        """Test creating a trend analysis with all fields."""
        trend = TrendAnalysis(
            user_id="user123",
            period_days=7,
            efficiency_trend=0.15,
            confidence_trend=0.05,
            operation_volume_trend=-0.10,
            quality_score=0.85
        )
        
        assert trend.user_id == "user123"
        assert trend.period_days == 7
        assert trend.efficiency_trend == 0.15
        assert trend.confidence_trend == 0.05
        assert trend.operation_volume_trend == -0.10
        assert trend.quality_score == 0.85
    
    def test_trend_analysis_positive_negative_trends(self):
        """Test trend analysis with positive and negative values."""
        # Positive trends
        positive_trend = TrendAnalysis(
            user_id="improving_user",
            period_days=30,
            efficiency_trend=0.25,
            confidence_trend=0.15,
            operation_volume_trend=0.30,
            quality_score=0.90
        )
        
        assert positive_trend.efficiency_trend > 0
        assert positive_trend.confidence_trend > 0
        assert positive_trend.operation_volume_trend > 0
        
        # Negative trends
        negative_trend = TrendAnalysis(
            user_id="declining_user",
            period_days=30,
            efficiency_trend=-0.15,
            confidence_trend=-0.05,
            operation_volume_trend=-0.20,
            quality_score=0.60
        )
        
        assert negative_trend.efficiency_trend < 0
        assert negative_trend.confidence_trend < 0
        assert negative_trend.operation_volume_trend < 0


class TestEvolutionInsightsAggregator:
    """Test the EvolutionInsightsAggregator class."""
    
    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        session = Mock(spec=Session)
        return session
    
    @pytest.fixture
    def aggregator(self, mock_db_session):
        """Create an aggregator instance with mocked database."""
        return EvolutionInsightsAggregator(mock_db_session)
    
    def test_aggregator_initialization(self, mock_db_session):
        """Test aggregator initialization."""
        aggregator = EvolutionInsightsAggregator(mock_db_session)
        
        assert aggregator.db_session == mock_db_session
        assert aggregator.batch_size == 100  # Assuming default batch size
    
    @pytest.mark.asyncio
    async def test_aggregate_daily_insights_single_user(self, aggregator, mock_db_session):
        """Test daily insights aggregation for a single user."""
        target_date = date(2024, 1, 15)
        user_id = "user123"
        
        # Mock database query results
        mock_operations = [
            Mock(user_id=user_id, operation_type='ADD', confidence=0.8, similarity_score=0.7),
            Mock(user_id=user_id, operation_type='UPDATE', confidence=0.9, similarity_score=0.8),
            Mock(user_id=user_id, operation_type='DELETE', confidence=0.75, similarity_score=0.6),
            Mock(user_id=user_id, operation_type='NOOP', confidence=0.6, similarity_score=0.5),
        ]
        
        # Mock the database query
        mock_result = Mock()
        mock_result.fetchall.return_value = mock_operations
        mock_db_session.execute.return_value = mock_result
        
        with patch.object(aggregator, '_calculate_learning_efficiency', return_value=0.5):
            with patch.object(aggregator, '_save_daily_insight') as mock_save:
                await aggregator.aggregate_daily_insights(target_date, user_id)
                
                # Verify save was called
                mock_save.assert_called_once()
                
                # Get the insight that was saved
                saved_insight = mock_save.call_args[0][0]
                assert isinstance(saved_insight, DailyInsight)
                assert saved_insight.user_id == user_id
                assert saved_insight.date == target_date
                assert saved_insight.total_operations == 4
                assert saved_insight.add_operations == 1
                assert saved_insight.update_operations == 1
                assert saved_insight.delete_operations == 1
                assert saved_insight.noop_operations == 1
    
    @pytest.mark.asyncio
    async def test_aggregate_daily_insights_all_users(self, aggregator, mock_db_session):
        """Test daily insights aggregation for all users."""
        target_date = date(2024, 1, 15)
        
        # Mock distinct users
        mock_users_result = Mock()
        mock_users_result.fetchall.return_value = [("user1",), ("user2",), ("user3",)]
        
        # Mock operations for each user
        mock_operations_result = Mock()
        mock_operations_result.fetchall.return_value = [
            Mock(user_id="user1", operation_type='ADD', confidence=0.8, similarity_score=0.7),
            Mock(user_id="user1", operation_type='UPDATE', confidence=0.9, similarity_score=0.8),
        ]
        
        mock_db_session.execute.side_effect = [mock_users_result, mock_operations_result, 
                                             mock_operations_result, mock_operations_result]
        
        with patch.object(aggregator, '_calculate_learning_efficiency', return_value=0.6):
            with patch.object(aggregator, '_save_daily_insight') as mock_save:
                result = await aggregator.aggregate_daily_insights(target_date)
                
                # Should save insights for each user
                assert mock_save.call_count == 3
                assert result == 3  # Number of users processed
    
    def test_calculate_learning_efficiency_normal_case(self, aggregator):
        """Test learning efficiency calculation with normal operations."""
        operations = [
            Mock(operation_type='ADD'),
            Mock(operation_type='UPDATE'), 
            Mock(operation_type='UPDATE'),
            Mock(operation_type='DELETE'),
            Mock(operation_type='NOOP'),
            Mock(operation_type='NOOP'),
        ]
        
        efficiency = aggregator._calculate_learning_efficiency(operations)
        
        # (2 updates + 1 delete) / 6 total = 0.5
        assert efficiency == 0.5
    
    def test_calculate_learning_efficiency_no_operations(self, aggregator):
        """Test learning efficiency calculation with no operations."""
        efficiency = aggregator._calculate_learning_efficiency([])
        assert efficiency == 0.0
    
    def test_calculate_learning_efficiency_only_adds(self, aggregator):
        """Test learning efficiency with only ADD operations."""
        operations = [
            Mock(operation_type='ADD'),
            Mock(operation_type='ADD'),
            Mock(operation_type='ADD'),
        ]
        
        efficiency = aggregator._calculate_learning_efficiency(operations)
        assert efficiency == 0.0  # No learning from existing memories
    
    def test_calculate_learning_efficiency_only_updates_deletes(self, aggregator):
        """Test learning efficiency with only UPDATE/DELETE operations."""
        operations = [
            Mock(operation_type='UPDATE'),
            Mock(operation_type='DELETE'),
            Mock(operation_type='UPDATE'),
        ]
        
        efficiency = aggregator._calculate_learning_efficiency(operations)
        assert efficiency == 1.0  # All operations show learning
    
    @pytest.mark.asyncio
    async def test_calculate_trend_analysis(self, aggregator, mock_db_session):
        """Test trend analysis calculation over a period."""
        user_id = "user123"
        period_days = 7
        end_date = date(2024, 1, 15)
        
        # Mock historical insights
        mock_insights = [
            Mock(date=end_date - timedelta(days=6), learning_efficiency=0.4, average_confidence=0.7, total_operations=10),
            Mock(date=end_date - timedelta(days=5), learning_efficiency=0.45, average_confidence=0.72, total_operations=12),
            Mock(date=end_date - timedelta(days=4), learning_efficiency=0.5, average_confidence=0.75, total_operations=8),
            Mock(date=end_date - timedelta(days=3), learning_efficiency=0.55, average_confidence=0.78, total_operations=15),
            Mock(date=end_date - timedelta(days=2), learning_efficiency=0.6, average_confidence=0.8, total_operations=11),
            Mock(date=end_date - timedelta(days=1), learning_efficiency=0.65, average_confidence=0.82, total_operations=9),
            Mock(date=end_date, learning_efficiency=0.7, average_confidence=0.85, total_operations=13),
        ]
        
        mock_result = Mock()
        mock_result.fetchall.return_value = mock_insights
        mock_db_session.execute.return_value = mock_result
        
        trend = await aggregator.calculate_trend_analysis(user_id, period_days, end_date)
        
        assert isinstance(trend, TrendAnalysis)
        assert trend.user_id == user_id
        assert trend.period_days == period_days
        assert trend.efficiency_trend > 0  # Efficiency is improving
        assert trend.confidence_trend > 0  # Confidence is improving
        # Operation volume trend calculation depends on implementation
    
    @pytest.mark.asyncio
    async def test_calculate_trend_analysis_insufficient_data(self, aggregator, mock_db_session):
        """Test trend analysis with insufficient historical data."""
        user_id = "new_user"
        period_days = 30
        end_date = date.today()
        
        # Mock insufficient data (less than 2 data points)
        mock_result = Mock()
        mock_result.fetchall.return_value = [
            Mock(date=end_date, learning_efficiency=0.5, average_confidence=0.7, total_operations=5)
        ]
        mock_db_session.execute.return_value = mock_result
        
        trend = await aggregator.calculate_trend_analysis(user_id, period_days, end_date)
        
        # Should return neutral trends for insufficient data
        assert trend.efficiency_trend == 0.0
        assert trend.confidence_trend == 0.0
        assert trend.operation_volume_trend == 0.0
    
    @pytest.mark.asyncio
    async def test_get_user_insights_summary(self, aggregator, mock_db_session):
        """Test getting user insights summary."""
        user_id = "user123"
        days = 30
        
        # Mock recent insights
        mock_insights = [
            Mock(date=date.today(), total_operations=10, learning_efficiency=0.6, 
                 average_confidence=0.8, conflict_resolution_count=2),
            Mock(date=date.today() - timedelta(days=1), total_operations=8, learning_efficiency=0.55,
                 average_confidence=0.75, conflict_resolution_count=1),
        ]
        
        mock_result = Mock()
        mock_result.fetchall.return_value = mock_insights
        mock_db_session.execute.return_value = mock_result
        
        with patch.object(aggregator, 'calculate_trend_analysis') as mock_trend:
            mock_trend.return_value = TrendAnalysis(
                user_id=user_id,
                period_days=days,
                efficiency_trend=0.1,
                confidence_trend=0.05,
                operation_volume_trend=0.2,
                quality_score=0.8
            )
            
            summary = await aggregator.get_user_insights_summary(user_id, days)
            
            assert "recent_insights" in summary
            assert "trend_analysis" in summary
            assert "total_operations" in summary
            assert "average_efficiency" in summary
            assert len(summary["recent_insights"]) == 2
    
    @pytest.mark.asyncio
    async def test_batch_processing_daily_insights(self, aggregator, mock_db_session):
        """Test batch processing of daily insights."""
        target_date = date(2024, 1, 15)
        
        # Mock many users
        users = [(f"user_{i}",) for i in range(250)]  # More than typical batch size
        mock_users_result = Mock()
        mock_users_result.fetchall.return_value = users
        
        # Mock operations
        mock_operations_result = Mock()
        mock_operations_result.fetchall.return_value = [
            Mock(user_id="user_1", operation_type='ADD', confidence=0.8, similarity_score=0.7)
        ]
        
        mock_db_session.execute.side_effect = [mock_users_result] + [mock_operations_result] * 250
        
        with patch.object(aggregator, '_calculate_learning_efficiency', return_value=0.5):
            with patch.object(aggregator, '_save_daily_insight') as mock_save:
                result = await aggregator.aggregate_daily_insights(target_date)
                
                # Should process all users
                assert result == 250
                assert mock_save.call_count == 250
    
    @pytest.mark.asyncio
    async def test_error_handling_database_error(self, aggregator, mock_db_session):
        """Test error handling for database errors."""
        target_date = date(2024, 1, 15)
        
        # Mock database error
        mock_db_session.execute.side_effect = SQLAlchemyError("Database connection failed")
        
        with pytest.raises(SQLAlchemyError):
            await aggregator.aggregate_daily_insights(target_date)
    
    def test_save_daily_insight(self, aggregator, mock_db_session):
        """Test saving daily insight to database."""
        insight = DailyInsight(
            user_id="user123",
            date=date(2024, 1, 15),
            total_operations=10,
            add_operations=4,
            update_operations=3,
            delete_operations=2,
            noop_operations=1,
            learning_efficiency=0.5,
            conflict_resolution_count=1,
            average_confidence=0.8,
            average_similarity=0.7
        )
        
        aggregator._save_daily_insight(insight)
        
        # Verify database operations
        mock_db_session.merge.assert_called_once()
        mock_db_session.commit.assert_called_once()
    
    def test_average_calculations(self, aggregator):
        """Test average calculations for confidence and similarity."""
        operations = [
            Mock(confidence=0.8, similarity_score=0.7),
            Mock(confidence=0.9, similarity_score=0.8),
            Mock(confidence=0.7, similarity_score=0.6),
            Mock(confidence=0.85, similarity_score=0.75),
        ]
        
        avg_confidence = aggregator._calculate_average_confidence(operations)
        avg_similarity = aggregator._calculate_average_similarity(operations)
        
        assert avg_confidence == 0.8125  # (0.8 + 0.9 + 0.7 + 0.85) / 4
        assert avg_similarity == 0.7125  # (0.7 + 0.8 + 0.6 + 0.75) / 4
    
    def test_average_calculations_empty_operations(self, aggregator):
        """Test average calculations with empty operations list."""
        avg_confidence = aggregator._calculate_average_confidence([])
        avg_similarity = aggregator._calculate_average_similarity([])
        
        assert avg_confidence == 0.0
        assert avg_similarity == 0.0
    
    @pytest.mark.asyncio
    async def test_quality_score_calculation(self, aggregator):
        """Test quality score calculation for trend analysis."""
        # Mock method to test quality score logic
        efficiency = 0.8
        confidence = 0.9
        operation_volume = 50
        conflicts = 2
        
        # This would be part of the internal quality score calculation
        quality_score = aggregator._calculate_quality_score(efficiency, confidence, operation_volume, conflicts)
        
        assert 0.0 <= quality_score <= 1.0
        assert isinstance(quality_score, float)
    
    @pytest.mark.asyncio
    async def test_concurrent_aggregation_safety(self, aggregator, mock_db_session):
        """Test that concurrent aggregation operations are safe."""
        target_date = date(2024, 1, 15)
        
        # Mock successful operations
        mock_users_result = Mock()
        mock_users_result.fetchall.return_value = [("user1",), ("user2",)]
        
        mock_operations_result = Mock()
        mock_operations_result.fetchall.return_value = [
            Mock(user_id="user1", operation_type='ADD', confidence=0.8, similarity_score=0.7)
        ]
        
        mock_db_session.execute.side_effect = [mock_users_result, mock_operations_result, mock_operations_result]
        
        with patch.object(aggregator, '_calculate_learning_efficiency', return_value=0.5):
            with patch.object(aggregator, '_save_daily_insight'):
                # Run multiple concurrent aggregations
                import asyncio
                results = await asyncio.gather(
                    aggregator.aggregate_daily_insights(target_date),
                    aggregator.aggregate_daily_insights(target_date),
                    return_exceptions=True
                )
                
                # Both should complete successfully or handle conflicts gracefully
                assert len(results) == 2