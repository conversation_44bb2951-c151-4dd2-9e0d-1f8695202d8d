"""
Database package initialization
Provides unified database access for Supabase PostgreSQL with enhanced connection pooling
"""
from .base import Base, get_db, get_engine, get_session_factory, _initialize_legacy_compatibility
from .service import db_service

# Initialize legacy compatibility on import
_initialize_legacy_compatibility()

# Legacy compatibility exports
engine = get_engine()
SessionLocal = get_session_factory()

__all__ = [
    'Base',
    'engine',
    'SessionLocal', 
    'get_db',
    'get_engine',
    'get_session_factory',
    'db_service'
]