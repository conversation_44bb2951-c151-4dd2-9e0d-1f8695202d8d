"""
Database base configuration
Uses enhanced connection pool manager for better performance and monitoring
"""
import os
from sqlalchemy.orm import declarative_base
from dotenv import load_dotenv

# load .env file
load_dotenv()

# Use Supabase PostgreSQL connection
DATABASE_URL = os.getenv("SUPABASE_DATABASE_URL")
if not DATABASE_URL:
    raise RuntimeError("SUPABASE_DATABASE_URL is not set in environment")

# Base class for models
Base = declarative_base()

# Import enhanced connection pool manager
# Note: Actual database connection and session management is now handled
# by the DatabasePoolManager in app.connection_pools.database_pool
# This provides better connection pooling, monitoring, and health checks

def get_db():
    """
    Legacy compatibility function.
    Actual implementation is in app.connection_pools.database_pool.get_db()
    """
    from app.connection_pools import get_db as enhanced_get_db
    return enhanced_get_db()

# Legacy compatibility - get engine and session factory from pool manager
def get_engine():
    """Get SQLAlchemy engine from pool manager"""
    from app.connection_pools import get_pool_manager
    return get_pool_manager().get_engine()

def get_session_factory():
    """Get SQLAlchemy session factory from pool manager"""
    from app.connection_pools import get_pool_manager
    return get_pool_manager().get_session_factory()

# For backward compatibility
engine = None
SessionLocal = None

def _initialize_legacy_compatibility():
    """Initialize legacy variables for backward compatibility"""
    global engine, SessionLocal
    if engine is None:
        engine = get_engine()
    if SessionLocal is None:
        SessionLocal = get_session_factory()