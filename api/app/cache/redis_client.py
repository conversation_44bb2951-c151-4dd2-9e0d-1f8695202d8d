"""
Redis client configuration and connection management
"""
import json
import logging
import pickle
from typing import Any, Optional, Union
from contextlib import asynccontextmanager

import redis.asyncio as redis
from redis.asyncio import ConnectionPool
from redis.exceptions import ConnectionError, TimeoutError, RedisError

from app.config import get_settings
from app.enhanced_logging import get_logger

logger = get_logger(__name__)


class RedisClient:
    """
    Redis client with connection pooling and error handling
    """
    
    def __init__(self):
        self._pool: Optional[ConnectionPool] = None
        self._client: Optional[redis.Redis] = None
        self.settings = get_settings()
        
    async def connect(self) -> None:
        """Initialize Redis connection pool"""
        try:
            # Create connection pool with optimal settings
            self._pool = ConnectionPool(
                host=self.settings.redis_host,
                port=self.settings.redis_port,
                password=getattr(self.settings, 'redis_password', None),
                db=getattr(self.settings, 'redis_db', 0),
                max_connections=getattr(self.settings, 'redis_max_connections', 20),
                retry_on_timeout=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                health_check_interval=30
            )
            
            self._client = redis.Redis(connection_pool=self._pool)
            
            # Test the connection
            await self._client.ping()
            logger.info("Redis connection established successfully")
            
        except (ConnectionError, TimeoutError) as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self._client = None
            self._pool = None
            raise
        except Exception as e:
            logger.error(f"Unexpected error connecting to Redis: {e}")
            self._client = None
            self._pool = None
            raise
    
    async def disconnect(self) -> None:
        """Close Redis connection"""
        if self._client:
            await self._client.aclose()
            self._client = None
        if self._pool:
            await self._pool.aclose()
            self._pool = None
        logger.info("Redis connection closed")
    
    @property
    def client(self) -> redis.Redis:
        """Get Redis client instance"""
        if not self._client:
            raise RuntimeError("Redis client not connected. Call connect() first.")
        return self._client
    
    @asynccontextmanager
    async def get_client(self):
        """Context manager for Redis client"""
        if not self._client:
            await self.connect()
        try:
            yield self._client
        except RedisError as e:
            logger.error(f"Redis operation error: {e}")
            raise
    
    async def is_connected(self) -> bool:
        """Check if Redis is connected and responsive"""
        try:
            if not self._client:
                return False
            await self._client.ping()
            return True
        except Exception:
            return False
    
    async def set_json(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """Set JSON-serializable value in Redis"""
        try:
            serialized = json.dumps(value)
            async with self.get_client() as client:
                await client.set(key, serialized, ex=expire)
            return True
        except (TypeError, RedisError) as e:
            logger.error(f"Failed to set JSON key {key}: {e}")
            return False
    
    async def get_json(self, key: str) -> Optional[Any]:
        """Get JSON value from Redis"""
        try:
            async with self.get_client() as client:
                value = await client.get(key)
            if value is None:
                return None
            return json.loads(value)
        except (json.JSONDecodeError, RedisError) as e:
            logger.error(f"Failed to get JSON key {key}: {e}")
            return None
    
    async def set_pickle(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """Set pickled value in Redis (for complex objects like numpy arrays)"""
        try:
            serialized = pickle.dumps(value)
            async with self.get_client() as client:
                await client.set(key, serialized, ex=expire)
            return True
        except (pickle.PickleError, RedisError) as e:
            logger.error(f"Failed to set pickle key {key}: {e}")
            return False
    
    async def get_pickle(self, key: str) -> Optional[Any]:
        """Get pickled value from Redis"""
        try:
            async with self.get_client() as client:
                value = await client.get(key)
            if value is None:
                return None
            return pickle.loads(value)
        except (pickle.PickleError, RedisError) as e:
            logger.error(f"Failed to get pickle key {key}: {e}")
            return None
    
    async def delete(self, *keys: str) -> int:
        """Delete keys from Redis"""
        try:
            async with self.get_client() as client:
                return await client.delete(*keys)
        except RedisError as e:
            logger.error(f"Failed to delete keys {keys}: {e}")
            return 0
    
    async def exists(self, *keys: str) -> int:
        """Check if keys exist in Redis"""
        try:
            async with self.get_client() as client:
                return await client.exists(*keys)
        except RedisError as e:
            logger.error(f"Failed to check existence of keys {keys}: {e}")
            return 0
    
    async def expire(self, key: str, seconds: int) -> bool:
        """Set expiration time for key"""
        try:
            async with self.get_client() as client:
                return await client.expire(key, seconds)
        except RedisError as e:
            logger.error(f"Failed to set expiration for key {key}: {e}")
            return False
    
    async def ttl(self, key: str) -> int:
        """Get time to live for key"""
        try:
            async with self.get_client() as client:
                return await client.ttl(key)
        except RedisError as e:
            logger.error(f"Failed to get TTL for key {key}: {e}")
            return -1
    
    async def keys(self, pattern: str = "*") -> list[str]:
        """Get keys matching pattern"""
        try:
            async with self.get_client() as client:
                keys = await client.keys(pattern)
                return [key.decode() if isinstance(key, bytes) else key for key in keys]
        except RedisError as e:
            logger.error(f"Failed to get keys for pattern {pattern}: {e}")
            return []
    
    async def flushdb(self) -> bool:
        """Clear current database"""
        try:
            async with self.get_client() as client:
                await client.flushdb()
            return True
        except RedisError as e:
            logger.error(f"Failed to flush database: {e}")
            return False


# Global Redis client instance
redis_client = RedisClient()