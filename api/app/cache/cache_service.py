"""
Cache service for embeddings, similarity calculations, and frequent queries
"""
import hashlib
import json
import time
from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta

import numpy as np

from app.config import get_settings
from app.enhanced_logging import get_logger
from .redis_client import redis_client

logger = get_logger(__name__)


class CacheService:
    """
    Service for caching embeddings, similarity calculations, and query results
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.client = redis_client
        
        # Cache key prefixes
        self.EMBEDDING_PREFIX = "emb:"
        self.SIMILARITY_PREFIX = "sim:"
        self.QUERY_PREFIX = "query:"
        self.STATS_PREFIX = "stats:"
        
        # Cache statistics
        self._stats = {
            "hits": 0,
            "misses": 0,
            "errors": 0,
            "total_operations": 0
        }
    
    def _generate_cache_key(self, prefix: str, *args) -> str:
        """Generate cache key from arguments"""
        key_data = "|".join(str(arg) for arg in args)
        hash_key = hashlib.md5(key_data.encode()).hexdigest()
        return f"{prefix}{hash_key}"
    
    def _get_ttl(self, cache_type: str) -> int:
        """Get TTL for different cache types"""
        ttl_map = {
            "embeddings": self.settings.cache_ttl_embeddings,
            "similarities": self.settings.cache_ttl_similarities,
            "queries": self.settings.cache_ttl_queries,
            "default": self.settings.cache_ttl_default
        }
        return ttl_map.get(cache_type, ttl_map["default"])
    
    async def _update_stats(self, operation: str) -> None:
        """Update cache statistics"""
        self._stats["total_operations"] += 1
        if operation in self._stats:
            self._stats[operation] += 1
        
        # Persist stats to Redis occasionally
        if self._stats["total_operations"] % 100 == 0:
            await self._persist_stats()
    
    async def _persist_stats(self) -> None:
        """Persist cache statistics to Redis"""
        try:
            stats_key = f"{self.STATS_PREFIX}cache_service"
            stats_data = {
                **self._stats,
                "last_updated": datetime.utcnow().isoformat()
            }
            await self.client.set_json(stats_key, stats_data, expire=86400)  # 24 hours
        except Exception as e:
            logger.error(f"Failed to persist cache stats: {e}")
    
    # Embedding caching methods
    async def get_embedding(self, text: str, model: str = "default") -> Optional[List[float]]:
        """Get cached embedding for text"""
        try:
            cache_key = self._generate_cache_key(self.EMBEDDING_PREFIX, text, model)
            result = await self.client.get_pickle(cache_key)
            
            if result is not None:
                await self._update_stats("hits")
                logger.debug(f"Cache hit for embedding: {cache_key[:16]}...")
                return result
            else:
                await self._update_stats("misses")
                logger.debug(f"Cache miss for embedding: {cache_key[:16]}...")
                return None
                
        except Exception as e:
            await self._update_stats("errors")
            logger.error(f"Error getting embedding from cache: {e}")
            return None
    
    async def set_embedding(self, text: str, embedding: List[float], model: str = "default") -> bool:
        """Cache embedding for text"""
        try:
            cache_key = self._generate_cache_key(self.EMBEDDING_PREFIX, text, model)
            ttl = self._get_ttl("embeddings")
            
            success = await self.client.set_pickle(cache_key, embedding, expire=ttl)
            if success:
                logger.debug(f"Cached embedding: {cache_key[:16]}...")
            return success
            
        except Exception as e:
            await self._update_stats("errors")
            logger.error(f"Error setting embedding in cache: {e}")
            return False
    
    # Similarity caching methods
    async def get_similarity_batch(self, query_embedding: List[float], 
                                 target_embeddings: List[Tuple[str, List[float]]]) -> Optional[List[Tuple[str, float]]]:
        """Get cached similarity calculations for a batch"""
        try:
            # Create hash for the query embedding and target IDs
            query_hash = hashlib.md5(str(query_embedding).encode()).hexdigest()[:8]
            target_ids = [target_id for target_id, _ in target_embeddings]
            targets_hash = hashlib.md5(str(sorted(target_ids)).encode()).hexdigest()[:8]
            
            cache_key = self._generate_cache_key(self.SIMILARITY_PREFIX, query_hash, targets_hash)
            result = await self.client.get_json(cache_key)
            
            if result is not None:
                await self._update_stats("hits")
                logger.debug(f"Cache hit for similarity batch: {cache_key[:16]}...")
                return [(item["id"], item["score"]) for item in result]
            else:
                await self._update_stats("misses")
                logger.debug(f"Cache miss for similarity batch: {cache_key[:16]}...")
                return None
                
        except Exception as e:
            await self._update_stats("errors")
            logger.error(f"Error getting similarity batch from cache: {e}")
            return None
    
    async def set_similarity_batch(self, query_embedding: List[float], 
                                 target_embeddings: List[Tuple[str, List[float]]], 
                                 similarities: List[Tuple[str, float]]) -> bool:
        """Cache similarity calculations for a batch"""
        try:
            # Create the same hash as in get_similarity_batch
            query_hash = hashlib.md5(str(query_embedding).encode()).hexdigest()[:8]
            target_ids = [target_id for target_id, _ in target_embeddings]
            targets_hash = hashlib.md5(str(sorted(target_ids)).encode()).hexdigest()[:8]
            
            cache_key = self._generate_cache_key(self.SIMILARITY_PREFIX, query_hash, targets_hash)
            ttl = self._get_ttl("similarities")
            
            # Store as list of dictionaries for JSON serialization
            cache_data = [{"id": sim_id, "score": score} for sim_id, score in similarities]
            
            success = await self.client.set_json(cache_key, cache_data, expire=ttl)
            if success:
                logger.debug(f"Cached similarity batch: {cache_key[:16]}...")
            return success
            
        except Exception as e:
            await self._update_stats("errors")
            logger.error(f"Error setting similarity batch in cache: {e}")
            return False
    
    # Query result caching methods
    async def get_query_result(self, query_type: str, **kwargs) -> Optional[Any]:
        """Get cached query result"""
        try:
            # Create cache key from query type and parameters
            params_str = json.dumps(kwargs, sort_keys=True)
            cache_key = self._generate_cache_key(self.QUERY_PREFIX, query_type, params_str)
            
            result = await self.client.get_json(cache_key)
            
            if result is not None:
                await self._update_stats("hits")
                logger.debug(f"Cache hit for query: {query_type}")
                return result
            else:
                await self._update_stats("misses")
                logger.debug(f"Cache miss for query: {query_type}")
                return None
                
        except Exception as e:
            await self._update_stats("errors")
            logger.error(f"Error getting query result from cache: {e}")
            return None
    
    async def set_query_result(self, query_type: str, result: Any, **kwargs) -> bool:
        """Cache query result"""
        try:
            # Create the same cache key as in get_query_result
            params_str = json.dumps(kwargs, sort_keys=True)
            cache_key = self._generate_cache_key(self.QUERY_PREFIX, query_type, params_str)
            ttl = self._get_ttl("queries")
            
            success = await self.client.set_json(cache_key, result, expire=ttl)
            if success:
                logger.debug(f"Cached query result: {query_type}")
            return success
            
        except Exception as e:
            await self._update_stats("errors")
            logger.error(f"Error setting query result in cache: {e}")
            return False
    
    # Cache management methods
    async def invalidate_pattern(self, pattern: str) -> int:
        """Invalidate all keys matching pattern"""
        try:
            keys = await self.client.keys(pattern)
            if keys:
                deleted = await self.client.delete(*keys)
                logger.info(f"Invalidated {deleted} cache keys matching pattern: {pattern}")
                return deleted
            return 0
        except Exception as e:
            logger.error(f"Error invalidating cache pattern {pattern}: {e}")
            return 0
    
    async def invalidate_user_cache(self, user_id: str) -> int:
        """Invalidate all cache entries for a specific user"""
        patterns = [
            f"{self.EMBEDDING_PREFIX}*{user_id}*",
            f"{self.SIMILARITY_PREFIX}*{user_id}*",
            f"{self.QUERY_PREFIX}*{user_id}*"
        ]
        
        total_deleted = 0
        for pattern in patterns:
            total_deleted += await self.invalidate_pattern(pattern)
        
        logger.info(f"Invalidated {total_deleted} cache entries for user: {user_id}")
        return total_deleted
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics"""
        await self._persist_stats()
        
        # Get Redis info
        redis_info = {}
        try:
            async with self.client.get_client() as client:
                info = await client.info("memory")
                redis_info = {
                    "used_memory": info.get("used_memory", 0),
                    "used_memory_human": info.get("used_memory_human", "0B"),
                    "maxmemory": info.get("maxmemory", 0),
                    "keyspace_hits": info.get("keyspace_hits", 0),
                    "keyspace_misses": info.get("keyspace_misses", 0)
                }
        except Exception as e:
            logger.error(f"Error getting Redis info: {e}")
        
        # Calculate hit rate
        total_ops = self._stats["hits"] + self._stats["misses"]
        hit_rate = (self._stats["hits"] / total_ops * 100) if total_ops > 0 else 0
        
        return {
            "cache_service_stats": self._stats,
            "hit_rate_percentage": round(hit_rate, 2),
            "redis_info": redis_info,
            "last_updated": datetime.utcnow().isoformat()
        }
    
    async def warm_up_cache(self, user_id: str) -> Dict[str, int]:
        """Pre-populate cache with commonly accessed data"""
        # This is a placeholder for cache warming logic
        # In practice, this would pre-load frequently accessed embeddings
        # and query results based on user patterns
        
        logger.info(f"Cache warm-up initiated for user: {user_id}")
        
        return {
            "embeddings_warmed": 0,
            "queries_warmed": 0,
            "time_taken_ms": 0
        }


# Global cache service instance
cache_service = CacheService()