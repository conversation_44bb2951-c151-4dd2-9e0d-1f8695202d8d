"""
Circuit Breaker package for fault tolerance and resilience.

This package provides circuit breaker implementations for protecting
external API calls and preventing cascading failures.
"""

from .api_circuit_breaker import APICircuitBreaker, CircuitState, CircuitBreakerMetrics
from aiobreaker import CircuitBreakerError
from .circuit_breaker_manager import (
    CircuitBreakerManager, 
    ServiceHealth,
    get_circuit_breaker_manager,
    initialize_circuit_breakers,
    shutdown_circuit_breakers
)

__all__ = [
    'APICircuitBreaker',
    'CircuitState', 
    'CircuitBreakerMetrics',
    'CircuitBreakerError',
    'CircuitBreakerManager',
    'ServiceHealth',
    'get_circuit_breaker_manager',
    'initialize_circuit_breakers',
    'shutdown_circuit_breakers'
] 