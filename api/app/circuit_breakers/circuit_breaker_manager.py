"""
Circuit Breaker Manager for coordinating multiple circuit breakers.

This module provides centralized management of circuit breakers across
different services and APIs, with health monitoring and metrics aggregation.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import httpx

from .api_circuit_breaker import APICircuitBreaker, CircuitState
from ..config import get_settings

# Set up logging
logger = logging.getLogger(__name__)


@dataclass
class ServiceHealth:
    """Health status for a service"""
    service_name: str
    is_healthy: bool
    last_check: datetime
    response_time: float
    error_message: Optional[str] = None


class CircuitBreakerManager:
    """
    Centralized manager for multiple circuit breakers.
    
    Features:
    - Register and manage multiple circuit breakers
    - Health monitoring across all services
    - Metrics aggregation and reporting
    - Graceful degradation coordination
    """
    
    def __init__(self):
        """Initialize Circuit Breaker Manager"""
        self.circuit_breakers: Dict[str, APICircuitBreaker] = {}
        self.health_checks: Dict[str, Callable] = {}
        self.service_health: Dict[str, ServiceHealth] = {}
        self._health_check_interval = 60  # seconds
        self._health_check_task: Optional[asyncio.Task] = None
        self._running = False
        
        logger.info("Circuit Breaker Manager initialized")
    
    def register_circuit_breaker(self, 
                                name: str, 
                                failure_threshold: Optional[int] = None,
                                recovery_timeout: Optional[int] = None,
                                half_open_max_calls: Optional[int] = None,
                                health_check_callback: Optional[Callable] = None) -> APICircuitBreaker:
        """
        Register a new circuit breaker.
        
        Args:
            name: Unique identifier for the circuit breaker
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Seconds before attempting recovery
            half_open_max_calls: Max calls allowed in half-open state
            health_check_callback: Optional health check function
            
        Returns:
            Configured APICircuitBreaker instance
        """
        if name in self.circuit_breakers:
            logger.warning(f"Circuit breaker '{name}' already exists, returning existing instance")
            return self.circuit_breakers[name]
        
        circuit_breaker = APICircuitBreaker(
            name=name,
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout,
            half_open_max_calls=half_open_max_calls,
            health_check_callback=health_check_callback
        )
        
        self.circuit_breakers[name] = circuit_breaker
        
        if health_check_callback:
            self.health_checks[name] = health_check_callback
        
        logger.info(f"Registered circuit breaker '{name}'")
        return circuit_breaker
    
    def get_circuit_breaker(self, name: str) -> Optional[APICircuitBreaker]:
        """Get circuit breaker by name"""
        return self.circuit_breakers.get(name)
    
    def remove_circuit_breaker(self, name: str) -> bool:
        """
        Remove circuit breaker by name.
        
        Args:
            name: Circuit breaker name to remove
            
        Returns:
            True if removed successfully, False if not found
        """
        if name in self.circuit_breakers:
            del self.circuit_breakers[name]
            if name in self.health_checks:
                del self.health_checks[name]
            if name in self.service_health:
                del self.service_health[name]
            logger.info(f"Removed circuit breaker '{name}'")
            return True
        return False
    
    async def call_with_circuit_breaker(self, name: str, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function with named circuit breaker protection.
        
        Args:
            name: Circuit breaker name
            func: Async function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            ValueError: If circuit breaker not found
            Exception: Any exception from the protected function
        """
        circuit_breaker = self.get_circuit_breaker(name)
        if not circuit_breaker:
            raise ValueError(f"Circuit breaker '{name}' not found")
        
        return await circuit_breaker.call(func, *args, **kwargs)
    
    async def start_health_monitoring(self):
        """Start background health monitoring for all registered services"""
        if self._running:
            logger.warning("Health monitoring already running")
            return
        
        self._running = True
        self._health_check_task = asyncio.create_task(self._health_monitor_loop())
        logger.info("Started health monitoring")
    
    async def stop_health_monitoring(self):
        """Stop background health monitoring"""
        self._running = False
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
            self._health_check_task = None
        logger.info("Stopped health monitoring")
    
    async def _health_monitor_loop(self):
        """Background loop for health checks"""
        while self._running:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self._health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitor loop: {e}")
                await asyncio.sleep(self._health_check_interval)
    
    async def _perform_health_checks(self):
        """Perform health checks for all registered services"""
        health_check_tasks = []
        
        for name, health_check_func in self.health_checks.items():
            task = asyncio.create_task(self._check_service_health(name, health_check_func))
            health_check_tasks.append(task)
        
        if health_check_tasks:
            await asyncio.gather(*health_check_tasks, return_exceptions=True)
    
    async def _check_service_health(self, name: str, health_check_func: Callable):
        """Check health of a specific service"""
        start_time = datetime.now()
        
        try:
            is_healthy = await asyncio.wait_for(health_check_func(), timeout=10.0)
            response_time = (datetime.now() - start_time).total_seconds()
            
            self.service_health[name] = ServiceHealth(
                service_name=name,
                is_healthy=bool(is_healthy),
                last_check=datetime.now(),
                response_time=response_time
            )
            
            logger.debug(f"Health check for '{name}': {'healthy' if is_healthy else 'unhealthy'} "
                        f"({response_time:.3f}s)")
            
        except asyncio.TimeoutError:
            response_time = (datetime.now() - start_time).total_seconds()
            self.service_health[name] = ServiceHealth(
                service_name=name,
                is_healthy=False,
                last_check=datetime.now(),
                response_time=response_time,
                error_message="Health check timeout"
            )
            logger.warning(f"Health check timeout for '{name}' after {response_time:.3f}s")
            
        except Exception as e:
            response_time = (datetime.now() - start_time).total_seconds()
            self.service_health[name] = ServiceHealth(
                service_name=name,
                is_healthy=False,
                last_check=datetime.now(),
                response_time=response_time,
                error_message=str(e)
            )
            logger.error(f"Health check failed for '{name}': {e}")
    
    def get_all_states(self) -> Dict[str, Dict[str, Any]]:
        """Get states of all circuit breakers"""
        return {name: cb.get_state() for name, cb in self.circuit_breakers.items()}
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get health summary for all services"""
        total_services = len(self.circuit_breakers)
        healthy_services = sum(1 for cb in self.circuit_breakers.values() if cb.is_healthy())
        
        return {
            'total_services': total_services,
            'healthy_services': healthy_services,
            'unhealthy_services': total_services - healthy_services,
            'overall_health_percentage': (healthy_services / total_services * 100) if total_services > 0 else 0,
            'last_check': datetime.now().isoformat(),
            'services': {
                name: {
                    'circuit_breaker_healthy': cb.is_healthy(),
                    'circuit_state': cb.get_state()['state'],
                    'failure_rate': cb.get_state()['failure_rate'],
                    'service_health': self.service_health.get(name, {})
                }
                for name, cb in self.circuit_breakers.items()
            }
        }
    
    def reset_all_circuit_breakers(self):
        """Reset all circuit breakers to closed state"""
        for name, circuit_breaker in self.circuit_breakers.items():
            try:
                circuit_breaker.reset()
                logger.info(f"Reset circuit breaker '{name}'")
            except Exception as e:
                logger.error(f"Failed to reset circuit breaker '{name}': {e}")
    
    def reset_circuit_breaker(self, name: str) -> bool:
        """
        Reset specific circuit breaker.
        
        Args:
            name: Circuit breaker name
            
        Returns:
            True if reset successfully, False if not found
        """
        circuit_breaker = self.get_circuit_breaker(name)
        if circuit_breaker:
            circuit_breaker.reset()
            return True
        return False
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get aggregated metrics across all circuit breakers"""
        total_requests = 0
        total_successful = 0
        total_failed = 0
        total_circuit_opens = 0
        
        for circuit_breaker in self.circuit_breakers.values():
            state = circuit_breaker.get_state()
            total_requests += state['total_requests']
            total_successful += state['successful_requests']
            total_failed += state['failed_requests']
            total_circuit_opens += state['circuit_open_count']
        
        overall_failure_rate = (total_failed / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'total_requests': total_requests,
            'total_successful_requests': total_successful,
            'total_failed_requests': total_failed,
            'overall_failure_rate': round(overall_failure_rate, 2),
            'total_circuit_opens': total_circuit_opens,
            'active_circuit_breakers': len(self.circuit_breakers),
            'timestamp': datetime.now().isoformat()
        }
    
    async def get_openai_health_check(self) -> bool:
        """Health check for OpenAI API"""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get("https://api.openai.com/v1/models", 
                                          headers={"Authorization": "Bearer dummy"})
                # Even with invalid auth, a 401 response means the service is up
                return response.status_code in [200, 401]
        except Exception:
            return False
    
    async def get_anthropic_health_check(self) -> bool:
        """Health check for Anthropic API"""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.post("https://api.anthropic.com/v1/messages",
                                           headers={"x-api-key": "dummy"})
                # Even with invalid auth, a 400/401 response means the service is up
                return response.status_code in [200, 400, 401]
        except Exception:
            return False
    
    async def get_redis_health_check(self) -> bool:
        """Health check for Redis"""
        try:
            from ..cache.redis_client import get_redis_client
            redis_client = get_redis_client()
            if redis_client:
                await redis_client.ping()
                return True
            return False
        except Exception:
            return False
    
    async def get_database_health_check(self) -> bool:
        """Health check for database"""
        try:
            from ..database import get_db
            # This would need to be adapted based on your database implementation
            # For now, just return True as a placeholder
            return True
        except Exception:
            return False
    
    def setup_default_circuit_breakers(self):
        """Set up default circuit breakers for common services"""
        # OpenAI API circuit breaker
        self.register_circuit_breaker(
            name="openai_api",
            failure_threshold=3,
            recovery_timeout=30,
            health_check_callback=self.get_openai_health_check
        )
        
        # Anthropic API circuit breaker
        self.register_circuit_breaker(
            name="anthropic_api",
            failure_threshold=3,
            recovery_timeout=30,
            health_check_callback=self.get_anthropic_health_check
        )
        
        # Redis circuit breaker
        self.register_circuit_breaker(
            name="redis",
            failure_threshold=5,
            recovery_timeout=15,
            health_check_callback=self.get_redis_health_check
        )
        
        # Database circuit breaker
        self.register_circuit_breaker(
            name="database",
            failure_threshold=5,
            recovery_timeout=20,
            health_check_callback=self.get_database_health_check
        )
        
        logger.info("Set up default circuit breakers for OpenAI, Anthropic, Redis, and Database")
    
    def __str__(self) -> str:
        return f"CircuitBreakerManager({len(self.circuit_breakers)} circuit breakers)"
    
    def __repr__(self) -> str:
        return f"CircuitBreakerManager(circuit_breakers={list(self.circuit_breakers.keys())})"


# Global circuit breaker manager instance
_circuit_breaker_manager: Optional[CircuitBreakerManager] = None


def get_circuit_breaker_manager() -> CircuitBreakerManager:
    """Get global circuit breaker manager instance"""
    global _circuit_breaker_manager
    if _circuit_breaker_manager is None:
        _circuit_breaker_manager = CircuitBreakerManager()
        _circuit_breaker_manager.setup_default_circuit_breakers()
    return _circuit_breaker_manager


async def initialize_circuit_breakers():
    """Initialize circuit breaker manager with health monitoring"""
    manager = get_circuit_breaker_manager()
    await manager.start_health_monitoring()
    logger.info("Circuit breaker system initialized")


async def shutdown_circuit_breakers():
    """Shutdown circuit breaker manager"""
    global _circuit_breaker_manager
    if _circuit_breaker_manager:
        await _circuit_breaker_manager.stop_health_monitoring()
        logger.info("Circuit breaker system shutdown") 