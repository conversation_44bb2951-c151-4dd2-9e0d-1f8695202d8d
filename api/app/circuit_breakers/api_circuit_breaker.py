"""
API Circuit Breaker implementation for protecting external API calls.

This module provides a comprehensive circuit breaker solution that monitors
external API health and prevents cascading failures.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
import httpx
import aiobreaker
from aiobreaker import CircuitBreaker, CircuitBreakerError

from ..config import get_settings

# Set up logging
logger = logging.getLogger(__name__)


class CircuitState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


@dataclass
class CircuitBreakerMetrics:
    """Metrics for circuit breaker monitoring"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    circuit_open_count: int = 0
    circuit_half_open_count: int = 0
    last_failure_time: Optional[datetime] = None
    last_success_time: Optional[datetime] = None
    current_state: CircuitState = CircuitState.CLOSED
    failure_rate: float = 0.0
    average_response_time: float = 0.0
    response_times: List[float] = field(default_factory=list)


class APICircuitBreaker:
    """
    Enhanced Circuit Breaker for API calls with monitoring and metrics.
    
    Features:
    - Automatic failure detection and recovery
    - Health check callbacks
    - Comprehensive metrics tracking
    - Configurable failure thresholds
    - Graceful degradation support
    """
    
    def __init__(self, 
                 name: str, 
                 failure_threshold: Optional[int] = None,
                 recovery_timeout: Optional[int] = None,
                 half_open_max_calls: Optional[int] = None,
                 health_check_callback: Optional[Callable] = None):
        """
        Initialize API Circuit Breaker.
        
        Args:
            name: Circuit breaker identifier
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Seconds before attempting recovery
            half_open_max_calls: Max calls allowed in half-open state
            health_check_callback: Optional health check function
        """
        settings = get_settings()
        
        self.name = name
        self.failure_threshold = failure_threshold or settings.circuit_breaker_failure_threshold
        self.recovery_timeout = recovery_timeout or settings.circuit_breaker_recovery_timeout
        self.half_open_max_calls = half_open_max_calls or settings.circuit_breaker_half_open_max_calls
        self.health_check_callback = health_check_callback
        
        # Initialize metrics
        self.metrics = CircuitBreakerMetrics()
        
        # Create aiobreaker instance
        from datetime import timedelta
        self.circuit_breaker = CircuitBreaker(
            fail_max=self.failure_threshold,
            timeout_duration=timedelta(seconds=self.recovery_timeout),
            exclude=self._get_expected_exceptions()
        )
        
        # Set up event listeners for state changes
        self._setup_event_listeners()
        
        logger.info(f"Initialized circuit breaker '{name}' with failure_threshold={self.failure_threshold}, "
                   f"recovery_timeout={self.recovery_timeout}s")
    
    def _get_expected_exceptions(self) -> tuple:
        """Get list of exceptions that should trigger circuit breaker"""
        return (
            httpx.HTTPError,
            httpx.RequestError,
            httpx.TimeoutException,
            httpx.ConnectError,
            httpx.ProxyError,
            httpx.ProtocolError,
            httpx.DecodingError,
            ConnectionError,
            TimeoutError,
            OSError,
            Exception  # Catch-all for unexpected errors
        )
    
    def _setup_event_listeners(self):
        """Set up event listeners for circuit breaker state changes"""
        
        def on_circuit_open(prev_state, new_state, exception):
            self.metrics.circuit_open_count += 1
            self.metrics.current_state = CircuitState.OPEN
            self.metrics.last_failure_time = datetime.now()
            logger.warning(f"Circuit breaker '{self.name}' opened after {self.failure_threshold} failures. "
                          f"Last exception: {exception}")
        
        def on_circuit_half_open(prev_state, new_state):
            self.metrics.circuit_half_open_count += 1
            self.metrics.current_state = CircuitState.HALF_OPEN
            logger.info(f"Circuit breaker '{self.name}' entered half-open state for testing")
        
        def on_circuit_close(prev_state, new_state):
            self.metrics.current_state = CircuitState.CLOSED
            logger.info(f"Circuit breaker '{self.name}' closed - service recovered")
        
        # Register listeners
        self.circuit_breaker.add_listener(on_circuit_open)
        self.circuit_breaker.add_listener(on_circuit_half_open)
        self.circuit_breaker.add_listener(on_circuit_close)
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function with circuit breaker protection.
        
        Args:
            func: Async function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            CircuitBreakerError: When circuit is open
            Exception: Any exception from the protected function
        """
        start_time = datetime.now()
        
        try:
            # Check if circuit breaker allows the call
            if self.circuit_breaker.current_state == 'open':
                self.metrics.total_requests += 1
                raise CircuitBreakerError(f"Circuit breaker '{self.name}' is open")
            
            # Execute the protected function
            result = await self._execute_with_circuit_breaker(func, *args, **kwargs)
            
            # Record successful call
            self._record_success(start_time)
            return result
            
        except CircuitBreakerError:
            # Circuit breaker prevented the call
            raise
        except Exception as e:
            # Function failed - record failure
            self._record_failure(start_time, e)
            raise
    
    async def _execute_with_circuit_breaker(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with aiobreaker protection"""
        @self.circuit_breaker
        async def protected_call():
            return await func(*args, **kwargs)
        
        return await protected_call()
    
    def _record_success(self, start_time: datetime):
        """Record successful API call"""
        response_time = (datetime.now() - start_time).total_seconds()
        
        self.metrics.total_requests += 1
        self.metrics.successful_requests += 1
        self.metrics.last_success_time = datetime.now()
        self.metrics.response_times.append(response_time)
        
        # Keep only last 100 response times for average calculation
        if len(self.metrics.response_times) > 100:
            self.metrics.response_times = self.metrics.response_times[-100:]
        
        # Update average response time
        if self.metrics.response_times:
            self.metrics.average_response_time = sum(self.metrics.response_times) / len(self.metrics.response_times)
        
        # Update failure rate
        self._update_failure_rate()
        
        logger.debug(f"Circuit breaker '{self.name}' - successful call in {response_time:.3f}s")
    
    def _record_failure(self, start_time: datetime, exception: Exception):
        """Record failed API call"""
        response_time = (datetime.now() - start_time).total_seconds()
        
        self.metrics.total_requests += 1
        self.metrics.failed_requests += 1
        self.metrics.last_failure_time = datetime.now()
        
        # Update failure rate
        self._update_failure_rate()
        
        logger.warning(f"Circuit breaker '{self.name}' - failed call in {response_time:.3f}s: {exception}")
    
    def _update_failure_rate(self):
        """Update current failure rate"""
        if self.metrics.total_requests > 0:
            self.metrics.failure_rate = self.metrics.failed_requests / self.metrics.total_requests
    
    async def health_check(self) -> bool:
        """
        Perform health check on the protected service.
        
        Returns:
            True if service is healthy, False otherwise
        """
        try:
            if self.health_check_callback:
                return await self.health_check_callback()
            
            # Default health check - just check if circuit is closed
            return self.circuit_breaker.current_state == 'closed'
            
        except Exception as e:
            logger.error(f"Health check failed for circuit breaker '{self.name}': {e}")
            return False
    
    def reset(self):
        """Reset circuit breaker to closed state"""
        try:
            # Reset aiobreaker state
            self.circuit_breaker._reset()
            
            # Reset metrics
            self.metrics = CircuitBreakerMetrics()
            
            logger.info(f"Circuit breaker '{self.name}' manually reset")
            
        except Exception as e:
            logger.error(f"Failed to reset circuit breaker '{self.name}': {e}")
    
    def get_state(self) -> Dict[str, Any]:
        """Get current circuit breaker state and metrics"""
        return {
            'name': self.name,
            'state': self.metrics.current_state.value,
            'total_requests': self.metrics.total_requests,
            'successful_requests': self.metrics.successful_requests,
            'failed_requests': self.metrics.failed_requests,
            'failure_rate': round(self.metrics.failure_rate * 100, 2),
            'average_response_time': round(self.metrics.average_response_time, 3),
            'circuit_open_count': self.metrics.circuit_open_count,
            'circuit_half_open_count': self.metrics.circuit_half_open_count,
            'last_failure_time': self.metrics.last_failure_time.isoformat() if self.metrics.last_failure_time else None,
            'last_success_time': self.metrics.last_success_time.isoformat() if self.metrics.last_success_time else None,
            'failure_threshold': self.failure_threshold,
            'recovery_timeout': self.recovery_timeout
        }
    
    def is_healthy(self) -> bool:
        """Check if circuit breaker is in a healthy state"""
        return (self.metrics.current_state == CircuitState.CLOSED and 
                self.metrics.failure_rate < 0.5)  # Less than 50% failure rate
    
    def __str__(self) -> str:
        return f"APICircuitBreaker(name='{self.name}', state={self.metrics.current_state.value})"
    
    def __repr__(self) -> str:
        return (f"APICircuitBreaker(name='{self.name}', state={self.metrics.current_state.value}, "
                f"failure_rate={self.metrics.failure_rate:.2%})") 