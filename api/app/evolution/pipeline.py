"""
Evolution Pipeline Coordinator for Batch Processing

This module provides the EvolutionPipeline class that coordinates batch processing
across all evolution components (fact extraction, similarity analysis, decision engine).
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from .fact_extractor import FactExtractor, ExtractionContext, FactExtractionResponse
from .similarity_analyzer import Similarity<PERSON><PERSON>y<PERSON>, SimilarityResult
from .decision_engine import EvolutionDecisionEngine, EvolutionDecision
from .evolution_tracker import EvolutionTracker
from ..database.service import DatabaseService

logger = logging.getLogger(__name__)

@dataclass
class PipelineInput:
    """Input for evolution pipeline processing."""
    user_id: str
    context: ExtractionContext
    existing_memories: List[Dict[str, Any]]
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class PipelineResult:
    """Result from evolution pipeline processing."""
    user_id: str
    decisions: List[EvolutionDecision]
    processing_time_ms: float
    success: bool
    error: Optional[str] = None
    partial_success: bool = False
    completed_operations: int = 0
    total_operations: int = 0

@dataclass
class BatchPipelineResult:
    """Result from batch pipeline processing."""
    results: List[PipelineResult]
    total_processing_time_ms: float
    success_count: int
    error_count: int
    partial_success_count: int
    batch_metrics: Dict[str, Any]

class EvolutionPipeline:
    """
    Coordinates batch processing across all evolution components.
    
    Provides end-to-end pipeline orchestration with error handling,
    partial success support, and comprehensive metrics tracking.
    """
    
    def __init__(
        self,
        fact_extractor: FactExtractor,
        similarity_analyzer: SimilarityAnalyzer,
        decision_engine: EvolutionDecisionEngine,
        evolution_tracker: EvolutionTracker,
        database_service: DatabaseService,
        max_concurrent_pipelines: int = 10
    ):
        self.fact_extractor = fact_extractor
        self.similarity_analyzer = similarity_analyzer
        self.decision_engine = decision_engine
        self.evolution_tracker = evolution_tracker
        self.database_service = database_service
        self.max_concurrent_pipelines = max_concurrent_pipelines
        
        # Pipeline metrics
        self._pipeline_stats = {
            "total_processed": 0,
            "successful_pipelines": 0,
            "failed_pipelines": 0,
            "partial_success_pipelines": 0,
            "avg_processing_time_ms": 0.0
        }
    
    async def process_single(
        self,
        pipeline_input: PipelineInput,
        model: str = "claude-3-5-sonnet-20241022",
        timeout_seconds: int = 30
    ) -> PipelineResult:
        """
        Process a single evolution pipeline.
        
        Args:
            pipeline_input: Input context for evolution processing
            model: LLM model to use for fact extraction
            timeout_seconds: Maximum processing time
            
        Returns:
            PipelineResult with decisions and metrics
        """
        start_time = datetime.now()
        
        try:
            # Phase 1: Fact Extraction
            extraction_response = await asyncio.wait_for(
                self.fact_extractor.extract_facts(pipeline_input.context, model),
                timeout=timeout_seconds
            )
            
            if not extraction_response.facts:
                return PipelineResult(
                    user_id=pipeline_input.user_id,
                    decisions=[],
                    processing_time_ms=0.0,
                    success=True,
                    completed_operations=0,
                    total_operations=0
                )
            
            # Phase 2: Similarity Analysis
            similarity_tasks = [
                self.similarity_analyzer.find_similar_memories(
                    fact.content, 
                    pipeline_input.existing_memories,
                    user_id=pipeline_input.user_id
                )
                for fact in extraction_response.facts
            ]
            
            similarity_results = await asyncio.gather(*similarity_tasks, return_exceptions=True)
            
            # Phase 3: Decision Making
            decisions = []
            successful_operations = 0
            
            for i, fact in enumerate(extraction_response.facts):
                try:
                    if isinstance(similarity_results[i], Exception):
                        logger.error(f"Similarity analysis failed for fact {i}: {similarity_results[i]}")
                        continue
                    
                    decision = await self.decision_engine.decide_operation(
                        fact.content,
                        similarity_results[i],
                        pipeline_input.user_id
                    )
                    
                    decisions.append(decision)
                    successful_operations += 1
                    
                except Exception as e:
                    logger.error(f"Decision making failed for fact {i}: {e}")
                    continue
            
            # Calculate processing time
            processing_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            
            # Determine success status
            total_operations = len(extraction_response.facts)
            success = successful_operations == total_operations
            partial_success = 0 < successful_operations < total_operations
            
            return PipelineResult(
                user_id=pipeline_input.user_id,
                decisions=decisions,
                processing_time_ms=processing_time_ms,
                success=success,
                partial_success=partial_success,
                completed_operations=successful_operations,
                total_operations=total_operations
            )
            
        except asyncio.TimeoutError:
            processing_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            return PipelineResult(
                user_id=pipeline_input.user_id,
                decisions=[],
                processing_time_ms=processing_time_ms,
                success=False,
                error="Pipeline processing timeout"
            )
            
        except Exception as e:
            processing_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            logger.error(f"Pipeline processing failed: {e}")
            return PipelineResult(
                user_id=pipeline_input.user_id,
                decisions=[],
                processing_time_ms=processing_time_ms,
                success=False,
                error=str(e)
            )
    
    async def process_batch(
        self,
        pipeline_inputs: List[PipelineInput],
        model: str = "claude-3-5-sonnet-20241022",
        timeout_seconds: int = 30
    ) -> BatchPipelineResult:
        """
        Process multiple evolution pipelines concurrently.
        
        Args:
            pipeline_inputs: List of pipeline inputs to process
            model: LLM model to use for fact extraction
            timeout_seconds: Maximum processing time per pipeline
            
        Returns:
            BatchPipelineResult with all results and batch metrics
        """
        if not pipeline_inputs:
            return BatchPipelineResult(
                results=[],
                total_processing_time_ms=0.0,
                success_count=0,
                error_count=0,
                partial_success_count=0,
                batch_metrics={}
            )
        
        batch_start_time = datetime.now()
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(self.max_concurrent_pipelines)
        
        async def process_with_semaphore(pipeline_input: PipelineInput) -> PipelineResult:
            async with semaphore:
                return await self.process_single(pipeline_input, model, timeout_seconds)
        
        # Process all pipelines concurrently
        try:
            results = await asyncio.gather(
                *[process_with_semaphore(input_item) for input_item in pipeline_inputs],
                return_exceptions=True
            )
            
            # Convert exceptions to error results
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Pipeline {i} failed with exception: {result}")
                    processed_results.append(PipelineResult(
                        user_id=pipeline_inputs[i].user_id,
                        decisions=[],
                        processing_time_ms=0.0,
                        success=False,
                        error=str(result)
                    ))
                else:
                    processed_results.append(result)
            
            # Calculate batch metrics
            total_processing_time_ms = (datetime.now() - batch_start_time).total_seconds() * 1000
            success_count = sum(1 for r in processed_results if r.success)
            error_count = sum(1 for r in processed_results if not r.success and not r.partial_success)
            partial_success_count = sum(1 for r in processed_results if r.partial_success)
            
            # Update pipeline statistics
            self._update_pipeline_stats(processed_results, total_processing_time_ms)
            
            batch_metrics = {
                "batch_size": len(pipeline_inputs),
                "concurrent_limit": self.max_concurrent_pipelines,
                "avg_pipeline_time_ms": sum(r.processing_time_ms for r in processed_results) / len(processed_results),
                "success_rate": success_count / len(processed_results),
                "error_rate": error_count / len(processed_results),
                "partial_success_rate": partial_success_count / len(processed_results)
            }
            
            return BatchPipelineResult(
                results=processed_results,
                total_processing_time_ms=total_processing_time_ms,
                success_count=success_count,
                error_count=error_count,
                partial_success_count=partial_success_count,
                batch_metrics=batch_metrics
            )
            
        except Exception as e:
            total_processing_time_ms = (datetime.now() - batch_start_time).total_seconds() * 1000
            logger.error(f"Batch processing failed: {e}")
            
            # Return error results for all inputs
            error_results = [
                PipelineResult(
                    user_id=input_item.user_id,
                    decisions=[],
                    processing_time_ms=0.0,
                    success=False,
                    error=f"Batch processing error: {str(e)}"
                )
                for input_item in pipeline_inputs
            ]
            
            return BatchPipelineResult(
                results=error_results,
                total_processing_time_ms=total_processing_time_ms,
                success_count=0,
                error_count=len(pipeline_inputs),
                partial_success_count=0,
                batch_metrics={"batch_error": str(e)}
            )
    
    async def process_and_persist_batch(
        self,
        pipeline_inputs: List[PipelineInput],
        model: str = "claude-3-5-sonnet-20241022",
        timeout_seconds: int = 30
    ) -> BatchPipelineResult:
        """
        Process batch and persist evolution operations to database.
        
        Args:
            pipeline_inputs: List of pipeline inputs to process
            model: LLM model to use for fact extraction
            timeout_seconds: Maximum processing time per pipeline
            
        Returns:
            BatchPipelineResult with persistence status
        """
        # Process the batch
        batch_result = await self.process_batch(pipeline_inputs, model, timeout_seconds)
        
        # Collect all decisions for batch persistence
        all_decisions = []
        for result in batch_result.results:
            if result.decisions:
                all_decisions.extend(result.decisions)
        
        # Persist decisions if any were made
        if all_decisions:
            try:
                await self._persist_decisions_batch(all_decisions)
                logger.info(f"Successfully persisted {len(all_decisions)} evolution decisions")
            except Exception as e:
                logger.error(f"Failed to persist evolution decisions: {e}")
                # Update results to reflect persistence failure
                for result in batch_result.results:
                    if result.success and result.decisions:
                        result.success = False
                        result.partial_success = True
                        result.error = f"Processing succeeded but persistence failed: {str(e)}"
        
        return batch_result
    
    async def _persist_decisions_batch(self, decisions: List[EvolutionDecision]) -> None:
        """Persist evolution decisions in batch to the database."""
        if not decisions:
            return
        
        # Group decisions by user for batch insertion
        user_decisions = {}
        for decision in decisions:
            user_id = decision.user_id
            if user_id not in user_decisions:
                user_decisions[user_id] = []
            user_decisions[user_id].append(decision)
        
        # Persist each user's decisions as a batch
        for user_id, user_decision_list in user_decisions.items():
            await self.evolution_tracker.track_operations_batch(user_decision_list, user_id)
    
    def _update_pipeline_stats(self, results: List[PipelineResult], total_time_ms: float) -> None:
        """Update internal pipeline statistics."""
        self._pipeline_stats["total_processed"] += len(results)
        self._pipeline_stats["successful_pipelines"] += sum(1 for r in results if r.success)
        self._pipeline_stats["failed_pipelines"] += sum(1 for r in results if not r.success and not r.partial_success)
        self._pipeline_stats["partial_success_pipelines"] += sum(1 for r in results if r.partial_success)
        
        # Update average processing time
        total_processed = self._pipeline_stats["total_processed"]
        current_avg = self._pipeline_stats["avg_processing_time_ms"]
        new_avg_time = sum(r.processing_time_ms for r in results) / len(results)
        
        # Weighted average calculation
        self._pipeline_stats["avg_processing_time_ms"] = (
            (current_avg * (total_processed - len(results)) + new_avg_time * len(results)) / total_processed
        )
    
    def get_pipeline_stats(self) -> Dict[str, Any]:
        """Get comprehensive pipeline statistics."""
        return {
            **self._pipeline_stats,
            "configuration": {
                "max_concurrent_pipelines": self.max_concurrent_pipelines,
                "components_health_status": "healthy"  # This could be enhanced with actual health checks
            }
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all pipeline components."""
        health_status = {"status": "healthy", "components": {}}
        
        try:
            # Check fact extractor
            fact_health = await self.fact_extractor.health_check()
            health_status["components"]["fact_extractor"] = fact_health
            
            # Check similarity analyzer
            similarity_health = await self.similarity_analyzer.health_check()
            health_status["components"]["similarity_analyzer"] = similarity_health
            
            # Check decision engine
            decision_health = await self.decision_engine.health_check()
            health_status["components"]["decision_engine"] = decision_health
            
            # Determine overall health
            component_statuses = [
                fact_health.get("status", "unhealthy"),
                similarity_health.get("status", "unhealthy"),
                decision_health.get("status", "unhealthy")
            ]
            
            if "unhealthy" in component_statuses:
                health_status["status"] = "unhealthy"
            elif "degraded" in component_statuses:
                health_status["status"] = "degraded"
                
        except Exception as e:
            health_status["status"] = "unhealthy"
            health_status["error"] = str(e)
        
        return health_status