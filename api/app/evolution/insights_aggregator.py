"""
Evolution Insights Aggregation System for Memory Master v2.

Implements daily aggregation system for evolution insights including learning efficiency
calculations, trend analysis, and batch processing capabilities.
"""

import logging
import asyncio
import datetime
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

from sqlalchemy.orm import Session
from sqlalchemy import text, func, and_, or_
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)

@dataclass
class DailyInsight:
    """Daily evolution insight data."""
    user_id: str
    date: datetime.date
    total_operations: int
    add_operations: int
    update_operations: int
    delete_operations: int
    noop_operations: int
    learning_efficiency: float
    conflict_resolution_count: int
    average_confidence: float
    average_similarity: float

@dataclass
class TrendAnalysis:
    """Trend analysis results."""
    user_id: str
    period_days: int
    efficiency_trend: float  # Change in learning efficiency
    confidence_trend: float  # Change in average confidence
    operation_volume_trend: float  # Change in operation volume
    quality_score: float  # Overall memory quality score

class EvolutionInsightsAggregator:
    """
    Daily aggregation system for evolution insights.
    
    Features:
    - Daily batch aggregation of evolution operations
    - Learning efficiency calculations
    - Trend analysis for memory quality improvements
    - PostgreSQL window functions for efficient aggregation
    - Data validation and error handling
    - Batch job scheduling and monitoring
    """
    
    def __init__(self, db_session: Session):
        """Initialize insights aggregator."""
        self.db_session = db_session
        
        logger.info("EvolutionInsightsAggregator initialized")
    
    async def aggregate_daily_insights(
        self,
        target_date: Optional[datetime.date] = None,
        user_id: Optional[str] = None
    ) -> int:
        """
        Aggregate evolution insights for a specific date.
        
        Args:
            target_date: Date to aggregate (defaults to yesterday)
            user_id: Specific user to aggregate (None for all users)
            
        Returns:
            Number of user insights aggregated
        """
        if target_date is None:
            target_date = datetime.date.today() - datetime.timedelta(days=1)
        
        logger.info(f"Starting daily insights aggregation for {target_date}")
        
        try:
            # Get list of users with operations on target date
            users_query = text("""
                SELECT DISTINCT user_id
                FROM memory_master.evolution_operations
                WHERE DATE(created_at) = :target_date
            """)
            
            if user_id:
                users_query = text("""
                    SELECT DISTINCT user_id
                    FROM memory_master.evolution_operations
                    WHERE DATE(created_at) = :target_date AND user_id = :user_id
                """)
            
            params = {"target_date": target_date}
            if user_id:
                params["user_id"] = user_id
            
            users_result = self.db_session.execute(users_query, params)
            user_ids = [row[0] for row in users_result.fetchall()]
            
            logger.info(f"Found {len(user_ids)} users with operations on {target_date}")
            
            aggregated_count = 0
            
            for uid in user_ids:
                try:
                    insight = await self._aggregate_user_daily_insight(
                        user_id=str(uid),
                        target_date=target_date
                    )
                    
                    if insight:
                        await self._store_daily_insight(insight)
                        aggregated_count += 1
                        logger.debug(f"Aggregated insights for user {uid}")
                    
                except Exception as user_error:
                    logger.error(f"Error aggregating insights for user {uid}: {user_error}")
                    continue
            
            logger.info(f"Completed daily insights aggregation: {aggregated_count} users processed")
            return aggregated_count
            
        except Exception as e:
            logger.error(f"Error in daily insights aggregation: {e}")
            self.db_session.rollback()
            return 0
    
    async def _aggregate_user_daily_insight(
        self,
        user_id: str,
        target_date: datetime.date
    ) -> Optional[DailyInsight]:
        """
        Aggregate insights for a specific user and date.
        
        Args:
            user_id: User identifier
            target_date: Date to aggregate
            
        Returns:
            DailyInsight object or None if no data
        """
        try:
            # Aggregate operations by type for the user and date
            operations_query = text("""
                SELECT 
                    operation_type,
                    COUNT(*) as operation_count,
                    AVG(confidence_score) as avg_confidence,
                    AVG(similarity_score) as avg_similarity,
                    COUNT(CASE WHEN metadata_->>'conflict_detected' = 'true' THEN 1 END) as conflict_count
                FROM memory_master.evolution_operations
                WHERE user_id = :user_id 
                    AND DATE(created_at) = :target_date
                GROUP BY operation_type
            """)
            
            operations_result = self.db_session.execute(operations_query, {
                "user_id": user_id,
                "target_date": target_date
            })
            
            operation_stats = {}
            total_operations = 0
            total_confidence_weighted = 0
            total_similarity_weighted = 0
            confidence_count = 0
            similarity_count = 0
            conflict_resolution_count = 0
            
            for row in operations_result.fetchall():
                op_type = row.operation_type
                count = row.operation_count
                avg_confidence = row.avg_confidence
                avg_similarity = row.avg_similarity
                conflicts = row.conflict_count
                
                operation_stats[op_type] = count
                total_operations += count
                conflict_resolution_count += conflicts
                
                if avg_confidence is not None:
                    total_confidence_weighted += avg_confidence * count
                    confidence_count += count
                
                if avg_similarity is not None:
                    total_similarity_weighted += avg_similarity * count
                    similarity_count += count
            
            if total_operations == 0:
                logger.debug(f"No operations found for user {user_id} on {target_date}")
                return None
            
            # Calculate learning efficiency
            update_ops = operation_stats.get('UPDATE', 0)
            delete_ops = operation_stats.get('DELETE', 0)
            learning_efficiency = (update_ops + delete_ops) / total_operations
            
            # Calculate averages
            average_confidence = total_confidence_weighted / confidence_count if confidence_count > 0 else 0.0
            average_similarity = total_similarity_weighted / similarity_count if similarity_count > 0 else 0.0
            
            insight = DailyInsight(
                user_id=user_id,
                date=target_date,
                total_operations=total_operations,
                add_operations=operation_stats.get('ADD', 0),
                update_operations=operation_stats.get('UPDATE', 0),
                delete_operations=operation_stats.get('DELETE', 0),
                noop_operations=operation_stats.get('NOOP', 0),
                learning_efficiency=learning_efficiency,
                conflict_resolution_count=conflict_resolution_count,
                average_confidence=average_confidence,
                average_similarity=average_similarity
            )
            
            logger.debug(f"Generated insight for user {user_id}: {total_operations} ops, efficiency {learning_efficiency:.3f}")
            return insight
            
        except Exception as e:
            logger.error(f"Error aggregating user daily insight: {e}")
            return None
    
    async def _store_daily_insight(self, insight: DailyInsight) -> bool:
        """
        Store daily insight in database with upsert logic.
        
        Args:
            insight: DailyInsight to store
            
        Returns:
            True if successfully stored
        """
        try:
            # Use PostgreSQL UPSERT (ON CONFLICT DO UPDATE)
            upsert_query = text("""
                INSERT INTO memory_master.evolution_insights 
                (user_id, date, total_operations, add_operations, update_operations, 
                 delete_operations, noop_operations, learning_efficiency, 
                 conflict_resolution_count, average_confidence, created_at, updated_at)
                VALUES (:user_id, :date, :total_ops, :add_ops, :update_ops, 
                        :delete_ops, :noop_ops, :learning_eff, :conflicts, 
                        :avg_confidence, NOW(), NOW())
                ON CONFLICT (user_id, date) DO UPDATE SET
                    total_operations = EXCLUDED.total_operations,
                    add_operations = EXCLUDED.add_operations,
                    update_operations = EXCLUDED.update_operations,
                    delete_operations = EXCLUDED.delete_operations,
                    noop_operations = EXCLUDED.noop_operations,
                    learning_efficiency = EXCLUDED.learning_efficiency,
                    conflict_resolution_count = EXCLUDED.conflict_resolution_count,
                    average_confidence = EXCLUDED.average_confidence,
                    updated_at = NOW()
            """)
            
            self.db_session.execute(upsert_query, {
                "user_id": insight.user_id,
                "date": insight.date,
                "total_ops": insight.total_operations,
                "add_ops": insight.add_operations,
                "update_ops": insight.update_operations,
                "delete_ops": insight.delete_operations,
                "noop_ops": insight.noop_operations,
                "learning_eff": insight.learning_efficiency,
                "conflicts": insight.conflict_resolution_count,
                "avg_confidence": insight.average_confidence
            })
            
            self.db_session.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error storing daily insight: {e}")
            self.db_session.rollback()
            return False
    
    async def analyze_trends(
        self,
        user_id: str,
        period_days: int = 30
    ) -> Optional[TrendAnalysis]:
        """
        Analyze evolution trends for a user over a specified period.
        
        Args:
            user_id: User identifier
            period_days: Number of days to analyze
            
        Returns:
            TrendAnalysis object or None if insufficient data
        """
        try:
            end_date = datetime.date.today()
            start_date = end_date - datetime.timedelta(days=period_days)
            
            # Get insights data with trend calculations using window functions
            trend_query = text("""
                WITH insights_with_trends AS (
                    SELECT 
                        date,
                        total_operations,
                        learning_efficiency,
                        average_confidence,
                        LAG(learning_efficiency) OVER (ORDER BY date) as prev_efficiency,
                        LAG(average_confidence) OVER (ORDER BY date) as prev_confidence,
                        LAG(total_operations) OVER (ORDER BY date) as prev_operations,
                        ROW_NUMBER() OVER (ORDER BY date) as row_num,
                        COUNT(*) OVER () as total_rows
                    FROM memory_master.evolution_insights
                    WHERE user_id = :user_id 
                        AND date BETWEEN :start_date AND :end_date
                        AND total_operations > 0
                    ORDER BY date
                ),
                trend_calculations AS (
                    SELECT 
                        AVG(learning_efficiency) as avg_efficiency,
                        AVG(average_confidence) as avg_confidence,
                        AVG(total_operations) as avg_operations,
                        -- Calculate trends as percentage change from first to last
                        (LAST_VALUE(learning_efficiency) OVER (ORDER BY date ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) - 
                         FIRST_VALUE(learning_efficiency) OVER (ORDER BY date ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)) / 
                         NULLIF(FIRST_VALUE(learning_efficiency) OVER (ORDER BY date ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING), 0) * 100 as efficiency_trend,
                        (LAST_VALUE(average_confidence) OVER (ORDER BY date ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) - 
                         FIRST_VALUE(average_confidence) OVER (ORDER BY date ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)) / 
                         NULLIF(FIRST_VALUE(average_confidence) OVER (ORDER BY date ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING), 0) * 100 as confidence_trend,
                        (LAST_VALUE(total_operations) OVER (ORDER BY date ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) - 
                         FIRST_VALUE(total_operations) OVER (ORDER BY date ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)) / 
                         NULLIF(FIRST_VALUE(total_operations) OVER (ORDER BY date ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING), 0) * 100 as operation_volume_trend
                    FROM insights_with_trends
                    WHERE total_rows >= 2  -- Need at least 2 data points for trends
                )
                SELECT 
                    efficiency_trend,
                    confidence_trend,
                    operation_volume_trend,
                    avg_efficiency,
                    avg_confidence
                FROM trend_calculations
                LIMIT 1
            """)
            
            result = self.db_session.execute(trend_query, {
                "user_id": user_id,
                "start_date": start_date,
                "end_date": end_date
            }).fetchone()
            
            if not result:
                logger.warning(f"Insufficient data for trend analysis for user {user_id}")
                return None
            
            # Calculate quality score (0-100) based on multiple factors
            avg_efficiency = result.avg_efficiency or 0
            avg_confidence = result.avg_confidence or 0
            efficiency_trend = result.efficiency_trend or 0
            confidence_trend = result.confidence_trend or 0
            
            # Quality score calculation
            efficiency_score = min(avg_efficiency * 100, 40)  # Max 40 points for efficiency
            confidence_score = min(avg_confidence * 30, 30)   # Max 30 points for confidence
            trend_score = max(0, min((efficiency_trend + confidence_trend) / 2, 30))  # Max 30 points for positive trends
            
            quality_score = efficiency_score + confidence_score + trend_score
            
            trend_analysis = TrendAnalysis(
                user_id=user_id,
                period_days=period_days,
                efficiency_trend=efficiency_trend,
                confidence_trend=confidence_trend,
                operation_volume_trend=result.operation_volume_trend or 0,
                quality_score=quality_score
            )
            
            logger.info(f"Generated trend analysis for user {user_id}: quality_score={quality_score:.1f}")
            return trend_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing trends for user {user_id}: {e}")
            return None
    
    async def get_aggregation_summary(
        self,
        days: int = 7
    ) -> Dict[str, Any]:
        """
        Get summary of recent aggregation activity.
        
        Args:
            days: Number of days to look back
            
        Returns:
            Summary statistics
        """
        try:
            end_date = datetime.date.today()
            start_date = end_date - datetime.timedelta(days=days)
            
            summary_query = text("""
                SELECT 
                    DATE(created_at) as insight_date,
                    COUNT(DISTINCT user_id) as users_count,
                    SUM(total_operations) as total_ops,
                    AVG(learning_efficiency) as avg_efficiency,
                    AVG(average_confidence) as avg_confidence,
                    COUNT(*) as insights_count
                FROM memory_master.evolution_insights
                WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                GROUP BY DATE(created_at)
                ORDER BY insight_date DESC
            """)
            
            results = self.db_session.execute(summary_query, {
                "start_date": start_date,
                "end_date": end_date
            }).fetchall()
            
            daily_summaries = []
            for row in results:
                daily_summaries.append({
                    "date": row.insight_date.isoformat(),
                    "users_count": row.users_count,
                    "total_operations": row.total_ops,
                    "average_efficiency": float(row.avg_efficiency or 0),
                    "average_confidence": float(row.avg_confidence or 0),
                    "insights_count": row.insights_count
                })
            
            # Overall totals
            total_query = text("""
                SELECT 
                    COUNT(DISTINCT user_id) as total_users,
                    SUM(total_operations) as total_operations,
                    AVG(learning_efficiency) as overall_efficiency,
                    AVG(average_confidence) as overall_confidence
                FROM memory_master.evolution_insights
                WHERE DATE(created_at) BETWEEN :start_date AND :end_date
            """)
            
            total_result = self.db_session.execute(total_query, {
                "start_date": start_date,
                "end_date": end_date
            }).fetchone()
            
            summary = {
                "period_days": days,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "daily_summaries": daily_summaries,
                "totals": {
                    "unique_users": total_result.total_users or 0,
                    "total_operations": total_result.total_operations or 0,
                    "overall_efficiency": float(total_result.overall_efficiency or 0),
                    "overall_confidence": float(total_result.overall_confidence or 0)
                }
            }
            
            logger.info(f"Generated aggregation summary for {days} days")
            return summary
            
        except Exception as e:
            logger.error(f"Error generating aggregation summary: {e}")
            return {"error": str(e)}
    
    async def run_batch_aggregation(
        self,
        start_date: Optional[datetime.date] = None,
        end_date: Optional[datetime.date] = None
    ) -> Dict[str, Any]:
        """
        Run batch aggregation for a date range.
        
        Args:
            start_date: Start date for aggregation
            end_date: End date for aggregation
            
        Returns:
            Batch processing results
        """
        if start_date is None:
            start_date = datetime.date.today() - datetime.timedelta(days=7)
        if end_date is None:
            end_date = datetime.date.today() - datetime.timedelta(days=1)
        
        logger.info(f"Starting batch aggregation from {start_date} to {end_date}")
        
        results = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "dates_processed": 0,
            "total_users_aggregated": 0,
            "errors": []
        }
        
        current_date = start_date
        while current_date <= end_date:
            try:
                user_count = await self.aggregate_daily_insights(target_date=current_date)
                results["dates_processed"] += 1
                results["total_users_aggregated"] += user_count
                
                logger.info(f"Processed {current_date}: {user_count} users aggregated")
                
            except Exception as date_error:
                error_msg = f"Error processing {current_date}: {date_error}"
                logger.error(error_msg)
                results["errors"].append(error_msg)
            
            current_date += datetime.timedelta(days=1)
        
        logger.info(f"Batch aggregation completed: {results['dates_processed']} dates, {results['total_users_aggregated']} users")
        return results
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check for insights aggregator."""
        health = {
            "service": "evolution_insights_aggregator",
            "status": "healthy"
        }
        
        try:
            # Test database connectivity
            test_query = text("SELECT COUNT(*) FROM memory_master.evolution_insights")
            insights_count = self.db_session.execute(test_query).scalar()
            health["total_insights"] = insights_count
            
            # Check recent aggregation activity
            recent_query = text("""
                SELECT COUNT(DISTINCT DATE(created_at)) as recent_days
                FROM memory_master.evolution_insights
                WHERE created_at >= NOW() - INTERVAL '7 days'
            """)
            recent_days = self.db_session.execute(recent_query).scalar()
            health["recent_aggregation_days"] = recent_days
            
            if recent_days == 0:
                health["status"] = "warning"
                health["message"] = "No recent aggregation activity"
            
        except Exception as e:
            health["status"] = "unhealthy"
            health["error"] = str(e)
        
        return health

# Utility function for scheduled batch jobs
async def run_daily_aggregation_job(db_session: Session) -> Dict[str, Any]:
    """
    Run daily aggregation job for yesterday's data.
    
    This function is designed to be called by a scheduler (e.g., cron job).
    
    Args:
        db_session: Database session
        
    Returns:
        Job execution results
    """
    aggregator = EvolutionInsightsAggregator(db_session)
    
    yesterday = datetime.date.today() - datetime.timedelta(days=1)
    
    logger.info(f"Running daily aggregation job for {yesterday}")
    
    try:
        user_count = await aggregator.aggregate_daily_insights(target_date=yesterday)
        
        result = {
            "status": "success",
            "date": yesterday.isoformat(),
            "users_aggregated": user_count,
            "timestamp": datetime.datetime.now(datetime.UTC).isoformat()
        }
        
        logger.info(f"Daily aggregation job completed successfully: {user_count} users processed")
        return result
        
    except Exception as e:
        result = {
            "status": "error",
            "date": yesterday.isoformat(),
            "error": str(e),
            "timestamp": datetime.datetime.now(datetime.UTC).isoformat()
        }
        
        logger.error(f"Daily aggregation job failed: {e}")
        return result