"""
Evolution Decision Engine for Memory Master v2 Evolution Intelligence.

Implements the core decision logic for ADD/UPDATE/DELETE/NOOP operations based on
similarity analysis and conflict detection using LLM-powered relationship analysis.
"""

import json
import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import asyncio

from anthropic import AsyncAnthropic
import openai
from pydantic import BaseModel, ValidationError

from .similarity_analyzer import SimilarityResult
from app.connection_pools import get_llm_pool_manager
from app.circuit_breakers import circuit_breaker_manager, CircuitBreakerError

logger = logging.getLogger(__name__)

class EvolutionOperation(Enum):
    """Evolution operation types."""
    ADD = "ADD"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    NOOP = "NOOP"

@dataclass
class ConflictAnalysis:
    """Analysis of potential conflicts between facts."""
    is_conflict: bool
    conflict_type: str  # temporal, preference_reversal, factual_contradiction, capability_change
    confidence: float
    reasoning: str

class DecisionResult(BaseModel):
    """Result of evolution decision analysis."""
    operation: str  # ADD, UPDATE, DELETE, NOOP
    confidence: float  # 0.0 to 1.0
    reasoning: str
    target_memory_id: Optional[str] = None
    metadata: Dict[str, Any] = {}

@dataclass
class EvolutionDecision:
    """Enhanced evolution decision with additional metadata and conflict analysis."""
    operation: EvolutionOperation
    confidence: float
    reasoning: str
    conflicts: List[ConflictAnalysis]
    suggested_changes: List[str]
    metadata: Dict[str, Any]
    target_memory_id: Optional[str] = None

class EvolutionDecisionEngine:
    """
    Core decision logic for memory evolution operations.
    
    Implements the four evolution operations:
    - ADD: New information that doesn't exist in memory
    - UPDATE: New information enhances existing memory
    - DELETE: New information contradicts and supersedes existing memory
    - NOOP: Information is redundant or not worth storing
    """
    
    SIMILARITY_THRESHOLD = 0.85
    MEDIUM_SIMILARITY_THRESHOLD = 0.70
    LOW_SIMILARITY_THRESHOLD = 0.50
    
    MEMORY_UPDATE_DECISION_PROMPT = """You are an expert memory manager deciding how to handle new technical information.

Given:
- New fact: {candidate_fact}
- Existing memory: {existing_memory}
- Similarity score: {similarity_score}

Analyze the relationship and choose ONE action:

ADD: If new fact covers different topic or adds new domain
UPDATE: If new fact enhances/specifies existing information  
DELETE: If new fact contradicts/supersedes existing information
NOOP: If new fact is redundant or already covered

Technical Decision Rules:
- Technology migrations: "switched from X to Y" → DELETE X, ADD Y
- Skill progression: "learning X" → "expert in X" → UPDATE
- Project evolution: "working on X" → "completed X" → UPDATE
- Preference changes: "likes X" → "prefers Y over X" → DELETE X, ADD Y
- Complementary info: "uses Python" → "uses Python for data science" → UPDATE
- Contradictory info: "works at Company A" → "works at Company B" → DELETE

Response format:
{{
  "operation": "ADD|UPDATE|DELETE|NOOP",
  "reasoning": "Detailed explanation of the decision",
  "confidence": 0.95,
  "conflict_analysis": {{
    "is_conflict": true/false,
    "conflict_type": "temporal|preference_reversal|factual_contradiction|capability_change|none",
    "confidence": 0.9
  }}
}}
"""

    STANDALONE_FACT_PROMPT = """You are analyzing whether a piece of information is worth storing as a memory.

Fact to analyze: {candidate_fact}
Context: {context}

Determine if this fact should be stored based on:
1. Is it lasting/persistent information (not temporary)?
2. Is it specific enough to be useful?
3. Is it about technical preferences, skills, or work context?
4. Is it actionable or reference-worthy?

Reject facts that are:
- Casual conversation or greetings
- Temporary troubleshooting steps
- Obvious or widely-known information
- Too vague or generic

Response format:
{{
  "operation": "ADD|NOOP",
  "reasoning": "Explanation of why this should or shouldn't be stored",
  "confidence": 0.85
}}
"""
    
    def __init__(
        self,
        anthropic_api_key: Optional[str] = None,
        openai_api_key: Optional[str] = None,
        similarity_threshold: float = 0.85
    ):
        """Initialize evolution decision engine."""
        from app.config import get_settings
        
        settings = get_settings()
        self.anthropic_api_key = anthropic_api_key or settings.anthropic_api_key
        self.openai_api_key = openai_api_key or settings.openai_api_key
        self.llm_pool = get_llm_pool_manager()
        self.circuit_breaker_manager = circuit_breaker_manager
        self.similarity_threshold = similarity_threshold
        
        logger.info("EvolutionDecisionEngine initialized")
    
    async def decide_operation(
        self,
        candidate_fact: str,
        similar_memories: List[SimilarityResult],
        context: Optional[str] = None,
        model: str = "claude-3-5-sonnet-20241022"
    ) -> DecisionResult:
        """
        Decide evolution operation for candidate fact.
        
        Args:
            candidate_fact: The fact to process
            similar_memories: List of similar existing memories
            context: Additional context for decision
            model: LLM model to use for decision
            
        Returns:
            Decision result with operation and reasoning
        """
        # If no similar memories found or similarity too low, ADD
        if not similar_memories or similar_memories[0].similarity_score < self.similarity_threshold:
            return await self._decide_standalone_fact(candidate_fact, context, model)
        
        # Find the most similar memory for detailed analysis
        most_similar = similar_memories[0]
        
        # Detailed relationship analysis
        return await self._analyze_relationship(
            candidate_fact, most_similar, context, model
        )
    
    async def decision_tree(
        self,
        candidate_fact: str,
        similar_memories: List[SimilarityResult],
        context: Optional[str] = None
    ) -> DecisionResult:
        """
        Apply decision tree logic for evolution operations.
        
        This is the core algorithm implementing the similarity-based decision tree:
        1. If no similar memories or similarity < threshold → ADD or NOOP
        2. If similar memory found and similarity >= threshold → analyze relationship
        """
        try:
            # Primary decision path
            decision = await self.decide_operation(candidate_fact, similar_memories, context)
            
            # Validate decision confidence
            if decision.confidence < 0.6:
                logger.warning(f"Low confidence decision ({decision.confidence}), defaulting to ADD")
                return DecisionResult(
                    operation=EvolutionOperation.ADD.value,
                    confidence=0.7,
                    reasoning=f"Defaulted to ADD due to low confidence in original decision: {decision.reasoning}",
                    metadata={"fallback": True, "original_decision": decision.operation}
                )
            
            return decision
            
        except Exception as e:
            logger.error(f"Decision tree error: {e}")
            # Fallback to ADD operation
            return DecisionResult(
                operation=EvolutionOperation.ADD.value,
                confidence=0.5,
                reasoning=f"Error in decision analysis, defaulting to ADD: {e}",
                metadata={"error": str(e), "fallback": True}
            )
    
    async def _decide_standalone_fact(
        self,
        candidate_fact: str,
        context: Optional[str],
        model: str
    ) -> DecisionResult:
        """Decide whether standalone fact should be added or ignored."""
        prompt = self.STANDALONE_FACT_PROMPT.format(
            candidate_fact=candidate_fact,
            context=context or "No additional context"
        )
        
        try:
            if model.startswith("claude"):
                response = await self._analyze_with_claude(prompt, model)
            else:
                response = await self._analyze_with_openai(prompt, model)
            
            # Parse response
            decision_data = self._parse_decision_response(response)
            
            return DecisionResult(
                operation=decision_data.get("operation", "ADD"),
                confidence=decision_data.get("confidence", 0.7),
                reasoning=decision_data.get("reasoning", "Standalone fact analysis"),
                metadata={"analysis_type": "standalone"}
            )
            
        except Exception as e:
            logger.error(f"Standalone fact analysis error: {e}")
            return DecisionResult(
                operation=EvolutionOperation.ADD.value,
                confidence=0.6,
                reasoning=f"Error in standalone analysis, defaulting to ADD: {e}",
                metadata={"error": str(e)}
            )
    
    async def _analyze_relationship(
        self,
        candidate_fact: str,
        similar_memory: SimilarityResult,
        context: Optional[str],
        model: str
    ) -> DecisionResult:
        """Analyze relationship between candidate fact and similar memory."""
        prompt = self.MEMORY_UPDATE_DECISION_PROMPT.format(
            candidate_fact=candidate_fact,
            existing_memory=similar_memory.content,
            similarity_score=similar_memory.similarity_score
        )
        
        try:
            if model.startswith("claude"):
                response = await self._analyze_with_claude(prompt, model)
            else:
                response = await self._analyze_with_openai(prompt, model)
            
            # Parse response
            decision_data = self._parse_decision_response(response)
            
            # Extract conflict analysis if present
            conflict_analysis = decision_data.get("conflict_analysis", {})
            
            return DecisionResult(
                operation=decision_data.get("operation", "ADD"),
                confidence=decision_data.get("confidence", 0.7),
                reasoning=decision_data.get("reasoning", "Relationship analysis"),
                target_memory_id=similar_memory.memory_id,
                metadata={
                    "analysis_type": "relationship",
                    "similarity_score": similar_memory.similarity_score,
                    "conflict_analysis": conflict_analysis,
                    "target_memory_content": similar_memory.content
                }
            )
            
        except Exception as e:
            logger.error(f"Relationship analysis error: {e}")
            return DecisionResult(
                operation=EvolutionOperation.ADD.value,
                confidence=0.6,
                reasoning=f"Error in relationship analysis, defaulting to ADD: {e}",
                target_memory_id=similar_memory.memory_id,
                metadata={"error": str(e)}
            )
    
    async def _analyze_with_claude(self, prompt: str, model: str) -> str:
        """Analyze relationship using Claude with circuit breaker protection."""
        if not self.anthropic_api_key:
            raise ValueError("Anthropic API key not available")
        
        try:
            return await self.circuit_breaker_manager.call_with_circuit_breaker(
                "anthropic_api",
                self._claude_api_call,
                prompt,
                model
            )
        except CircuitBreakerError as e:
            logger.warning(f"Claude analysis blocked by circuit breaker: {e}")
            # Return default response for graceful degradation
            return '{"operation": "ADD", "reasoning": "Circuit breaker open - defaulting to ADD", "confidence": 0.5}'
    
    async def _claude_api_call(self, prompt: str, model: str) -> str:
        """Protected Claude API call method."""
        async with self.llm_pool.get_anthropic_client(self.anthropic_api_key) as client:
            message = await client.messages.create(
                model=model,
                max_tokens=1000,
                messages=[{"role": "user", "content": prompt}]
            )
            return message.content[0].text
    
    async def _analyze_with_openai(self, prompt: str, model: str) -> str:
        """Analyze relationship using OpenAI with circuit breaker protection."""
        if not self.openai_api_key:
            raise ValueError("OpenAI API key not available")
        
        try:
            return await self.circuit_breaker_manager.call_with_circuit_breaker(
                "openai_api",
                self._openai_api_call,
                prompt,
                model
            )
        except CircuitBreakerError as e:
            logger.warning(f"OpenAI analysis blocked by circuit breaker: {e}")
            # Return default response for graceful degradation
            return '{"operation": "ADD", "reasoning": "Circuit breaker open - defaulting to ADD", "confidence": 0.5}'
    
    async def _openai_api_call(self, prompt: str, model: str) -> str:
        """Protected OpenAI API call method."""
        async with self.llm_pool.get_openai_client(self.openai_api_key) as client:
            response = await client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1000,
                response_format={"type": "json_object"}
            )
            return response.choices[0].message.content
    
    def _parse_decision_response(self, response_text: str) -> Dict[str, Any]:
        """Parse LLM response into decision data."""
        try:
            # Handle Claude's response format
            if "```json" in response_text:
                json_start = response_text.find("```json") + 7
                json_end = response_text.find("```", json_start)
                response_text = response_text[json_start:json_end].strip()
            
            data = json.loads(response_text)
            
            # Validate operation type
            operation = data.get("operation", "ADD").upper()
            if operation not in ["ADD", "UPDATE", "DELETE", "NOOP"]:
                logger.warning(f"Invalid operation: {operation}, defaulting to ADD")
                data["operation"] = "ADD"
            
            # Validate confidence score
            confidence = data.get("confidence", 0.7)
            if not isinstance(confidence, (int, float)) or confidence < 0 or confidence > 1:
                logger.warning(f"Invalid confidence: {confidence}, defaulting to 0.7")
                data["confidence"] = 0.7
            
            return data
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse decision response: {e}")
            logger.debug(f"Response text: {response_text}")
            return {
                "operation": "ADD",
                "confidence": 0.5,
                "reasoning": f"Failed to parse LLM response: {e}"
            }
    
    def detect_conflicts(
        self,
        candidate_fact: str,
        existing_memory: str,
        similarity_score: float
    ) -> ConflictAnalysis:
        """
        Detect conflicts between candidate fact and existing memory.
        
        This is a rule-based conflict detection system that can operate
        without LLM calls for faster processing.
        """
        # Convert to lowercase for comparison
        candidate_lower = candidate_fact.lower()
        existing_lower = existing_memory.lower()
        
        # Temporal conflict indicators
        temporal_indicators = [
            ("was", "now"), ("used to", "currently"), ("previously", "now"),
            ("before", "after"), ("old", "new"), ("switched from", "to")
        ]
        
        # Preference reversal indicators
        preference_indicators = [
            ("love", "hate"), ("like", "dislike"), ("prefer", "avoid"),
            ("favourite", "least favourite"), ("best", "worst")
        ]
        
        # Factual contradiction indicators
        factual_indicators = [
            ("works at", "works at"), ("lives in", "lives in"),
            ("uses", "uses"), ("has", "doesn't have")
        ]
        
        is_conflict = False
        conflict_type = "none"
        confidence = 0.0
        reasoning = "No conflict detected"
        
        # Check for temporal conflicts
        for old_term, new_term in temporal_indicators:
            if old_term in existing_lower and new_term in candidate_lower:
                is_conflict = True
                conflict_type = "temporal"
                confidence = 0.8
                reasoning = f"Temporal conflict detected: {old_term} vs {new_term}"
                break
        
        # Check for preference reversals
        if not is_conflict:
            for pos_term, neg_term in preference_indicators:
                if ((pos_term in existing_lower and neg_term in candidate_lower) or
                    (neg_term in existing_lower and pos_term in candidate_lower)):
                    is_conflict = True
                    conflict_type = "preference_reversal"
                    confidence = 0.85
                    reasoning = f"Preference reversal detected: {pos_term} vs {neg_term}"
                    break
        
        # Check for factual contradictions with high similarity
        if not is_conflict and similarity_score > 0.85:
            # Look for contradictory statements about the same entity
            contradictory_pairs = [
                ("is", "is not"), ("can", "cannot"), ("will", "will not"),
                ("does", "does not"), ("has", "has not")
            ]
            
            for pos, neg in contradictory_pairs:
                if ((pos in existing_lower and neg in candidate_lower) or
                    (neg in existing_lower and pos in candidate_lower)):
                    is_conflict = True
                    conflict_type = "factual_contradiction"
                    confidence = 0.9
                    reasoning = f"Factual contradiction detected: {pos} vs {neg}"
                    break
        
        return ConflictAnalysis(
            is_conflict=is_conflict,
            conflict_type=conflict_type,
            confidence=confidence,
            reasoning=reasoning
        )
    
    async def batch_decide_operations(
        self,
        facts_and_similarities: List[Tuple[str, List[SimilarityResult]]],
        context: Optional[str] = None
    ) -> List[DecisionResult]:
        """Batch process multiple facts for evolution decisions."""
        decisions = []
        
        # Process decisions concurrently for better performance
        tasks = []
        for candidate_fact, similar_memories in facts_and_similarities:
            task = self.decision_tree(candidate_fact, similar_memories, context)
            tasks.append(task)
        
        decisions = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle any exceptions
        for i, decision in enumerate(decisions):
            if isinstance(decision, Exception):
                logger.error(f"Batch decision error for fact {i}: {decision}")
                decisions[i] = DecisionResult(
                    operation=EvolutionOperation.ADD.value,
                    confidence=0.5,
                    reasoning=f"Error in batch processing: {decision}",
                    metadata={"error": str(decision), "batch_index": i}
                )
        
        return decisions
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on decision engine."""
        health = {
            "service": "evolution_decision_engine",
            "status": "healthy",
            "similarity_threshold": self.similarity_threshold,
            "api_keys": {
                "anthropic": bool(self.anthropic_api_key),
                "openai": bool(self.openai_api_key)
            }
        }
        
        # Test decision making
        try:
            from .similarity_analyzer import SimilarityResult
            
            test_similar_memory = SimilarityResult(
                memory_id="test_id",
                content="I love Python programming",
                similarity_score=0.9
            )
            
            decision = await self.decide_operation(
                "I prefer TypeScript over Python",
                [test_similar_memory]
            )
            
            if decision.operation in ["ADD", "UPDATE", "DELETE", "NOOP"]:
                health["test_decision"] = "passed"
            else:
                health["status"] = "degraded"
                health["test_decision"] = f"invalid operation: {decision.operation}"
                
        except Exception as e:
            health["status"] = "unhealthy"
            health["test_decision"] = f"failed: {e}"
        
        return health
    
    async def decide_operations_batch(
        self,
        candidate_facts: List[str],
        similar_memories_batch: List[List[SimilarityResult]],
        contexts: Optional[List[str]] = None,
        model: str = "claude-3-5-sonnet-20241022",
        max_concurrent: int = 5
    ) -> List[EvolutionDecision]:
        """
        Make evolution decisions for multiple facts concurrently.
        
        Args:
            candidate_facts: List of candidate facts
            similar_memories_batch: List of similar memories for each fact
            contexts: Optional contexts for each fact
            model: Model to use for decision making
            max_concurrent: Maximum concurrent decisions
            
        Returns:
            List of evolution decisions
        """
        if not candidate_facts:
            return []
        
        if len(candidate_facts) != len(similar_memories_batch):
            raise ValueError("Candidate facts and similar memories lists must have same length")
        
        contexts = contexts or [None] * len(candidate_facts)
        
        # Process in batches to manage concurrency
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def decide_with_semaphore(
            fact: str, 
            similar_memories: List[SimilarityResult],
            context: Optional[str]
        ) -> EvolutionDecision:
            async with semaphore:
                try:
                    return await self.decide_operation(fact, similar_memories, context, model)
                except Exception as e:
                    logger.error(f"Batch decision failed for fact: {fact[:50]}... Error: {e}")
                    # Return no-op decision on error
                    return EvolutionDecision(
                        operation=EvolutionOperation.NOOP,
                        confidence=0.0,
                        reasoning="Decision failed due to error",
                        conflicts=[],
                        suggested_changes=[],
                        metadata={"error": str(e)}
                    )
        
        # Create tasks for all decisions
        tasks = [
            decide_with_semaphore(fact, memories, context)
            for fact, memories, context in zip(candidate_facts, similar_memories_batch, contexts)
        ]
        
        # Execute concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Decision failed for fact {i}: {result}")
                processed_results.append(EvolutionDecision(
                    operation=EvolutionOperation.NOOP,
                    confidence=0.0,
                    reasoning="Exception during decision",
                    conflicts=[],
                    suggested_changes=[],
                    metadata={"exception": str(result)}
                ))
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def analyze_conflicts_batch(
        self,
        candidate_facts: List[str],
        existing_memories: List[str]
    ) -> List[ConflictAnalysis]:
        """
        Analyze conflicts for multiple candidate facts concurrently.
        
        Args:
            candidate_facts: List of candidate facts
            existing_memories: List of existing memory contents
            
        Returns:
            List of conflict analyses
        """
        if not candidate_facts:
            return []
        
        # Create tasks for conflict analysis
        tasks = []
        for fact in candidate_facts:
            task = asyncio.create_task(self._analyze_conflicts(fact, existing_memories))
            tasks.append(task)
        
        # Execute concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Conflict analysis failed for fact {i}: {result}")
                processed_results.append(ConflictAnalysis(
                    is_conflict=False,
                    conflict_type="analysis_error",
                    severity="unknown",
                    conflicting_memory_id=None,
                    resolution_strategy="manual_review",
                    confidence=0.0
                ))
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def optimize_batch_decisions(
        self,
        facts_and_memories: List[Tuple[str, List[SimilarityResult]]],
        model: str = "claude-3-5-sonnet-20241022"
    ) -> List[EvolutionDecision]:
        """
        Optimize batch decision making by grouping similar operations.
        
        Args:
            facts_and_memories: List of (fact, similar_memories) tuples
            model: Model to use for decision making
            
        Returns:
            List of evolution decisions
        """
        if not facts_and_memories:
            return []
        
        # Group facts by operation type likelihood for batch processing
        high_confidence_adds = []
        potential_updates = []
        conflict_candidates = []
        
        # Quick classification based on similarity scores
        for fact, memories in facts_and_memories:
            if not memories:
                high_confidence_adds.append((fact, memories))
            elif memories[0].similarity_score > 0.95:
                potential_updates.append((fact, memories))
            elif memories[0].similarity_score > 0.8:
                conflict_candidates.append((fact, memories))
            else:
                high_confidence_adds.append((fact, memories))
        
        # Process each group with appropriate concurrency
        results = []
        
        # Process high-confidence adds with higher concurrency
        if high_confidence_adds:
            add_decisions = await self.decide_operations_batch(
                [fact for fact, _ in high_confidence_adds],
                [memories for _, memories in high_confidence_adds],
                max_concurrent=10
            )
            results.extend(add_decisions)
        
        # Process potential updates with medium concurrency
        if potential_updates:
            update_decisions = await self.decide_operations_batch(
                [fact for fact, _ in potential_updates],
                [memories for _, memories in potential_updates],
                max_concurrent=5
            )
            results.extend(update_decisions)
        
        # Process conflict candidates with lower concurrency for careful analysis
        if conflict_candidates:
            conflict_decisions = await self.decide_operations_batch(
                [fact for fact, _ in conflict_candidates],
                [memories for _, memories in conflict_candidates],
                max_concurrent=2
            )
            results.extend(conflict_decisions)
        
        return results
    
    def get_batch_decision_stats(self) -> Dict[str, Any]:
        """Get statistics about batch decision processing."""
        return {
            "similarity_threshold": self.similarity_threshold,
            "batch_processing_limits": {
                "max_concurrent_decisions": 20,
                "recommended_batch_size": 10,
                "conflict_analysis_concurrency": 5
            },
            "optimization_features": {
                "operation_grouping": True,
                "adaptive_concurrency": True,
                "error_recovery": True
            }
        }