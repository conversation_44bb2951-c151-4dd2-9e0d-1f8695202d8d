"""
Evolution Operations Tracking System for Memory Master v2.

Implements comprehensive tracking and logging system for all evolution operations
with audit trail capabilities, batch processing, and data retention policies.
"""

import logging
import asyncio
import uuid
import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum
import json

from sqlalchemy.orm import Session
from sqlalchemy import text, func

logger = logging.getLogger(__name__)

@dataclass
class EvolutionTrackingEntry:
    """Single evolution operation tracking entry."""
    operation_id: str
    memory_id: Optional[str]
    user_id: str
    operation_type: str  # ADD, UPDATE, DELETE, NOOP
    candidate_fact: str
    existing_memory_content: Optional[str]
    similarity_score: Optional[float]
    confidence_score: Optional[float]
    reasoning: Optional[str]
    metadata: Dict[str, Any]
    timestamp: datetime.datetime

class EvolutionTracker:
    """
    Comprehensive tracking system for evolution operations.
    
    Features:
    - Audit trail logging for all operations
    - Batch insertion for performance
    - Metadata tracking for debugging and analysis
    - Data retention policies
    - Operation reversal tracking
    - Performance metrics collection
    """
    
    def __init__(self, db_session: Session, batch_size: int = 100):
        """Initialize evolution tracker."""
        self.db_session = db_session
        self.batch_size = batch_size
        self.pending_entries: List[EvolutionTrackingEntry] = []
        self.tracking_enabled = True
        
        logger.info(f"EvolutionTracker initialized with batch_size={batch_size}")
    
    async def track_operation(
        self,
        operation_id: str,
        user_id: str,
        operation_type: str,
        candidate_fact: str,
        memory_id: Optional[str] = None,
        existing_memory_content: Optional[str] = None,
        similarity_score: Optional[float] = None,
        confidence_score: Optional[float] = None,
        reasoning: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Track a single evolution operation.
        
        Args:
            operation_id: Unique identifier for the operation
            user_id: User identifier
            operation_type: Type of operation (ADD, UPDATE, DELETE, NOOP)
            candidate_fact: The fact being processed
            memory_id: ID of related memory (if applicable)
            existing_memory_content: Content of existing memory (for comparison)
            similarity_score: Similarity score between candidate and existing
            confidence_score: Confidence in the decision
            reasoning: LLM reasoning for the decision
            metadata: Additional metadata for debugging
            
        Returns:
            True if successfully tracked
        """
        if not self.tracking_enabled:
            return False
        
        try:
            entry = EvolutionTrackingEntry(
                operation_id=operation_id,
                memory_id=memory_id,
                user_id=user_id,
                operation_type=operation_type,
                candidate_fact=candidate_fact,
                existing_memory_content=existing_memory_content,
                similarity_score=similarity_score,
                confidence_score=confidence_score,
                reasoning=reasoning,
                metadata=metadata or {},
                timestamp=datetime.datetime.now(datetime.UTC)
            )
            
            self.pending_entries.append(entry)
            
            # Trigger batch flush if needed
            if len(self.pending_entries) >= self.batch_size:
                await self.flush_batch()
            
            logger.debug(f"Tracked evolution operation: {operation_type} for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error tracking evolution operation: {e}")
            return False
    
    async def track_batch_operations(
        self,
        operations: List[Dict[str, Any]]
    ) -> int:
        """
        Track multiple evolution operations in batch.
        
        Args:
            operations: List of operation dictionaries
            
        Returns:
            Number of successfully tracked operations
        """
        tracked_count = 0
        
        for operation in operations:
            success = await self.track_operation(
                operation_id=operation.get('id', str(uuid.uuid4())),
                user_id=operation['user_id'],
                operation_type=operation['operation_type'],
                candidate_fact=operation['candidate_fact'],
                memory_id=operation.get('memory_id'),
                existing_memory_content=operation.get('existing_memory_content'),
                similarity_score=operation.get('similarity_score'),
                confidence_score=operation.get('confidence_score'),
                reasoning=operation.get('reasoning'),
                metadata=operation.get('metadata', {})
            )
            
            if success:
                tracked_count += 1
        
        # Force flush after batch tracking
        await self.flush_batch()
        
        logger.info(f"Batch tracked {tracked_count}/{len(operations)} evolution operations")
        return tracked_count
    
    async def track_operations_batch(self, decisions: List[Any], user_id: str) -> int:
        """
        Track multiple EvolutionDecision objects in batch.
        
        Args:
            decisions: List of EvolutionDecision objects
            user_id: User identifier for the operations
            
        Returns:
            Number of successfully tracked operations
        """
        tracked_count = 0
        
        for decision in decisions:
            try:
                # Extract attributes from EvolutionDecision object
                operation_type = decision.operation.value if hasattr(decision.operation, 'value') else str(decision.operation)
                metadata = getattr(decision, 'metadata', {})
                
                # Add decision-specific metadata
                if hasattr(decision, 'conflicts'):
                    metadata['conflicts_detected'] = len(decision.conflicts) > 0
                    metadata['conflict_count'] = len(decision.conflicts)
                
                if hasattr(decision, 'suggested_changes'):
                    metadata['suggested_changes_count'] = len(decision.suggested_changes)
                
                success = await self.track_operation(
                    operation_id=str(uuid.uuid4()),
                    user_id=user_id,
                    operation_type=operation_type,
                    candidate_fact=getattr(decision, 'candidate_fact', ''),
                    memory_id=getattr(decision, 'target_memory_id', None),
                    existing_memory_content=getattr(decision, 'existing_memory_content', None),
                    similarity_score=getattr(decision, 'similarity_score', None),
                    confidence_score=getattr(decision, 'confidence', None),
                    reasoning=getattr(decision, 'reasoning', ''),
                    metadata=metadata
                )
                
                if success:
                    tracked_count += 1
                    
            except Exception as e:
                logger.error(f"Failed to track evolution decision: {e}")
                continue
        
        # Force flush after batch tracking
        await self.flush_batch()
        
        logger.info(f"Batch tracked {tracked_count}/{len(decisions)} evolution decisions")
        return tracked_count
    
    async def flush_batch(self) -> bool:
        """
        Flush pending tracking entries to database.
        
        Returns:
            True if successfully flushed
        """
        if not self.pending_entries:
            return True
        
        try:
            from ..models import EvolutionOperation, EvolutionOperationType
            
            # Prepare batch insert
            operations_to_insert = []
            for entry in self.pending_entries:
                operation = EvolutionOperation(
                    id=uuid.UUID(entry.operation_id),
                    memory_id=uuid.UUID(entry.memory_id) if entry.memory_id else None,
                    user_id=uuid.UUID(entry.user_id),
                    operation_type=EvolutionOperationType(entry.operation_type),
                    candidate_fact=entry.candidate_fact,
                    existing_memory_content=entry.existing_memory_content,
                    similarity_score=entry.similarity_score,
                    confidence_score=entry.confidence_score,
                    reasoning=entry.reasoning,
                    created_at=entry.timestamp,
                    metadata_=entry.metadata
                )
                operations_to_insert.append(operation)
            
            # Bulk insert
            self.db_session.bulk_save_objects(operations_to_insert)
            self.db_session.commit()
            
            logger.info(f"Flushed {len(self.pending_entries)} evolution tracking entries to database")
            
            # Clear pending entries
            self.pending_entries.clear()
            return True
            
        except Exception as e:
            logger.error(f"Error flushing evolution tracking batch: {e}")
            self.db_session.rollback()
            return False
    
    async def get_operation_audit_trail(
        self,
        user_id: str,
        start_date: Optional[datetime.datetime] = None,
        end_date: Optional[datetime.datetime] = None,
        operation_types: Optional[List[str]] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get audit trail for evolution operations.
        
        Args:
            user_id: User identifier
            start_date: Start date for filtering
            end_date: End date for filtering
            operation_types: Filter by operation types
            limit: Maximum number of results
            
        Returns:
            List of operation audit records
        """
        try:
            from ..models import EvolutionOperation
            
            query = self.db_session.query(EvolutionOperation).filter(
                EvolutionOperation.user_id == uuid.UUID(user_id)
            )
            
            if start_date:
                query = query.filter(EvolutionOperation.created_at >= start_date)
            
            if end_date:
                query = query.filter(EvolutionOperation.created_at <= end_date)
            
            if operation_types:
                query = query.filter(EvolutionOperation.operation_type.in_(operation_types))
            
            operations = query.order_by(
                EvolutionOperation.created_at.desc()
            ).limit(limit).all()
            
            audit_trail = []
            for op in operations:
                audit_trail.append({
                    "id": str(op.id),
                    "memory_id": str(op.memory_id) if op.memory_id else None,
                    "operation_type": op.operation_type.value,
                    "candidate_fact": op.candidate_fact,
                    "existing_memory_content": op.existing_memory_content,
                    "similarity_score": op.similarity_score,
                    "confidence_score": op.confidence_score,
                    "reasoning": op.reasoning,
                    "created_at": op.created_at.isoformat() if op.created_at else None,
                    "metadata": op.metadata_
                })
            
            logger.info(f"Retrieved {len(audit_trail)} audit trail entries for user {user_id}")
            return audit_trail
            
        except Exception as e:
            logger.error(f"Error retrieving operation audit trail: {e}")
            return []
    
    async def get_operation_statistics(
        self,
        user_id: str,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get evolution operation statistics for user.
        
        Args:
            user_id: User identifier
            days: Number of days to look back
            
        Returns:
            Statistics dictionary
        """
        try:
            from ..models import EvolutionOperation
            
            end_date = datetime.datetime.now(datetime.UTC)
            start_date = end_date - datetime.timedelta(days=days)
            
            # Get operation counts by type
            stats_query = self.db_session.query(
                EvolutionOperation.operation_type,
                func.count(EvolutionOperation.id).label('count'),
                func.avg(EvolutionOperation.confidence_score).label('avg_confidence'),
                func.avg(EvolutionOperation.similarity_score).label('avg_similarity')
            ).filter(
                EvolutionOperation.user_id == uuid.UUID(user_id),
                EvolutionOperation.created_at >= start_date
            ).group_by(EvolutionOperation.operation_type).all()
            
            total_operations = 0
            operation_counts = {}
            total_confidence = 0
            total_similarity = 0
            confidence_count = 0
            similarity_count = 0
            
            for stat in stats_query:
                op_type = stat.operation_type.value
                count = stat.count
                avg_confidence = stat.avg_confidence
                avg_similarity = stat.avg_similarity
                
                operation_counts[op_type] = count
                total_operations += count
                
                if avg_confidence:
                    total_confidence += avg_confidence * count
                    confidence_count += count
                
                if avg_similarity:
                    total_similarity += avg_similarity * count
                    similarity_count += count
            
            # Calculate derived metrics
            learning_efficiency = 0.0
            if total_operations > 0:
                update_delete_ops = operation_counts.get('UPDATE', 0) + operation_counts.get('DELETE', 0)
                learning_efficiency = update_delete_ops / total_operations
            
            overall_confidence = total_confidence / confidence_count if confidence_count > 0 else 0.0
            overall_similarity = total_similarity / similarity_count if similarity_count > 0 else 0.0
            
            statistics = {
                "period_days": days,
                "total_operations": total_operations,
                "operation_counts": operation_counts,
                "learning_efficiency": learning_efficiency,
                "average_confidence": overall_confidence,
                "average_similarity": overall_similarity,
                "metrics": {
                    "add_rate": operation_counts.get('ADD', 0) / max(total_operations, 1),
                    "update_rate": operation_counts.get('UPDATE', 0) / max(total_operations, 1),
                    "delete_rate": operation_counts.get('DELETE', 0) / max(total_operations, 1),
                    "noop_rate": operation_counts.get('NOOP', 0) / max(total_operations, 1)
                }
            }
            
            logger.info(f"Generated operation statistics for user {user_id}: {total_operations} operations")
            return statistics
            
        except Exception as e:
            logger.error(f"Error generating operation statistics: {e}")
            return {"error": str(e)}
    
    async def cleanup_old_operations(
        self,
        retention_days: int = 90,
        batch_size: int = 1000
    ) -> int:
        """
        Clean up old evolution operations based on retention policy.
        
        Args:
            retention_days: Number of days to retain operations
            batch_size: Number of operations to delete per batch
            
        Returns:
            Number of operations deleted
        """
        try:
            from ..models import EvolutionOperation
            
            cutoff_date = datetime.datetime.now(datetime.UTC) - datetime.timedelta(days=retention_days)
            
            # Count operations to be deleted
            count_query = self.db_session.query(func.count(EvolutionOperation.id)).filter(
                EvolutionOperation.created_at < cutoff_date
            )
            total_to_delete = count_query.scalar()
            
            if total_to_delete == 0:
                logger.info("No old evolution operations to clean up")
                return 0
            
            logger.info(f"Starting cleanup of {total_to_delete} evolution operations older than {retention_days} days")
            
            deleted_count = 0
            
            # Delete in batches to avoid long-running transactions
            while True:
                # Get batch of IDs to delete
                ids_query = self.db_session.query(EvolutionOperation.id).filter(
                    EvolutionOperation.created_at < cutoff_date
                ).limit(batch_size)
                
                ids_to_delete = [row[0] for row in ids_query.all()]
                
                if not ids_to_delete:
                    break
                
                # Delete batch
                delete_count = self.db_session.query(EvolutionOperation).filter(
                    EvolutionOperation.id.in_(ids_to_delete)
                ).delete(synchronize_session=False)
                
                self.db_session.commit()
                deleted_count += delete_count
                
                logger.info(f"Deleted batch of {delete_count} operations (total: {deleted_count}/{total_to_delete})")
                
                # Prevent infinite loop
                if delete_count == 0:
                    break
            
            logger.info(f"Evolution operations cleanup completed: {deleted_count} operations deleted")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Error during evolution operations cleanup: {e}")
            self.db_session.rollback()
            return 0
    
    async def export_operations_for_analysis(
        self,
        user_id: str,
        start_date: Optional[datetime.datetime] = None,
        end_date: Optional[datetime.datetime] = None,
        format: str = "json"
    ) -> Optional[str]:
        """
        Export evolution operations for external analysis.
        
        Args:
            user_id: User identifier
            start_date: Start date for export
            end_date: End date for export
            format: Export format ('json' or 'csv')
            
        Returns:
            Exported data as string or None if error
        """
        try:
            operations = await self.get_operation_audit_trail(
                user_id=user_id,
                start_date=start_date,
                end_date=end_date,
                limit=10000  # Large limit for export
            )
            
            if format.lower() == "json":
                export_data = {
                    "export_timestamp": datetime.datetime.now(datetime.UTC).isoformat(),
                    "user_id": user_id,
                    "start_date": start_date.isoformat() if start_date else None,
                    "end_date": end_date.isoformat() if end_date else None,
                    "operations_count": len(operations),
                    "operations": operations
                }
                return json.dumps(export_data, indent=2)
            
            elif format.lower() == "csv":
                import csv
                import io
                
                output = io.StringIO()
                writer = csv.writer(output)
                
                # Write header
                writer.writerow([
                    "id", "memory_id", "operation_type", "candidate_fact",
                    "existing_memory_content", "similarity_score", "confidence_score",
                    "reasoning", "created_at"
                ])
                
                # Write data
                for op in operations:
                    writer.writerow([
                        op.get("id", ""),
                        op.get("memory_id", ""),
                        op.get("operation_type", ""),
                        op.get("candidate_fact", ""),
                        op.get("existing_memory_content", ""),
                        op.get("similarity_score", ""),
                        op.get("confidence_score", ""),
                        op.get("reasoning", ""),
                        op.get("created_at", "")
                    ])
                
                return output.getvalue()
            
            else:
                logger.error(f"Unsupported export format: {format}")
                return None
                
        except Exception as e:
            logger.error(f"Error exporting operations for analysis: {e}")
            return None
    
    def enable_tracking(self):
        """Enable evolution tracking."""
        self.tracking_enabled = True
        logger.info("Evolution tracking enabled")
    
    def disable_tracking(self):
        """Disable evolution tracking."""
        self.tracking_enabled = False
        logger.info("Evolution tracking disabled")
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check for evolution tracker."""
        health = {
            "service": "evolution_tracker",
            "status": "healthy",
            "tracking_enabled": self.tracking_enabled,
            "batch_size": self.batch_size,
            "pending_entries": len(self.pending_entries)
        }
        
        try:
            # Test database connectivity
            from ..models import EvolutionOperation
            test_count = self.db_session.query(func.count(EvolutionOperation.id)).scalar()
            health["total_tracked_operations"] = test_count
            
        except Exception as e:
            health["status"] = "unhealthy"
            health["database_error"] = str(e)
        
        return health

# Global tracker instance for reuse
_evolution_tracker: Optional[EvolutionTracker] = None

def get_evolution_tracker(db_session: Session) -> EvolutionTracker:
    """Get evolution tracker instance."""
    global _evolution_tracker
    
    if _evolution_tracker is None:
        _evolution_tracker = EvolutionTracker(db_session)
    
    return _evolution_tracker