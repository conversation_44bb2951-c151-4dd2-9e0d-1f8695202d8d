"""
Evolution intelligence package for Memory Master v2.

This package implements the intelligent memory evolution system based on mem0's
two-phase pipeline for automatic memory conflict resolution and updates.
"""

from .pipeline import EvolutionPipeline, PipelineInput, PipelineResult, BatchPipelineResult
from .fact_extractor import FactExtractor, ExtractionContext, FactExtractionResponse
from .similarity_analyzer import SimilarityAnalyzer, SimilarityResult
from .decision_engine import EvolutionDecisionEngine, EvolutionOperation, EvolutionDecision
from .evolution_tracker import EvolutionTracker

__all__ = [
    'EvolutionPipeline',
    'PipelineInput', 
    'PipelineResult',
    'BatchPipelineResult',
    'FactExtractor',
    'ExtractionContext',
    'FactExtractionResponse', 
    'SimilarityAnalyzer',
    'SimilarityResult',
    'EvolutionDecisionEngine',
    'EvolutionOperation',
    'EvolutionDecision',
    'EvolutionTracker'
]