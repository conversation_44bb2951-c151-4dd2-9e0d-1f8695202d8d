"""
Evolution Service for Memory Master v2 Evolution Intelligence.

Orchestrates the two-phase evolution pipeline: fact extraction and evolution decisions.
Integrates with the existing add_memories() function while preserving all current functionality.
"""

import logging
import json
import time
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import uuid
import datetime

from .fact_extractor import FactExtractor, ExtractedFact, ExtractionContext
from .similarity_analyzer import SimilarityAnaly<PERSON>, SimilarityResult
from .decision_engine import EvolutionDecisionEngine, EvolutionDecision, EvolutionOperation
from .prompts import PromptManager, PromptVersion

logger = logging.getLogger(__name__)

@dataclass
class EvolutionConfig:
    """Configuration for evolution operations."""
    enable_evolution: bool = True
    evolution_timeout: float = 30.0  # seconds
    similarity_threshold: float = 0.7
    confidence_threshold: float = 0.6
    max_facts_per_text: int = 10
    prompt_version: PromptVersion = PromptVersion.V3_TECHNICAL
    feature_flag: str = "evolution_intelligence_v1"
    
class EvolutionMetrics:
    """Metrics tracking for evolution operations."""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """Reset all metrics."""
        self.total_operations = 0
        self.add_operations = 0
        self.update_operations = 0
        self.delete_operations = 0
        self.noop_operations = 0
        self.conflicts_detected = 0
        self.processing_time = 0.0
        self.facts_extracted = 0
        self.similarities_computed = 0
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        return {
            "total_operations": self.total_operations,
            "add_operations": self.add_operations,
            "update_operations": self.update_operations, 
            "delete_operations": self.delete_operations,
            "noop_operations": self.noop_operations,
            "conflicts_detected": self.conflicts_detected,
            "processing_time": self.processing_time,
            "facts_extracted": self.facts_extracted,
            "similarities_computed": self.similarities_computed,
            "learning_efficiency": (self.update_operations + self.delete_operations) / max(self.total_operations, 1)
        }

@dataclass
class EvolutionResult:
    """Result of evolution processing."""
    success: bool
    metrics: EvolutionMetrics
    operations: List[Dict[str, Any]]
    errors: List[str]
    processing_time: float
    fallback_mode: bool = False

class EvolutionService:
    """
    Evolution Intelligence Service.
    
    Implements the two-phase evolution pipeline for intelligent memory management:
    1. Phase 1: Fact extraction from conversational input
    2. Phase 2: Evolution decisions based on similarity analysis
    """
    
    def __init__(
        self,
        config: Optional[EvolutionConfig] = None,
        db_session = None
    ):
        """Initialize evolution service."""
        self.config = config or EvolutionConfig()
        self.db_session = db_session
        
        # Initialize components
        self.fact_extractor = FactExtractor()
        self.similarity_analyzer = SimilarityAnalyzer() 
        self.decision_engine = EvolutionDecisionEngine()
        self.prompt_manager = PromptManager(default_version=self.config.prompt_version)
        
        # Metrics tracking
        self.session_metrics = EvolutionMetrics()
        
        logger.info(f"EvolutionService initialized with config: {self.config}")
    
    async def process_memory_evolution(
        self,
        text: str,
        user_id: str,
        client_name: str,
        existing_memories: Optional[List[Dict[str, Any]]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> EvolutionResult:
        """
        Process memory evolution pipeline.
        
        Args:
            text: Input text to process
            user_id: User identifier
            client_name: Client application name  
            existing_memories: List of existing memories for similarity analysis
            context: Additional context for processing
            
        Returns:
            EvolutionResult with operations and metrics
        """
        start_time = time.time()
        result = EvolutionResult(
            success=False,
            metrics=EvolutionMetrics(),
            operations=[],
            errors=[],
            processing_time=0.0
        )
        
        try:
            logger.info(f"Starting evolution pipeline for user {user_id}, client {client_name}")
            
            # Check feature flag
            if not self.config.enable_evolution:
                logger.info("Evolution intelligence disabled by config")
                result.success = True
                result.processing_time = time.time() - start_time
                return result
            
            # Phase 1: Fact Extraction
            logger.info("Phase 1: Extracting facts from input text")
            extraction_context = ExtractionContext(
                latest_message=text,
                recent_messages=context.get("recent_messages", []) if context else [],
                user_background=context.get("user_background") if context else None
            )
            
            extracted_facts = await self.fact_extractor.extract_facts(
                extraction_context,
                version=self.config.prompt_version
            )
            
            result.metrics.facts_extracted = len(extracted_facts)
            logger.info(f"Extracted {len(extracted_facts)} facts from input")
            
            if not extracted_facts:
                logger.info("No facts extracted, returning empty result")
                result.success = True
                result.processing_time = time.time() - start_time
                return result
            
            # Phase 2: Evolution Decisions
            logger.info("Phase 2: Processing evolution decisions")
            
            for fact in extracted_facts:
                try:
                    # Find similar existing memories
                    if existing_memories:
                        similar_memories = await self.similarity_analyzer.find_similar_memories(
                            fact.fact,
                            existing_memories,
                            threshold=self.config.similarity_threshold
                        )
                        result.metrics.similarities_computed += len(similar_memories)
                    else:
                        similar_memories = []
                    
                    # Make evolution decision
                    if similar_memories:
                        # Process potential conflicts with existing memories
                        decision = await self.decision_engine.decide_operation(
                            candidate_fact=fact.fact,
                            similar_memories=similar_memories[:3],  # Limit to top 3 for performance
                            context=f"user_id: {user_id}, client: {client_name}, category: {fact.category}, confidence: {fact.confidence}"
                        )

                        if decision.confidence >= self.config.confidence_threshold:
                            # Use the most similar memory for the operation record
                            most_similar = similar_memories[0] if similar_memories else None
                            operation = self._create_operation_record(
                                fact, decision, most_similar, user_id, client_name
                            )
                            result.operations.append(operation)
                            self._update_metrics(result.metrics, decision.operation)

                            if decision.conflict_analysis and decision.conflict_analysis.is_conflict:
                                result.metrics.conflicts_detected += 1

                            logger.info(f"Evolution decision: {decision.operation.value} for fact '{fact.fact[:50]}...'")
                    else:
                        # No similar memories found - standalone fact evaluation
                        standalone_decision = await self.decision_engine.evaluate_standalone_fact(
                            fact.fact,
                            context={
                                "user_id": user_id,
                                "client_name": client_name,
                                "fact_category": fact.category,
                                "extraction_confidence": fact.confidence
                            },
                            version=self.config.prompt_version
                        )
                        
                        if standalone_decision.confidence >= self.config.confidence_threshold:
                            operation = self._create_standalone_operation_record(
                                fact, standalone_decision, user_id, client_name
                            )
                            result.operations.append(operation)
                            self._update_metrics(result.metrics, standalone_decision.operation)
                            
                            logger.info(f"Standalone decision: {standalone_decision.operation.value} for fact '{fact.fact[:50]}...'")
                
                except Exception as fact_error:
                    logger.error(f"Error processing fact '{fact.fact[:50]}...': {fact_error}")
                    result.errors.append(f"Fact processing error: {fact_error}")
            
            # Store evolution insights if DB session available
            if self.db_session:
                try:
                    await self._store_evolution_insights(
                        user_id, result.metrics, self.db_session
                    )
                except Exception as insights_error:
                    logger.error(f"Error storing evolution insights: {insights_error}")
                    result.errors.append(f"Insights storage error: {insights_error}")
            
            result.success = True
            logger.info(f"Evolution pipeline completed with {len(result.operations)} operations")
            
        except Exception as e:
            logger.error(f"Evolution pipeline error: {e}")
            result.errors.append(f"Pipeline error: {e}")
            result.success = False
            
        finally:
            result.processing_time = time.time() - start_time
            result.metrics.processing_time = result.processing_time
            
            # Update session metrics
            self.session_metrics.total_operations += result.metrics.total_operations
            self.session_metrics.add_operations += result.metrics.add_operations
            self.session_metrics.update_operations += result.metrics.update_operations
            self.session_metrics.delete_operations += result.metrics.delete_operations
            self.session_metrics.noop_operations += result.metrics.noop_operations
            self.session_metrics.conflicts_detected += result.metrics.conflicts_detected
            self.session_metrics.processing_time += result.metrics.processing_time
            self.session_metrics.facts_extracted += result.metrics.facts_extracted
            self.session_metrics.similarities_computed += result.metrics.similarities_computed
        
        return result
    
    def _create_operation_record(
        self,
        fact: ExtractedFact,
        decision: EvolutionDecision,
        similar_memory: SimilarityResult,
        user_id: str,
        client_name: str
    ) -> Dict[str, Any]:
        """Create operation record for evolution operations table."""
        return {
            "id": str(uuid.uuid4()),
            "memory_id": similar_memory.memory_id if similar_memory.memory_id else None,
            "user_id": user_id,
            "operation_type": decision.operation.value,
            "candidate_fact": fact.fact,
            "existing_memory_content": similar_memory.content,
            "similarity_score": similar_memory.similarity_score,
            "confidence_score": decision.confidence,
            "reasoning": decision.reasoning,
            "created_at": datetime.datetime.now(datetime.UTC),
            "metadata": {
                "fact_category": fact.category,
                "fact_confidence": fact.confidence,
                "fact_relevance": fact.relevance_score,
                "client_name": client_name,
                "conflict_detected": decision.conflict_analysis.is_conflict if decision.conflict_analysis else False,
                "conflict_type": decision.conflict_analysis.conflict_type if decision.conflict_analysis else None
            }
        }
    
    def _create_standalone_operation_record(
        self,
        fact: ExtractedFact,
        decision: EvolutionDecision,
        user_id: str,
        client_name: str
    ) -> Dict[str, Any]:
        """Create operation record for standalone facts."""
        return {
            "id": str(uuid.uuid4()),
            "memory_id": None,
            "user_id": user_id,
            "operation_type": decision.operation.value,
            "candidate_fact": fact.fact,
            "existing_memory_content": None,
            "similarity_score": None,
            "confidence_score": decision.confidence,
            "reasoning": decision.reasoning,
            "created_at": datetime.datetime.now(datetime.UTC),
            "metadata": {
                "fact_category": fact.category,
                "fact_confidence": fact.confidence,
                "fact_relevance": fact.relevance_score,
                "client_name": client_name,
                "standalone_evaluation": True
            }
        }
    
    def _update_metrics(
        self,
        metrics: EvolutionMetrics,
        operation: EvolutionOperation
    ):
        """Update metrics with operation results."""
        metrics.total_operations += 1
        
        if operation == EvolutionOperation.ADD:
            metrics.add_operations += 1
        elif operation == EvolutionOperation.UPDATE:
            metrics.update_operations += 1
        elif operation == EvolutionOperation.DELETE:
            metrics.delete_operations += 1
        elif operation == EvolutionOperation.NOOP:
            metrics.noop_operations += 1
    
    async def _store_evolution_insights(
        self,
        user_id: str,
        metrics: EvolutionMetrics,
        db_session
    ):
        """Store evolution insights in database."""
        try:
            from ..models import EvolutionInsight
            from sqlalchemy import text
            
            today = datetime.date.today()
            
            # Use raw SQL for upsert to handle the unique constraint
            upsert_sql = text("""
                INSERT INTO memory_master.evolution_insights 
                (user_id, date, total_operations, add_operations, update_operations, 
                 delete_operations, noop_operations, conflict_resolution_count, 
                 average_confidence, learning_efficiency, created_at, updated_at)
                VALUES (:user_id, :date, :total_ops, :add_ops, :update_ops, 
                        :delete_ops, :noop_ops, :conflicts, :avg_confidence, 
                        :learning_eff, NOW(), NOW())
                ON CONFLICT (user_id, date) DO UPDATE SET
                    total_operations = evolution_insights.total_operations + :total_ops,
                    add_operations = evolution_insights.add_operations + :add_ops,
                    update_operations = evolution_insights.update_operations + :update_ops,
                    delete_operations = evolution_insights.delete_operations + :delete_ops,
                    noop_operations = evolution_insights.noop_operations + :noop_ops,
                    conflict_resolution_count = evolution_insights.conflict_resolution_count + :conflicts,
                    learning_efficiency = (evolution_insights.update_operations + evolution_insights.delete_operations + :update_ops + :delete_ops) / 
                                         GREATEST(evolution_insights.total_operations + :total_ops, 1),
                    updated_at = NOW()
            """)
            
            db_session.execute(upsert_sql, {
                "user_id": user_id,
                "date": today,
                "total_ops": metrics.total_operations,
                "add_ops": metrics.add_operations,
                "update_ops": metrics.update_operations,
                "delete_ops": metrics.delete_operations,
                "noop_ops": metrics.noop_operations,
                "conflicts": metrics.conflicts_detected,
                "avg_confidence": 0.0,  # TODO: Calculate average confidence
                "learning_eff": metrics.learning_efficiency if metrics.total_operations > 0 else 0.0
            })
            
            db_session.commit()
            logger.info(f"Stored evolution insights for user {user_id} on {today}")
            
        except Exception as e:
            logger.error(f"Failed to store evolution insights: {e}")
            db_session.rollback()
            raise
    
    async def get_evolution_statistics(
        self,
        user_id: str,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get evolution statistics for user."""
        try:
            if not self.db_session:
                return {"error": "Database session not available"}
            
            from sqlalchemy import text
            from datetime import date, timedelta
            
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            stats_sql = text("""
                SELECT 
                    SUM(total_operations) as total_operations,
                    SUM(add_operations) as add_operations,
                    SUM(update_operations) as update_operations,
                    SUM(delete_operations) as delete_operations,
                    SUM(noop_operations) as noop_operations,
                    SUM(conflict_resolution_count) as conflicts_resolved,
                    AVG(learning_efficiency) as avg_learning_efficiency,
                    COUNT(*) as active_days
                FROM memory_master.evolution_insights
                WHERE user_id = :user_id 
                    AND date BETWEEN :start_date AND :end_date
            """)
            
            result = self.db_session.execute(stats_sql, {
                "user_id": user_id,
                "start_date": start_date,
                "end_date": end_date
            }).fetchone()
            
            if result:
                return {
                    "period_days": days,
                    "total_operations": result.total_operations or 0,
                    "add_operations": result.add_operations or 0,
                    "update_operations": result.update_operations or 0,
                    "delete_operations": result.delete_operations or 0,
                    "noop_operations": result.noop_operations or 0,
                    "conflicts_resolved": result.conflicts_resolved or 0,
                    "learning_efficiency": float(result.avg_learning_efficiency or 0.0),
                    "active_days": result.active_days or 0
                }
            else:
                return {
                    "period_days": days,
                    "total_operations": 0,
                    "message": "No evolution data found for specified period"
                }
                
        except Exception as e:
            logger.error(f"Error retrieving evolution statistics: {e}")
            return {"error": f"Failed to retrieve statistics: {e}"}
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check for evolution service."""
        health = {
            "service": "evolution_service",
            "status": "healthy",
            "config": {
                "enable_evolution": self.config.enable_evolution,
                "similarity_threshold": self.config.similarity_threshold,
                "confidence_threshold": self.config.confidence_threshold,
                "prompt_version": self.config.prompt_version.value
            },
            "session_metrics": self.session_metrics.to_dict()
        }
        
        # Test components
        try:
            # Test fact extractor
            fact_health = await self.fact_extractor.health_check()
            health["fact_extractor"] = fact_health["status"]
            
            # Test similarity analyzer
            similarity_health = await self.similarity_analyzer.health_check()
            health["similarity_analyzer"] = similarity_health["status"]
            
            # Test decision engine
            decision_health = await self.decision_engine.health_check()
            health["decision_engine"] = decision_health["status"]
            
            # Test prompt manager
            prompt_health = await self.prompt_manager.health_check()
            health["prompt_manager"] = prompt_health["status"]
            
            # Check if any component is unhealthy
            component_statuses = [
                fact_health["status"], similarity_health["status"],
                decision_health["status"], prompt_health["status"]
            ]
            
            if "unhealthy" in component_statuses:
                health["status"] = "unhealthy"
            elif "degraded" in component_statuses:
                health["status"] = "degraded"
            
        except Exception as e:
            health["status"] = "unhealthy"
            health["error"] = str(e)
        
        return health