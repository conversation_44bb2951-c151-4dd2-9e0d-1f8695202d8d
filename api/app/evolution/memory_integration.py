"""
Memory Integration for Evolution Intelligence.

This module provides the integration wrapper for enhancing the existing add_memories()
function with evolution intelligence while preserving all existing functionality.
"""

import logging
import asyncio
import time
import os
from typing import Dict, Any, List, Optional, Tuple

from .evolution_service import EvolutionService, EvolutionConfig, EvolutionResult
from .fact_extractor import ExtractionContext

logger = logging.getLogger(__name__)

class MemoryEvolutionIntegration:
    """
    Integration wrapper for adding evolution intelligence to memory operations.
    
    Features:
    - Feature flag control for gradual rollout
    - Preserves existing functionality completely
    - Adds detailed logging for evolution operations
    - Graceful degradation when evolution fails
    - Performance monitoring and timeouts
    """
    
    def __init__(self, db_session=None):
        """Initialize memory evolution integration."""
        self.evolution_service = None
        self.config = EvolutionConfig()
        self.db_session = db_session
        
        # Feature flag configuration
        self.feature_flag_enabled = os.getenv("EVOLUTION_INTELLIGENCE_ENABLED", "false").lower() == "true"
        self.evolution_timeout = float(os.getenv("EVOLUTION_TIMEOUT", "30.0"))
        
        # Initialize evolution service only if enabled
        if self.feature_flag_enabled:
            try:
                self.evolution_service = EvolutionService(
                    config=self.config,
                    db_session=db_session
                )
                logger.info("Evolution intelligence enabled via feature flag")
            except Exception as e:
                logger.error(f"Failed to initialize evolution service: {e}")
                self.feature_flag_enabled = False
        else:
            logger.info("Evolution intelligence disabled via feature flag")
    
    async def enhance_memory_operation(
        self,
        text: str,
        user_id: str,
        client_name: str,
        existing_memories: Optional[List[Dict[str, Any]]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Enhance memory operation with evolution intelligence.
        
        Args:
            text: Input text to process
            user_id: User identifier
            client_name: Client application name
            existing_memories: Existing memories for context
            context: Additional context
            
        Returns:
            Tuple of (evolution_processed, evolution_result)
            - evolution_processed: True if evolution was applied
            - evolution_result: Evolution processing results
        """
        # Return early if evolution not enabled
        if not self.feature_flag_enabled or not self.evolution_service:
            logger.debug("Evolution intelligence disabled, skipping enhancement")
            return False, {"status": "disabled", "reason": "feature_flag_disabled"}
        
        start_time = time.time()
        
        try:
            logger.info(f"Starting evolution enhancement for user {user_id}")
            
            # Process with timeout
            evolution_result = await asyncio.wait_for(
                self.evolution_service.process_memory_evolution(
                    text=text,
                    user_id=user_id,
                    client_name=client_name,
                    existing_memories=existing_memories,
                    context=context
                ),
                timeout=self.evolution_timeout
            )
            
            processing_time = time.time() - start_time
            logger.info(f"Evolution processing completed in {processing_time:.3f}s")
            
            # Log evolution metrics
            self._log_evolution_metrics(evolution_result, user_id, client_name)
            
            return True, {
                "status": "success",
                "evolution_result": evolution_result,
                "processing_time": processing_time
            }
            
        except asyncio.TimeoutError:
            processing_time = time.time() - start_time
            logger.warning(f"Evolution processing timeout after {processing_time:.3f}s")
            return False, {
                "status": "timeout",
                "processing_time": processing_time,
                "reason": "evolution_timeout"
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Evolution processing error after {processing_time:.3f}s: {e}")
            return False, {
                "status": "error",
                "error": str(e),
                "processing_time": processing_time,
                "reason": "evolution_error"
            }
    
    def _log_evolution_metrics(
        self,
        evolution_result: EvolutionResult,
        user_id: str,
        client_name: str
    ):
        """Log evolution metrics for monitoring."""
        metrics = evolution_result.metrics.to_dict()
        
        logger.info(f"EVOLUTION_METRICS: user={user_id}, client={client_name}, "
                   f"total_ops={metrics['total_operations']}, "
                   f"add={metrics['add_operations']}, "
                   f"update={metrics['update_operations']}, "
                   f"delete={metrics['delete_operations']}, "
                   f"noop={metrics['noop_operations']}, "
                   f"conflicts={metrics['conflicts_detected']}, "
                   f"efficiency={metrics['learning_efficiency']:.3f}, "
                   f"time={metrics['processing_time']:.3f}s")
        
        # Log individual operations for debugging
        for i, operation in enumerate(evolution_result.operations):
            logger.debug(f"EVOLUTION_OP_{i+1}: {operation['operation_type']} - "
                        f"fact='{operation['candidate_fact'][:50]}...', "
                        f"confidence={operation['confidence_score']:.3f}")
    
    async def get_user_memories_for_context(
        self,
        user_id: str,
        client_name: str,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get recent user memories for evolution context.
        
        This method can be called before enhance_memory_operation to provide
        existing memories for similarity analysis.
        """
        if not self.db_session:
            logger.warning("No database session available for memory context")
            return []
        
        try:
            from ..models import Memory, User, App
            from ..database import SessionLocal
            
            # Get user and app
            user = self.db_session.query(User).filter(User.user_id == user_id).first()
            if not user:
                logger.warning(f"User {user_id} not found for memory context")
                return []
            
            app = self.db_session.query(App).filter(App.name == client_name).first()
            if not app:
                logger.warning(f"App {client_name} not found for memory context")
                return []
            
            # Get recent memories
            memories = self.db_session.query(Memory).filter(
                Memory.user_id == user.id,
                Memory.app_id == app.id,
                Memory.state == "active"
            ).order_by(Memory.created_at.desc()).limit(limit).all()
            
            # Convert to format expected by evolution service
            memory_list = []
            for memory in memories:
                memory_list.append({
                    "id": str(memory.id),
                    "content": memory.content,
                    "metadata": memory.metadata_ or {},
                    "created_at": memory.created_at.isoformat() if memory.created_at else None
                })
            
            logger.info(f"Retrieved {len(memory_list)} memories for evolution context")
            return memory_list
            
        except Exception as e:
            logger.error(f"Error retrieving memories for context: {e}")
            return []
    
    def is_evolution_enabled(self) -> bool:
        """Check if evolution intelligence is enabled."""
        return self.feature_flag_enabled and self.evolution_service is not None
    
    async def get_evolution_statistics(
        self,
        user_id: str,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get evolution statistics for user."""
        if not self.evolution_service:
            return {"error": "Evolution service not available"}
        
        return await self.evolution_service.get_evolution_statistics(user_id, days)
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check for evolution integration."""
        health = {
            "service": "memory_evolution_integration",
            "status": "healthy",
            "feature_flag_enabled": self.feature_flag_enabled,
            "evolution_timeout": self.evolution_timeout
        }
        
        if self.evolution_service:
            try:
                evolution_health = await self.evolution_service.health_check()
                health["evolution_service"] = evolution_health
                
                if evolution_health["status"] != "healthy":
                    health["status"] = evolution_health["status"]
                    
            except Exception as e:
                health["status"] = "unhealthy"
                health["evolution_service_error"] = str(e)
        else:
            health["evolution_service"] = "disabled"
        
        return health

# Singleton instance for global use
_memory_evolution_integration: Optional[MemoryEvolutionIntegration] = None

def get_memory_evolution_integration(db_session=None) -> MemoryEvolutionIntegration:
    """Get singleton memory evolution integration instance."""
    global _memory_evolution_integration
    
    if _memory_evolution_integration is None:
        _memory_evolution_integration = MemoryEvolutionIntegration(db_session)
    
    return _memory_evolution_integration

async def enhance_add_memories(
    text: str,
    user_id: str,
    client_name: str,
    db_session=None,
    existing_memories: Optional[List[Dict[str, Any]]] = None,
    context: Optional[Dict[str, Any]] = None
) -> Tuple[bool, Dict[str, Any]]:
    """
    Convenience function to enhance add_memories operation with evolution intelligence.
    
    This is the main integration point that should be called from the existing
    add_memories() function to add evolution capabilities.
    
    Args:
        text: Input text to process
        user_id: User identifier
        client_name: Client application name
        db_session: Database session for operations
        existing_memories: Existing memories for context
        context: Additional context for processing
        
    Returns:
        Tuple of (evolution_processed, evolution_result)
    """
    integration = get_memory_evolution_integration(db_session)
    
    # Get existing memories for context if not provided
    if existing_memories is None and integration.is_evolution_enabled():
        existing_memories = await integration.get_user_memories_for_context(
            user_id, client_name
        )
    
    return await integration.enhance_memory_operation(
        text=text,
        user_id=user_id,
        client_name=client_name,
        existing_memories=existing_memories,
        context=context
    )