"""
Similarity Analysis Module for Memory Master v2 Evolution Intelligence.

Implements semantic similarity analysis to compare candidate facts against existing 
memories using vector embeddings for intelligent memory evolution decisions.
"""

import logging
import numpy as np
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import asyncio
from functools import lru_cache
import hashlib
import json

# Vector similarity libraries
try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

import openai
from anthropic import AsyncAnthropic

from app.cache.cache_service import cache_service
from app.connection_pools import get_llm_pool_manager
from app.circuit_breakers import get_circuit_breaker_manager
from aiobreaker import CircuitBreakerError

logger = logging.getLogger(__name__)

@dataclass
class MemoryRecord:
    """Represents a memory record for similarity analysis."""
    id: str
    content: str
    embedding: Optional[np.ndarray] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class SimilarityResult:
    """Result of similarity analysis."""
    memory_id: str
    content: str
    similarity_score: float
    metadata: Optional[Dict[str, Any]] = None

class SimilarityAnalyzer:
    """
    Semantic similarity analysis for memory evolution decisions.
    
    Uses vector embeddings to compare candidate facts against existing memories
    with configurable similarity thresholds for evolution operations.
    """
    
    HIGH_SIMILARITY_THRESHOLD = 0.85
    MEDIUM_SIMILARITY_THRESHOLD = 0.70
    LOW_SIMILARITY_THRESHOLD = 0.50
    
    def __init__(
        self,
        openai_api_key: Optional[str] = None,
        anthropic_api_key: Optional[str] = None,
        embedding_model: str = "text-embedding-3-large",
        similarity_threshold: float = 0.85,
        use_faiss: bool = True
    ):
        """
        Initialise similarity analyzer.
        
        Args:
            openai_api_key: OpenAI API key for embeddings
            anthropic_api_key: Anthropic API key (future use)
            embedding_model: Model for generating embeddings
            similarity_threshold: Threshold for high similarity detection
            use_faiss: Whether to use FAISS for efficient similarity search
        """
        from app.config import get_settings
        
        settings = get_settings()
        self.openai_api_key = openai_api_key or settings.openai_api_key
        self.anthropic_api_key = anthropic_api_key or settings.anthropic_api_key
        self.llm_pool = get_llm_pool_manager()
        self.circuit_breaker_manager = get_circuit_breaker_manager()
        self.embedding_model = embedding_model
        self.similarity_threshold = similarity_threshold
        self.use_faiss = use_faiss and FAISS_AVAILABLE
        
        # Initialize Sentence-BERT as fallback
        self.sentence_transformer = None
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                self.sentence_transformer = SentenceTransformer('all-MiniLM-L6-v2')
                logger.info("Initialized Sentence-BERT model")
            except Exception as e:
                logger.warning(f"Failed to initialize Sentence-BERT: {e}")
        
        # FAISS index for efficient similarity search
        self.faiss_index = None
        self.memory_index_map = {}  # Maps FAISS index to memory IDs
        
        # Embedding cache
        self._embedding_cache = {}
        
        logger.info(f"SimilarityAnalyzer initialized with model: {embedding_model}")
    
    async def find_similar_memories(
        self,
        candidate_fact: str,
        existing_memories: List[MemoryRecord],
        top_k: int = 5
    ) -> List[SimilarityResult]:
        """
        Find most similar memories to candidate fact.
        
        Args:
            candidate_fact: The fact to compare against existing memories
            existing_memories: List of existing memory records
            top_k: Number of top similar memories to return
            
        Returns:
            List of similarity results ordered by similarity score
        """
        if not existing_memories:
            return []
        
        # Get embedding for candidate fact
        candidate_embedding = await self._get_embedding(candidate_fact)
        if candidate_embedding is None:
            logger.error("Failed to generate embedding for candidate fact")
            return []
        
        # Calculate similarities
        similarities = []
        
        if self.use_faiss and len(existing_memories) > 100:
            # Use FAISS for efficient search with large datasets
            similarities = await self._faiss_similarity_search(
                candidate_embedding, existing_memories, top_k
            )
        else:
            # Use direct computation for smaller datasets
            similarities = await self._direct_similarity_search(
                candidate_embedding, existing_memories
            )
        
        # Sort by similarity score and return top k
        similarities.sort(key=lambda x: x.similarity_score, reverse=True)
        return similarities[:top_k]
    
    async def calculate_similarity_score(
        self,
        text1: str,
        text2: str
    ) -> float:
        """
        Calculate similarity score between two texts.
        
        Args:
            text1: First text
            text2: Second text
            
        Returns:
            Cosine similarity score between 0.0 and 1.0
        """
        # Get embeddings
        embedding1 = await self._get_embedding(text1)
        embedding2 = await self._get_embedding(text2)
        
        if embedding1 is None or embedding2 is None:
            return 0.0
        
        # Calculate cosine similarity
        return self._cosine_similarity(embedding1, embedding2)
    
    async def batch_similarity_analysis(
        self,
        candidate_facts: List[str],
        existing_memories: List[MemoryRecord],
        similarity_threshold: Optional[float] = None
    ) -> Dict[str, List[SimilarityResult]]:
        """
        Perform batch similarity analysis for multiple candidate facts.
        
        Args:
            candidate_facts: List of facts to analyze
            existing_memories: List of existing memory records
            similarity_threshold: Override default similarity threshold
            
        Returns:
            Dictionary mapping each fact to its similarity results
        """
        threshold = similarity_threshold or self.similarity_threshold
        results = {}
        
        # Generate embeddings for all candidate facts
        candidate_embeddings = await self._batch_get_embeddings(candidate_facts)
        
        for i, fact in enumerate(candidate_facts):
            if candidate_embeddings[i] is not None:
                similar_memories = await self.find_similar_memories(
                    fact, existing_memories
                )
                # Filter by threshold
                filtered_memories = [
                    mem for mem in similar_memories 
                    if mem.similarity_score >= threshold
                ]
                results[fact] = filtered_memories
            else:
                results[fact] = []
        
        return results
    
    async def _get_embedding(self, text: str) -> Optional[np.ndarray]:
        """Get embedding for text with Redis and local caching."""
        # Check local cache first (fastest)
        cache_key = self._get_cache_key(text)
        if cache_key in self._embedding_cache:
            return self._embedding_cache[cache_key]
        
        # Check Redis cache
        model_key = f"{self.embedding_model}_{text}"
        cached_embedding = await cache_service.get_embedding(text, self.embedding_model)
        if cached_embedding is not None:
            embedding_array = np.array(cached_embedding, dtype=np.float32)
            # Also cache locally for faster subsequent access
            self._embedding_cache[cache_key] = embedding_array
            return embedding_array
        
        embedding = None
        
        # Try OpenAI first
        if self.openai_api_key:
            try:
                embedding = await self._get_openai_embedding(text)
            except Exception as e:
                logger.warning(f"OpenAI embedding failed: {e}")
        
        # Fallback to Sentence-BERT
        if embedding is None and self.sentence_transformer:
            try:
                embedding = await self._get_sentence_bert_embedding(text)
            except Exception as e:
                logger.warning(f"Sentence-BERT embedding failed: {e}")
        
        # Cache the embedding in both local and Redis cache
        if embedding is not None:
            self._embedding_cache[cache_key] = embedding
            # Cache in Redis (convert numpy array to list for JSON serialization)
            await cache_service.set_embedding(text, embedding.tolist(), self.embedding_model)
        
        return embedding
    
    async def _get_openai_embedding(self, text: str) -> Optional[np.ndarray]:
        """Get embedding using OpenAI API with connection pooling and circuit breaker protection."""
        if not self.openai_api_key:
            return None
        
        try:
            # Use circuit breaker to protect OpenAI API calls
            return await self.circuit_breaker_manager.call_with_circuit_breaker(
                "openai_api",
                self._openai_embedding_call,
                text
            )
        except CircuitBreakerError as e:
            logger.warning(f"OpenAI embedding call blocked by circuit breaker: {e}")
            # Return None for graceful degradation - will fallback to Sentence-BERT
            return None
        except Exception as e:
            logger.error(f"OpenAI embedding error: {e}")
            return None
    
    async def _openai_embedding_call(self, text: str) -> np.ndarray:
        """Protected OpenAI embedding API call method"""
        async with self.llm_pool.get_openai_client(self.openai_api_key) as client:
            response = await client.embeddings.create(
                input=text,
                model=self.embedding_model
            )
            return np.array(response.data[0].embedding, dtype=np.float32)
    
    async def _get_sentence_bert_embedding(self, text: str) -> Optional[np.ndarray]:
        """Get embedding using Sentence-BERT."""
        try:
            # Run in thread pool since sentence-transformers is sync
            loop = asyncio.get_event_loop()
            embedding = await loop.run_in_executor(
                None, self.sentence_transformer.encode, text
            )
            return np.array(embedding, dtype=np.float32)
        except Exception as e:
            logger.error(f"Sentence-BERT embedding error: {e}")
            return None
    
    async def _batch_get_embeddings(self, texts: List[str]) -> List[Optional[np.ndarray]]:
        """Get embeddings for multiple texts efficiently with circuit breaker protection."""
        embeddings = []
        
        if self.openai_api_key:
            try:
                # Use circuit breaker to protect batch OpenAI API calls
                return await self.circuit_breaker_manager.call_with_circuit_breaker(
                    "openai_api",
                    self._openai_batch_embedding_call,
                    texts
                )
            except CircuitBreakerError as e:
                logger.warning(f"Batch OpenAI embedding blocked by circuit breaker: {e}")
                # Fallback to individual requests with graceful degradation
                for text in texts:
                    embedding = await self._get_embedding(text)
                    embeddings.append(embedding)
            except Exception as e:
                logger.warning(f"Batch OpenAI embedding failed: {e}")
                # Fallback to individual requests
                for text in texts:
                    embedding = await self._get_embedding(text)
                    embeddings.append(embedding)
        else:
            # Use individual requests
            for text in texts:
                embedding = await self._get_embedding(text)
                embeddings.append(embedding)
        
        return embeddings
    
    async def _openai_batch_embedding_call(self, texts: List[str]) -> List[Optional[np.ndarray]]:
        """Protected OpenAI batch embedding API call method"""
        async with self.llm_pool.get_openai_client(self.openai_api_key) as client:
            response = await client.embeddings.create(
                input=texts,
                model=self.embedding_model
            )
            return [
                np.array(data.embedding, dtype=np.float32) 
                for data in response.data
            ]
    
    async def _direct_similarity_search(
        self,
        candidate_embedding: np.ndarray,
        existing_memories: List[MemoryRecord]
    ) -> List[SimilarityResult]:
        """Direct similarity search without FAISS, with batch caching optimization."""
        # Prepare data for batch similarity caching
        memory_embeddings = []
        
        for memory in existing_memories:
            if memory.embedding is None:
                # Generate embedding if not cached
                memory.embedding = await self._get_embedding(memory.content)
            
            if memory.embedding is not None:
                memory_embeddings.append((memory.id, memory.embedding.tolist()))
        
        # Check if we have cached similarity results for this batch
        cached_similarities = await cache_service.get_similarity_batch(
            candidate_embedding.tolist(), memory_embeddings
        )
        
        if cached_similarities is not None:
            # Return cached results
            similarities = []
            for memory_id, score in cached_similarities:
                memory = next((m for m in existing_memories if m.id == memory_id), None)
                if memory:
                    similarities.append(SimilarityResult(
                        memory_id=memory.id,
                        content=memory.content,
                        similarity_score=score,
                        metadata=memory.metadata
                    ))
            return similarities
        
        # Cache miss - calculate similarities and cache results
        similarities = []
        calculated_similarities = []
        
        for memory in existing_memories:
            if memory.embedding is not None:
                similarity_score = self._cosine_similarity(
                    candidate_embedding, memory.embedding
                )
                similarities.append(SimilarityResult(
                    memory_id=memory.id,
                    content=memory.content,
                    similarity_score=similarity_score,
                    metadata=memory.metadata
                ))
                calculated_similarities.append((memory.id, similarity_score))
        
        # Cache the calculated similarities for future use
        if calculated_similarities:
            await cache_service.set_similarity_batch(
                candidate_embedding.tolist(),
                memory_embeddings,
                calculated_similarities
            )
        
        return similarities
    
    async def _faiss_similarity_search(
        self,
        candidate_embedding: np.ndarray,
        existing_memories: List[MemoryRecord],
        top_k: int
    ) -> List[SimilarityResult]:
        """FAISS-based similarity search for large datasets."""
        if not FAISS_AVAILABLE:
            return await self._direct_similarity_search(candidate_embedding, existing_memories)
        
        # Build or update FAISS index
        await self._build_faiss_index(existing_memories)
        
        if self.faiss_index is None:
            return []
        
        # Search
        scores, indices = self.faiss_index.search(
            candidate_embedding.reshape(1, -1), top_k
        )
        
        similarities = []
        for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
            if idx != -1 and idx < len(existing_memories):
                memory = existing_memories[idx]
                similarities.append(SimilarityResult(
                    memory_id=memory.id,
                    content=memory.content,
                    similarity_score=float(score),
                    metadata=memory.metadata
                ))
        
        return similarities
    
    async def _build_faiss_index(self, memories: List[MemoryRecord]):
        """Build FAISS index for efficient similarity search."""
        if not memories:
            return
        
        # Get embeddings for all memories
        embeddings = []
        for memory in memories:
            if memory.embedding is None:
                memory.embedding = await self._get_embedding(memory.content)
            if memory.embedding is not None:
                embeddings.append(memory.embedding)
        
        if not embeddings:
            return
        
        # Build FAISS index
        embedding_matrix = np.vstack(embeddings).astype(np.float32)
        dimension = embedding_matrix.shape[1]
        
        # Use IndexFlatIP for cosine similarity (with normalized vectors)
        self.faiss_index = faiss.IndexFlatIP(dimension)
        
        # Normalize embeddings for cosine similarity
        faiss.normalize_L2(embedding_matrix)
        
        # Add to index
        self.faiss_index.add(embedding_matrix)
        
        logger.info(f"Built FAISS index with {len(embeddings)} embeddings")
    
    @staticmethod
    def _cosine_similarity(embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """Calculate cosine similarity between two embeddings."""
        # Normalize embeddings
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        # Calculate cosine similarity
        similarity = np.dot(embedding1, embedding2) / (norm1 * norm2)
        
        # Ensure result is between 0 and 1
        return max(0.0, min(1.0, float(similarity)))
    
    def _get_cache_key(self, text: str) -> str:
        """Generate cache key for text."""
        return hashlib.md5(f"{self.embedding_model}:{text}".encode()).hexdigest()
    
    def clear_cache(self):
        """Clear embedding cache."""
        self._embedding_cache.clear()
        self.faiss_index = None
        self.memory_index_map.clear()
        logger.info("Similarity analyzer cache cleared")
    
    def get_similarity_category(self, score: float) -> str:
        """Categorise similarity score."""
        if score >= self.HIGH_SIMILARITY_THRESHOLD:
            return "high"
        elif score >= self.MEDIUM_SIMILARITY_THRESHOLD:
            return "medium"
        elif score >= self.LOW_SIMILARITY_THRESHOLD:
            return "low"
        else:
            return "very_low"
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on similarity analyzer."""
        health = {
            "service": "similarity_analyzer",
            "status": "healthy",
            "embedding_model": self.embedding_model,
            "similarity_threshold": self.similarity_threshold,
            "api_keys": {
                "openai": bool(self.openai_api_key),
                "anthropic": bool(self.anthropic_api_key)
            },
            "clients": {
                "sentence_transformers": self.sentence_transformer is not None,
                "faiss": FAISS_AVAILABLE and self.use_faiss
            },
            "cache_size": len(self._embedding_cache)
        }
        
        # Test embedding generation
        try:
            test_embedding = await self._get_embedding("test text")
            if test_embedding is not None:
                health["test_embedding"] = "passed"
                health["embedding_dimension"] = len(test_embedding)
            else:
                health["status"] = "degraded"
                health["test_embedding"] = "failed"
        except Exception as e:
            health["status"] = "unhealthy"
            health["test_embedding"] = f"failed: {e}"
        
        return health
    
    async def analyze_similarities_batch(
        self,
        candidate_facts: List[str],
        existing_memories: List[MemoryRecord],
        top_k: int = 5,
        max_concurrent: int = 10
    ) -> List[List[SimilarityResult]]:
        """
        Analyze similarities for multiple candidate facts concurrently.
        
        Args:
            candidate_facts: List of candidate facts to analyze
            existing_memories: List of existing memory records
            top_k: Number of top similar memories to return per fact
            max_concurrent: Maximum concurrent similarity analyses
            
        Returns:
            List of similarity results for each candidate fact
        """
        if not candidate_facts or not existing_memories:
            return [[] for _ in candidate_facts]
        
        # Process in batches to manage concurrency
        results = []
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def analyze_with_semaphore(fact: str) -> List[SimilarityResult]:
            async with semaphore:
                try:
                    return await self.find_similar_memories(fact, existing_memories, top_k)
                except Exception as e:
                    logger.error(f"Batch similarity analysis failed for fact: {fact[:50]}... Error: {e}")
                    return []
        
        # Create tasks for all analyses
        tasks = [analyze_with_semaphore(fact) for fact in candidate_facts]
        
        # Execute concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Similarity analysis failed for fact {i}: {result}")
                processed_results.append([])
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def build_memory_index_batch(
        self,
        memory_records: List[MemoryRecord],
        batch_size: int = 50
    ) -> bool:
        """
        Build FAISS index from memory records in batches for efficient similarity search.
        
        Args:
            memory_records: List of memory records to index
            batch_size: Number of records to process per batch
            
        Returns:
            True if index was built successfully
        """
        if not memory_records:
            return False
        
        try:
            # Get embeddings for all memory contents in batches
            all_embeddings = []
            
            for i in range(0, len(memory_records), batch_size):
                batch = memory_records[i:i + batch_size]
                batch_texts = [record.content for record in batch]
                
                # Get embeddings for this batch
                batch_embeddings = await self._batch_get_embeddings(batch_texts)
                
                # Filter out None embeddings and store valid ones
                for j, embedding in enumerate(batch_embeddings):
                    if embedding is not None:
                        memory_records[i + j].embedding = embedding
                        all_embeddings.append(embedding)
            
            if not all_embeddings:
                logger.warning("No valid embeddings generated for memory indexing")
                return False
            
            # Build FAISS index if available
            if self.use_faiss and FAISS_AVAILABLE:
                dimension = len(all_embeddings[0])
                self.faiss_index = faiss.IndexFlatIP(dimension)  # Inner product for cosine similarity
                
                # Convert to numpy array and normalize for cosine similarity
                embeddings_array = np.array(all_embeddings, dtype=np.float32)
                faiss.normalize_L2(embeddings_array)
                
                # Add to index
                self.faiss_index.add(embeddings_array)
                
                # Update memory index mapping
                self.memory_index_map = {
                    i: memory_records[i].id 
                    for i, record in enumerate(memory_records)
                    if record.embedding is not None
                }
                
                logger.info(f"Built FAISS index with {len(all_embeddings)} embeddings")
                return True
            
            logger.info(f"Generated {len(all_embeddings)} embeddings (FAISS not available)")
            return True
            
        except Exception as e:
            logger.error(f"Failed to build memory index: {e}")
            return False
    
    async def find_similar_memories_optimized(
        self,
        candidate_fact: str,
        top_k: int = 5
    ) -> List[SimilarityResult]:
        """
        Find similar memories using optimized FAISS index search.
        
        Args:
            candidate_fact: Fact to find similarities for
            top_k: Number of top similar memories to return
            
        Returns:
            List of similarity results
        """
        if not self.faiss_index:
            logger.warning("FAISS index not available, falling back to standard similarity")
            return []
        
        try:
            # Get embedding for candidate fact
            candidate_embedding = await self._get_embedding(candidate_fact)
            if candidate_embedding is None:
                return []
            
            # Normalize for cosine similarity
            candidate_embedding = np.array([candidate_embedding], dtype=np.float32)
            faiss.normalize_L2(candidate_embedding)
            
            # Search index
            scores, indices = self.faiss_index.search(candidate_embedding, top_k)
            
            # Convert to similarity results
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx in self.memory_index_map and score > 0:  # Filter out invalid results
                    memory_id = self.memory_index_map[idx]
                    results.append(SimilarityResult(
                        memory_id=memory_id,
                        content="",  # Content not stored in index mapping
                        similarity_score=float(score)
                    ))
            
            return results
            
        except Exception as e:
            logger.error(f"Optimized similarity search failed: {e}")
            return []
    
    def get_batch_processing_stats(self) -> Dict[str, Any]:
        """Get statistics about batch processing capabilities."""
        return {
            "faiss_available": FAISS_AVAILABLE and self.use_faiss,
            "faiss_index_size": self.faiss_index.ntotal if self.faiss_index else 0,
            "memory_index_mapping_size": len(self.memory_index_map),
            "embedding_cache_size": len(self._embedding_cache),
            "sentence_transformers_available": SENTENCE_TRANSFORMERS_AVAILABLE,
            "batch_processing_limits": {
                "max_concurrent_analyses": 50,
                "recommended_batch_size": 25,
                "max_embedding_batch_size": 100
            }
        }