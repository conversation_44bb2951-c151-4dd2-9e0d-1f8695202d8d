"""
Custom Prompts Development and Optimization for Memory Master v2 Evolution Intelligence.

Implements versioned prompt templates for fact extraction and memory update decisions
with domain-specific optimization for technical conversations.
"""

import json
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum
import hashlib

logger = logging.getLogger(__name__)

class PromptVersion(Enum):
    """Prompt version identifiers for A/B testing."""
    V1_BASIC = "v1_basic"
    V2_ENHANCED = "v2_enhanced"
    V3_TECHNICAL = "v3_technical"

@dataclass
class PromptTemplate:
    """Template for LLM prompts with versioning."""
    name: str
    version: PromptVersion
    template: str
    description: str
    variables: List[str]
    examples: List[Dict[str, str]]
    validation_schema: Optional[Dict[str, Any]] = None

class PromptManager:
    """
    Manages versioned prompts for memory evolution operations.
    
    Provides prompt templates optimized for technical domain conversations
    with A/B testing capabilities and output validation.
    """
    
    def __init__(self, default_version: PromptVersion = PromptVersion.V3_TECHNICAL):
        """Initialize prompt manager with default version."""
        self.default_version = default_version
        self.prompts = self._initialize_prompts()
        
        logger.info(f"PromptManager initialized with default version: {default_version.value}")
    
    def _initialize_prompts(self) -> Dict[str, Dict[PromptVersion, PromptTemplate]]:
        """Initialize all prompt templates."""
        return {
            "fact_extraction": self._create_fact_extraction_prompts(),
            "memory_decision": self._create_memory_decision_prompts(),
            "conflict_detection": self._create_conflict_detection_prompts(),
            "standalone_evaluation": self._create_standalone_evaluation_prompts()
        }
    
    def _create_fact_extraction_prompts(self) -> Dict[PromptVersion, PromptTemplate]:
        """Create fact extraction prompt templates."""
        
        v1_basic = PromptTemplate(
            name="fact_extraction_basic",
            version=PromptVersion.V1_BASIC,
            template="""Extract important facts from this conversation:
            
Latest message: {latest_message}
Recent messages: {recent_messages}
User background: {user_context}

Return JSON with extracted facts.""",
            description="Basic fact extraction prompt",
            variables=["latest_message", "recent_messages", "user_context"],
            examples=[]
        )
        
        v2_enhanced = PromptTemplate(
            name="fact_extraction_enhanced",
            version=PromptVersion.V2_ENHANCED,
            template="""You are an expert at extracting meaningful, lasting facts from technical conversations.

Context Information:
- Latest message: {latest_message}
- Recent conversation: {recent_messages}
- User background: {user_context}

Extract facts about:
1. Technical preferences (languages, frameworks, tools)
2. Project decisions and requirements
3. Learning goals and skill development  
4. Work context and professional information
5. Problem-solving approaches and methodologies

Ignore:
- Casual conversation and greetings
- Temporary troubleshooting steps
- Widely-known information

Output JSON format:
{{
  "facts": [
    {{
      "fact": "Complete standalone statement",
      "category": "technical_preference|project_decision|learning_goal|work_context|methodology",
      "confidence": 0.9,
      "relevance_score": 0.85
    }}
  ]
}}""",
            description="Enhanced fact extraction with categorization",
            variables=["latest_message", "recent_messages", "user_context"],
            examples=[
                {
                    "input": "I've been working with React for 2 years now and love using TypeScript with it",
                    "output": '{"facts": [{"fact": "Has 2 years experience with React", "category": "work_context", "confidence": 0.95, "relevance_score": 0.9}, {"fact": "Prefers TypeScript when working with React", "category": "technical_preference", "confidence": 0.9, "relevance_score": 0.85}]}'
                }
            ]
        )
        
        v3_technical = PromptTemplate(
            name="fact_extraction_technical",
            version=PromptVersion.V3_TECHNICAL,
            template="""You are an expert technical knowledge curator extracting persistent facts from developer conversations.

Conversation Context:
- Latest message: {latest_message}
- Recent conversation history: {recent_messages}
- Developer background: {user_context}

EXTRACTION FOCUS - Technical Domain Facts:

🔧 TECHNICAL PREFERENCES
- Programming languages, frameworks, libraries
- Development tools, IDEs, workflows
- Architecture patterns, coding standards
- Technology stack preferences and aversions

📋 PROJECT CONTEXT  
- Current projects and their tech stacks
- Role responsibilities and team structure
- Business domain and industry context
- Project methodologies (Agile, DevOps, etc.)

🎯 PROFESSIONAL DEVELOPMENT
- Learning goals and skill progression
- Certifications, courses, training plans
- Career aspirations and technical interests
- Knowledge gaps and improvement areas

💼 WORK ENVIRONMENT
- Company culture and technical practices
- Team composition and collaboration tools
- Remote/office work preferences
- Meeting and communication patterns

🧠 METHODOLOGY & APPROACH
- Problem-solving strategies and debugging techniques
- Code review practices and quality standards
- Testing approaches and automation preferences
- Documentation and knowledge sharing habits

EXTRACTION RULES:
✅ INCLUDE: Lasting preferences, established patterns, technical decisions, skill levels, work context
❌ EXCLUDE: Temporary errors, casual chat, obvious facts, procedural conversation, troubleshooting steps

Few-shot Examples:

Input: "I switched our team from Redux to Zustand for state management because Redux felt too boilerplate-heavy for our needs"
Output: {{"facts": [{{"fact": "Switched team from Redux to Zustand for state management", "category": "project_decision", "confidence": 0.95, "relevance_score": 0.9}}, {{"fact": "Finds Redux too boilerplate-heavy", "category": "technical_preference", "confidence": 0.9, "relevance_score": 0.85}}]}}

Input: "I'm learning Rust in my spare time because I want to get into systems programming"  
Output: {{"facts": [{{"fact": "Currently learning Rust programming language", "category": "learning_goal", "confidence": 0.9, "relevance_score": 0.85}}, {{"fact": "Interested in systems programming career direction", "category": "learning_goal", "confidence": 0.85, "relevance_score": 0.8}}]}}

Input: "Our team does code reviews in GitHub and we require at least 2 approvals before merging"
Output: {{"facts": [{{"fact": "Team uses GitHub for code reviews", "category": "work_context", "confidence": 0.95, "relevance_score": 0.9}}, {{"fact": "Team requires minimum 2 approvals before code merge", "category": "methodology", "confidence": 0.95, "relevance_score": 0.85}}]}}

OUTPUT FORMAT:
{{
  "facts": [
    {{
      "fact": "Complete, standalone statement capturing persistent technical information",
      "category": "technical_preference|project_decision|learning_goal|work_context|methodology",
      "confidence": 0.95,
      "relevance_score": 0.90
    }}
  ]
}}""",
            description="Advanced technical domain fact extraction with examples",
            variables=["latest_message", "recent_messages", "user_context"],
            examples=[
                {
                    "input": "I switched our team from Redux to Zustand because Redux felt too boilerplate-heavy",
                    "output": '{"facts": [{"fact": "Switched team from Redux to Zustand for state management", "category": "project_decision", "confidence": 0.95, "relevance_score": 0.9}, {"fact": "Finds Redux too boilerplate-heavy", "category": "technical_preference", "confidence": 0.9, "relevance_score": 0.85}]}'
                },
                {
                    "input": "I'm learning Rust because I want to get into systems programming",
                    "output": '{"facts": [{"fact": "Currently learning Rust programming language", "category": "learning_goal", "confidence": 0.9, "relevance_score": 0.85}, {"fact": "Interested in systems programming", "category": "learning_goal", "confidence": 0.85, "relevance_score": 0.8}]}'
                }
            ],
            validation_schema={
                "type": "object",
                "properties": {
                    "facts": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "fact": {"type": "string", "minLength": 10},
                                "category": {"enum": ["technical_preference", "project_decision", "learning_goal", "work_context", "methodology"]},
                                "confidence": {"type": "number", "minimum": 0, "maximum": 1},
                                "relevance_score": {"type": "number", "minimum": 0, "maximum": 1}
                            },
                            "required": ["fact", "category", "confidence", "relevance_score"]
                        }
                    }
                },
                "required": ["facts"]
            }
        )
        
        return {
            PromptVersion.V1_BASIC: v1_basic,
            PromptVersion.V2_ENHANCED: v2_enhanced,
            PromptVersion.V3_TECHNICAL: v3_technical
        }
    
    def _create_memory_decision_prompts(self) -> Dict[PromptVersion, PromptTemplate]:
        """Create memory update decision prompt templates."""
        
        v3_technical = PromptTemplate(
            name="memory_decision_technical",
            version=PromptVersion.V3_TECHNICAL,
            template="""You are an expert technical memory manager deciding how to handle new developer information.

CONTEXT:
- New fact: {candidate_fact}
- Existing memory: {existing_memory}  
- Similarity score: {similarity_score}

DECISION FRAMEWORK - Choose ONE operation:

🔄 UPDATE: New fact enhances/specifies existing information
- Skill progression: "learning Python" → "proficient in Python for data science"
- Project evolution: "working on API" → "completed REST API with authentication"
- Specification: "uses Docker" → "uses Docker for local development and CI/CD"
- Context addition: "prefers TypeScript" → "prefers TypeScript for large codebases"

➕ ADD: New fact covers different domain/technology
- New technology: existing "uses React" + new "learning Rust" → ADD
- Different domain: existing "web development" + new "machine learning" → ADD  
- New project: existing "e-commerce site" + new "mobile app" → ADD
- Parallel preferences: existing "likes VS Code" + new "uses IntelliJ for Java" → ADD

🗑️ DELETE: New fact contradicts/supersedes existing information  
- Technology migration: "uses Angular" → "switched to React" → DELETE Angular, ADD React
- Role change: "frontend developer" → "full-stack developer" → DELETE frontend, ADD full-stack
- Company change: "works at Company A" → "works at Company B" → DELETE A, ADD B
- Skill upgrade: "beginner Python" → "senior Python developer" → DELETE beginner, ADD senior

🚫 NOOP: New fact is redundant/already covered
- Redundant: existing "uses Python" + new "codes in Python" → NOOP
- Already covered: existing "React expert" + new "knows React" → NOOP
- Too vague: existing "specific tech stack" + new "likes programming" → NOOP

TECHNICAL DECISION RULES:
• Migration patterns: "switched from X to Y" → DELETE X, ADD Y
• Skill evolution: Level changes → DELETE old level, ADD new level  
• Role progression: Title changes → UPDATE or DELETE+ADD based on scope
• Tool adoption: New tools in same category → ADD (developers use multiple tools)
• Framework changes: Complete migration → DELETE old, ADD new
• Learning progression: "learning X" → "competent in X" → UPDATE
• Contradictory preferences: "loves X" → "prefers Y over X" → DELETE X, ADD Y

OUTPUT FORMAT:
{{
  "operation": "ADD|UPDATE|DELETE|NOOP",
  "reasoning": "Detailed technical explanation of the decision with specific rule applied",
  "confidence": 0.95,
  "conflict_analysis": {{
    "is_conflict": true/false,
    "conflict_type": "temporal|preference_reversal|factual_contradiction|capability_change|none",
    "confidence": 0.9
  }}
}}""",
            description="Technical domain memory update decisions",
            variables=["candidate_fact", "existing_memory", "similarity_score"],
            examples=[
                {
                    "scenario": "Technology migration",
                    "input": "Candidate: 'Switched team to TypeScript', Existing: 'Team uses JavaScript'",
                    "output": '{"operation": "DELETE", "reasoning": "Technology migration from JavaScript to TypeScript - old technology superseded", "confidence": 0.9, "conflict_analysis": {"is_conflict": true, "conflict_type": "factual_contradiction", "confidence": 0.85}}'
                }
            ]
        )
        
        return {PromptVersion.V3_TECHNICAL: v3_technical}
    
    def _create_conflict_detection_prompts(self) -> Dict[PromptVersion, PromptTemplate]:
        """Create conflict detection prompt templates."""
        
        v3_technical = PromptTemplate(
            name="conflict_detection_technical", 
            version=PromptVersion.V3_TECHNICAL,
            template="""Analyze potential conflicts between technical facts:

Current memory: {existing_memory}
New information: {candidate_fact}
Similarity: {similarity_score}

Detect conflicts:
1. TEMPORAL: Time-based changes (was/now, before/after, switched from/to)
2. PREFERENCE_REVERSAL: Preference changes (loves→hates, prefers X→prefers Y)  
3. FACTUAL_CONTRADICTION: Direct contradictions (uses X vs uses Y, works at A vs works at B)
4. CAPABILITY_CHANGE: Skill level changes (beginner→expert, learning→proficient)

Output:
{{
  "is_conflict": true/false,
  "conflict_type": "temporal|preference_reversal|factual_contradiction|capability_change|none",
  "confidence": 0.85,
  "reasoning": "Explanation of detected conflict"
}}""",
            description="Technical conflict detection",
            variables=["existing_memory", "candidate_fact", "similarity_score"],
            examples=[]
        )
        
        return {PromptVersion.V3_TECHNICAL: v3_technical}
    
    def _create_standalone_evaluation_prompts(self) -> Dict[PromptVersion, PromptTemplate]:
        """Create standalone fact evaluation prompt templates."""
        
        v3_technical = PromptTemplate(
            name="standalone_evaluation_technical",
            version=PromptVersion.V3_TECHNICAL,
            template="""Evaluate if this technical information should be stored as memory:

Fact: {candidate_fact}
Context: {context}

STORAGE CRITERIA - Technical Focus:
✅ STORE if fact is:
• Lasting technical preferences or choices
• Established skills, experience levels, or certifications  
• Project context, roles, or responsibilities
• Technology stack decisions or migrations
• Development methodologies and practices
• Professional goals or learning objectives
• Work environment and team structure

❌ REJECT if fact is:
• Casual conversation or greetings ("Hi", "Thanks")
• Temporary troubleshooting steps ("Try restarting", "Check logs")
• Obvious information ("Python is a programming language") 
• Procedural conversation ("Can you help me", "Let me check")
• Vague statements ("I like coding", "Programming is fun")
• Error messages or temporary issues

EXAMPLES:
✅ "I prefer TypeScript over JavaScript for large projects" → STORE (technical preference)
✅ "I'm a senior React developer with 5 years experience" → STORE (skill/experience)
✅ "Our team uses Agile methodology with 2-week sprints" → STORE (work methodology)
❌ "Can you help me debug this error?" → REJECT (procedural)
❌ "React is a JavaScript library" → REJECT (obvious information)
❌ "Programming is interesting" → REJECT (too vague)

OUTPUT:
{{
  "operation": "ADD|NOOP", 
  "reasoning": "Explanation of storage decision with specific criteria applied",
  "confidence": 0.85
}}""",
            description="Technical standalone fact evaluation",
            variables=["candidate_fact", "context"],
            examples=[
                {
                    "input": "I prefer TypeScript over JavaScript for large projects",
                    "output": '{"operation": "ADD", "reasoning": "Technical preference with specific context - worth storing", "confidence": 0.9}'
                },
                {
                    "input": "Can you help me debug this?", 
                    "output": '{"operation": "NOOP", "reasoning": "Procedural conversation element - not worth storing", "confidence": 0.95}'
                }
            ]
        )
        
        return {PromptVersion.V3_TECHNICAL: v3_technical}
    
    def get_prompt(
        self,
        prompt_type: str,
        version: Optional[PromptVersion] = None,
        **kwargs
    ) -> str:
        """
        Get formatted prompt template.
        
        Args:
            prompt_type: Type of prompt (fact_extraction, memory_decision, etc.)
            version: Prompt version to use (defaults to default_version)
            **kwargs: Variables to substitute in template
            
        Returns:
            Formatted prompt string
        """
        version = version or self.default_version
        
        if prompt_type not in self.prompts:
            raise ValueError(f"Unknown prompt type: {prompt_type}")
        
        if version not in self.prompts[prompt_type]:
            # Fallback to V3_TECHNICAL or first available version
            available_versions = list(self.prompts[prompt_type].keys())
            if PromptVersion.V3_TECHNICAL in available_versions:
                version = PromptVersion.V3_TECHNICAL
            else:
                version = available_versions[0]
            
            logger.warning(f"Version {version} not available for {prompt_type}, using {version}")
        
        template = self.prompts[prompt_type][version]
        
        # Validate required variables
        missing_vars = set(template.variables) - set(kwargs.keys())
        if missing_vars:
            raise ValueError(f"Missing required variables for {prompt_type}: {missing_vars}")
        
        return template.template.format(**kwargs)
    
    def get_prompt_template(
        self,
        prompt_type: str,
        version: Optional[PromptVersion] = None
    ) -> PromptTemplate:
        """Get prompt template object."""
        version = version or self.default_version
        return self.prompts[prompt_type][version]
    
    def list_prompt_types(self) -> List[str]:
        """List available prompt types."""
        return list(self.prompts.keys())
    
    def list_versions(self, prompt_type: str) -> List[PromptVersion]:
        """List available versions for prompt type."""
        return list(self.prompts[prompt_type].keys())
    
    def validate_prompt_output(
        self,
        prompt_type: str,
        output: str,
        version: Optional[PromptVersion] = None
    ) -> bool:
        """Validate LLM output against prompt schema."""
        version = version or self.default_version
        template = self.prompts[prompt_type][version]
        
        if not template.validation_schema:
            return True
        
        try:
            data = json.loads(output)
            # Basic JSON schema validation would go here
            # For now, just check that required keys exist
            required_props = template.validation_schema.get("properties", {})
            return all(key in data for key in template.validation_schema.get("required", []))
        except json.JSONDecodeError:
            return False
    
    def get_prompt_hash(
        self,
        prompt_type: str,
        version: Optional[PromptVersion] = None,
        **kwargs
    ) -> str:
        """Get hash of formatted prompt for caching."""
        prompt = self.get_prompt(prompt_type, version, **kwargs)
        return hashlib.md5(prompt.encode()).hexdigest()
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check for prompt manager."""
        health = {
            "service": "prompt_manager",
            "status": "healthy",
            "default_version": self.default_version.value,
            "prompt_types": self.list_prompt_types(),
            "total_prompts": sum(len(versions) for versions in self.prompts.values())
        }
        
        # Test prompt generation
        try:
            test_prompt = self.get_prompt(
                "fact_extraction",
                latest_message="Test message",
                recent_messages="Test conversation",
                user_context="Test context"
            )
            
            if test_prompt and len(test_prompt) > 100:
                health["test_prompt_generation"] = "passed"
            else:
                health["status"] = "degraded"
                health["test_prompt_generation"] = "failed - prompt too short"
                
        except Exception as e:
            health["status"] = "unhealthy"
            health["test_prompt_generation"] = f"failed: {e}"
        
        return health