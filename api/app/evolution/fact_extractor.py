"""
Fact Extraction Module for Memory Master v2 Evolution Intelligence.

Implements LLM-based fact extraction from conversational input using custom prompts
optimised for technical domain conversations.
"""

import json
import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from functools import lru_cache
import asyncio
import aiohttp
from anthropic import AsyncAnthropic
import openai
from pydantic import BaseModel, ValidationError
from datetime import datetime

from app.connection_pools import get_llm_pool_manager
from app.circuit_breakers import get_circuit_breaker_manager
from aiobreaker import CircuitBreakerError

logger = logging.getLogger(__name__)

@dataclass
class ExtractionContext:
    """Context for fact extraction."""
    latest_message: str
    recent_messages: List[str]
    user_background: Optional[str] = None
    
class ExtractedFact(BaseModel):
    """Structured output for extracted facts."""
    fact: str
    category: str  # technical_preference, project_decision, learning_goal, work_context, methodology
    confidence: float  # 0.0 to 1.0
    relevance_score: float  # 0.0 to 1.0

class FactExtractionResponse(BaseModel):
    """Response structure for fact extraction."""
    facts: List[ExtractedFact]
    processing_time_ms: float
    model_used: str

class FactExtractor:
    """
    LLM-based fact extraction from technical conversations.
    
    Extracts meaningful, lasting facts about:
    - Technical preferences (languages, frameworks, tools)
    - Project decisions and requirements
    - Learning goals and skill development
    - Work context and professional information
    - Problem-solving approaches and methodologies
    """
    
    FACT_EXTRACTION_PROMPT = """You are an expert at extracting important, memorable facts from technical conversations.

Context:
- Latest message: {latest_message}
- Recent conversation: {recent_messages}
- User background: {user_context}

Extract only lasting facts about:
1. Technical preferences (languages, frameworks, tools)
2. Project decisions and requirements  
3. Learning goals and skill development
4. Work context and professional information
5. Problem-solving approaches and methodologies

Ignore:
- Casual greetings and acknowledgments
- Temporary troubleshooting steps
- Procedural conversation elements
- Obvious or widely-known information

Return facts as complete, standalone statements with categories and confidence scores.
Example: "Prefers TypeScript over JavaScript for large projects" (category: technical_preference, confidence: 0.9)

Output format:
{{
  "facts": [
    {{
      "fact": "Complete standalone statement",
      "category": "technical_preference|project_decision|learning_goal|work_context|methodology", 
      "confidence": 0.9,
      "relevance_score": 0.85
    }}
  ]
}}
"""
    
    def __init__(self, anthropic_api_key: Optional[str] = None, openai_api_key: Optional[str] = None):
        """Initialise fact extractor with API keys and connection pooling."""
        from app.config import get_settings
        
        settings = get_settings()
        self.anthropic_api_key = anthropic_api_key or settings.anthropic_api_key
        self.openai_api_key = openai_api_key or settings.openai_api_key
        self.llm_pool = get_llm_pool_manager()
        self.circuit_breaker_manager = get_circuit_breaker_manager()
        self._cache = {}  # Simple in-memory cache
        
        # Rate limiting configuration
        self.max_concurrent_requests = 5
        self.request_delay = 0.1  # 100ms between requests
        
        logger.info("FactExtractor initialized with circuit breaker protection")
        
    async def extract_facts(
        self, 
        context: ExtractionContext,
        model: str = "claude-3-5-sonnet-20241022",
        max_retries: int = 3
    ) -> FactExtractionResponse:
        """
        Extract facts from conversational context.
        
        Args:
            context: Extraction context with messages and background
            model: Model to use for extraction
            max_retries: Maximum retry attempts
            
        Returns:
            Structured fact extraction response
        """
        start_time = time.time()
        
        # Build cache key
        cache_key = self._build_cache_key(context, model)
        if cache_key in self._cache:
            logger.info("Returning cached fact extraction result")
            cached_result = self._cache[cache_key]
            cached_result.processing_time_ms = time.time() - start_time
            return cached_result
        
        # Build prompt
        prompt = self._build_extraction_prompt(context)
        
        # Extract facts with retries
        for attempt in range(max_retries):
            try:
                if model.startswith("claude"):
                    result = await self._extract_with_claude(prompt, model)
                elif model.startswith("gpt"):
                    result = await self._extract_with_openai(prompt, model)
                else:
                    raise ValueError(f"Unsupported model: {model}")
                
                # Validate and cache result
                response = FactExtractionResponse(
                    facts=result,
                    processing_time_ms=(time.time() - start_time) * 1000,
                    model_used=model
                )
                
                self._cache[cache_key] = response
                return response
                
            except Exception as e:
                logger.warning(f"Fact extraction attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    raise
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
        
    async def _extract_with_claude(self, prompt: str, model: str) -> List[ExtractedFact]:
        """Extract facts using Claude with connection pooling and circuit breaker protection."""
        if not self.anthropic_api_key:
            raise ValueError("Anthropic API key not available")
        
        # Use circuit breaker to protect Anthropic API calls
        try:
            return await self.circuit_breaker_manager.call_with_circuit_breaker(
                "anthropic_api",
                self._claude_api_call,
                prompt,
                model
            )
        except CircuitBreakerError as e:
            logger.warning(f"Claude API call blocked by circuit breaker: {e}")
            # Return empty result for graceful degradation
            return []
    
    async def _claude_api_call(self, prompt: str, model: str) -> List[ExtractedFact]:
        """Protected Claude API call method"""
        async with self.llm_pool.get_anthropic_client(self.anthropic_api_key) as client:
            message = await client.messages.create(
                model=model,
                max_tokens=1000,
                messages=[{"role": "user", "content": prompt}]
            )
        
            return self._parse_extraction_response(message.content[0].text)
    
    async def _extract_with_openai(self, prompt: str, model: str) -> List[ExtractedFact]:
        """Extract facts using OpenAI with connection pooling and circuit breaker protection."""
        if not self.openai_api_key:
            raise ValueError("OpenAI API key not available")
        
        # Use circuit breaker to protect OpenAI API calls
        try:
            return await self.circuit_breaker_manager.call_with_circuit_breaker(
                "openai_api",
                self._openai_api_call,
                prompt,
                model
            )
        except CircuitBreakerError as e:
            logger.warning(f"OpenAI API call blocked by circuit breaker: {e}")
            # Return empty result for graceful degradation
            return []
    
    async def _openai_api_call(self, prompt: str, model: str) -> List[ExtractedFact]:
        """Protected OpenAI API call method"""
        async with self.llm_pool.get_openai_client(self.openai_api_key) as client:
            response = await client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1000,
                response_format={"type": "json_object"}
            )
        
            return self._parse_extraction_response(response.choices[0].message.content)
    
    def _build_extraction_prompt(self, context: ExtractionContext) -> str:
        """Build extraction prompt from context."""
        recent_messages_text = "\n".join(context.recent_messages[-10:])  # Last 10 messages
        user_context = context.user_background or "No background information available"
        
        return self.FACT_EXTRACTION_PROMPT.format(
            latest_message=context.latest_message,
            recent_messages=recent_messages_text,
            user_context=user_context
        )
    
    def _parse_extraction_response(self, response_text: str) -> List[ExtractedFact]:
        """Parse LLM response into structured facts."""
        try:
            # Handle Claude's response format
            if "```json" in response_text:
                json_start = response_text.find("```json") + 7
                json_end = response_text.find("```", json_start)
                response_text = response_text[json_start:json_end].strip()
            
            data = json.loads(response_text)
            facts = []
            
            for fact_data in data.get("facts", []):
                try:
                    fact = ExtractedFact(**fact_data)
                    # Validate confidence and relevance scores
                    if 0.0 <= fact.confidence <= 1.0 and 0.0 <= fact.relevance_score <= 1.0:
                        facts.append(fact)
                    else:
                        logger.warning(f"Invalid scores in fact: {fact_data}")
                except ValidationError as e:
                    logger.warning(f"Invalid fact structure: {fact_data}, error: {e}")
                    
            return facts
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse extraction response: {e}")
            logger.debug(f"Response text: {response_text}")
            return []
    
    def _build_cache_key(self, context: ExtractionContext, model: str) -> str:
        """Build cache key for context and model."""
        content_hash = hash(
            context.latest_message + 
            "".join(context.recent_messages) + 
            (context.user_background or "")
        )
        return f"{model}_{content_hash}"
    
    @lru_cache(maxsize=100)
    def _get_cached_extraction(self, cache_key: str) -> Optional[FactExtractionResponse]:
        """Get cached extraction result."""
        return self._cache.get(cache_key)
    
    def clear_cache(self):
        """Clear extraction cache."""
        self._cache.clear()
        logger.info("Fact extraction cache cleared")
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on extraction service."""
        # Get LLM pool health status
        pool_health = await self.llm_pool.health_check()
        
        health = {
            "service": "fact_extractor",
            "status": "healthy",
            "api_keys": {
                "anthropic": bool(self.anthropic_api_key),
                "openai": bool(self.openai_api_key)
            },
            "cache_size": len(self._cache),
            "connection_pool": pool_health
        }
        
        # Test basic functionality
        try:
            test_context = ExtractionContext(
                latest_message="I love Python programming",
                recent_messages=["Hello", "I'm working on a project"]
            )
            
            if self.anthropic_api_key:
                await self.extract_facts(test_context, "claude-3-5-sonnet-20241022")
                health["test_extraction"] = "passed"
            elif self.openai_api_key:
                await self.extract_facts(test_context, "gpt-4")
                health["test_extraction"] = "passed"
            else:
                health["status"] = "unhealthy"
                health["error"] = "No LLM clients available"
                
        except Exception as e:
            health["status"] = "unhealthy"
            health["test_extraction"] = f"failed: {e}"
            
        return health
    
    async def extract_facts_batch(
        self,
        contexts: List[ExtractionContext],
        model: str = "claude-3-5-sonnet-20241022",
        max_batch_size: int = 10,
        max_retries: int = 3
    ) -> List[FactExtractionResponse]:
        """
        Extract facts from multiple contexts concurrently.
        
        Args:
            contexts: List of extraction contexts to process
            model: Model to use for extraction
            max_batch_size: Maximum number of concurrent extractions
            max_retries: Maximum retry attempts per extraction
            
        Returns:
            List of fact extraction responses in same order as input
        """
        if not contexts:
            return []
        
        # Process in batches to avoid overwhelming the API
        results = []
        for i in range(0, len(contexts), max_batch_size):
            batch = contexts[i:i + max_batch_size]
            batch_results = await self._process_extraction_batch(batch, model, max_retries)
            results.extend(batch_results)
            
        return results
    
    async def _process_extraction_batch(
        self,
        contexts: List[ExtractionContext],
        model: str,
        max_retries: int
    ) -> List[FactExtractionResponse]:
        """Process a batch of extractions concurrently."""
        tasks = []
        for context in contexts:
            task = asyncio.create_task(
                self.extract_facts(context, model, max_retries)
            )
            tasks.append(task)
        
        # Wait for all extractions to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions and create error responses
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Batch extraction failed for context {i}: {result}")
                # Create error response
                error_response = FactExtractionResponse(
                    facts=[],
                    processing_time_ms=0.0,
                    model_used=model
                )
                processed_results.append(error_response)
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def extract_facts_with_rate_limiting(
        self,
        contexts: List[ExtractionContext],
        model: str = "claude-3-5-sonnet-20241022",
        requests_per_minute: int = 60,
        max_retries: int = 3
    ) -> List[FactExtractionResponse]:
        """
        Extract facts with intelligent rate limiting.
        
        Args:
            contexts: List of extraction contexts
            model: Model to use for extraction
            requests_per_minute: Maximum requests per minute
            max_retries: Maximum retry attempts
            
        Returns:
            List of fact extraction responses
        """
        if not contexts:
            return []
        
        # Calculate delay between requests
        delay_between_requests = 60.0 / requests_per_minute
        
        results = []
        semaphore = asyncio.Semaphore(min(10, requests_per_minute // 6))  # Limit concurrent requests
        
        async def extract_with_semaphore(context: ExtractionContext) -> FactExtractionResponse:
            async with semaphore:
                try:
                    result = await self.extract_facts(context, model, max_retries)
                    await asyncio.sleep(delay_between_requests)
                    return result
                except Exception as e:
                    logger.error(f"Rate-limited extraction failed: {e}")
                    return FactExtractionResponse(
                        facts=[],
                        processing_time_ms=0.0,
                        model_used=model
                    )
        
        # Create tasks for all extractions
        tasks = [extract_with_semaphore(context) for context in contexts]
        
        # Process with progress tracking
        results = await asyncio.gather(*tasks)
        
        return results
    
    def get_batch_metrics(self) -> Dict[str, Any]:
        """Get metrics about batch processing performance."""
        return {
            "cache_size": len(self._cache),
            "cache_hit_rate": self._calculate_cache_hit_rate(),
            "avg_processing_time": self._calculate_avg_processing_time(),
            "supported_batch_sizes": {
                "max_concurrent": 50,
                "recommended_batch_size": 10,
                "rate_limited_rpm": 60
            }
        }
    
    def _calculate_cache_hit_rate(self) -> float:
        """Calculate cache hit rate (placeholder for actual implementation)."""
        # This would be implemented with proper metrics tracking
        return 0.0
    
    def _calculate_avg_processing_time(self) -> float:
        """Calculate average processing time (placeholder for actual implementation)."""
        # This would be implemented with proper metrics tracking
        return 0.0