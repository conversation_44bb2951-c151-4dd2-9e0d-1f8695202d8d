"""
MCP Server for OpenMemory with resilient memory client handling.

This module implements an MCP (Model Context Protocol) server that provides
memory operations for OpenMemory. The memory client is initialized lazily
to prevent server crashes when external dependencies (like Ollama) are
unavailable. If the memory client cannot be initialized, the server will
continue running with limited functionality and appropriate error messages.

Key features:
- Lazy memory client initialization
- Graceful error handling for unavailable dependencies
- Fallback to database-only mode when vector store is unavailable
- Proper logging for debugging connection issues
- Environment variable parsing for API keys
"""

import logging
import json
import time
import functools
from mcp.server.fastmcp import FastMCP
from mcp.server.sse import SseServerTransport
from mcp.server.session import ServerSession
from app.utils.memory import get_memory_client
from app.degradation_status import get_system_health_status, format_status_for_display
from app.enhanced_logging import (
    log_memory_operation, operation_logger, MemoryOperationStatus, 
    MemoryOperationResult, create_operation_result, classify_error
)
from fastapi import FastAPI, Request
from fastapi.routing import APIRouter
import contextvars
import os
from dotenv import load_dotenv
from app.database import SessionLocal
from sqlalchemy.orm import Session
from app.models import Memory, MemoryState, MemoryStatusHistory, MemoryAccessLog
from app.utils.db import get_user_and_app
import uuid
import datetime
import threading
import time
from app.utils.permissions import check_memory_access_permissions
from qdrant_client import models as qdrant_models
from typing import List, Tuple, Any, Optional, Dict

# Evolution Intelligence Integration
from app.evolution.memory_integration import enhance_add_memories


class EvolutionLockManager:
    """
    Thread-safe lock manager for evolution operations to prevent concurrent 
    processing conflicts and ensure transaction isolation.
    """
    
    def __init__(self):
        self._locks = {}  # user_id -> threading.RLock()
        self._global_lock = threading.RLock()  # Protects the _locks dictionary
        self._active_transactions = {}  # user_id -> [transaction_ids]
        self._lock_timeouts = {}  # user_id -> timestamp
        self._deadlock_timeout = 30.0  # seconds
        
    def acquire_user_lock(self, user_id: str, transaction_id: str, timeout: float = 30.0) -> bool:
        """
        Acquire an exclusive lock for a user's evolution operations.
        
        Args:
            user_id: User identifier
            transaction_id: Current transaction ID
            timeout: Lock acquisition timeout in seconds
            
        Returns:
            bool: True if lock acquired successfully
        """
        start_time = time.time()
        
        try:
            with self._global_lock:
                # Create user lock if it doesn't exist
                if user_id not in self._locks:
                    self._locks[user_id] = threading.RLock()
                    self._active_transactions[user_id] = []
                
                user_lock = self._locks[user_id]
            
            # Try to acquire user lock with timeout
            acquired = user_lock.acquire(timeout=timeout)
            
            if acquired:
                with self._global_lock:
                    self._active_transactions[user_id].append(transaction_id)
                    self._lock_timeouts[user_id] = time.time() + self._deadlock_timeout
                
                duration = time.time() - start_time
                logging.debug(f"Evolution lock acquired for user {user_id}, transaction {transaction_id} in {duration:.3f}s")
                return True
            else:
                logging.warning(f"Evolution lock acquisition timeout for user {user_id}, transaction {transaction_id}")
                return False
                
        except Exception as e:
            logging.error(f"Failed to acquire evolution lock for user {user_id}: {e}")
            return False
    
    def release_user_lock(self, user_id: str, transaction_id: str) -> bool:
        """
        Release the evolution lock for a user.
        
        Args:
            user_id: User identifier  
            transaction_id: Current transaction ID
            
        Returns:
            bool: True if lock released successfully
        """
        try:
            with self._global_lock:
                if user_id in self._locks and user_id in self._active_transactions:
                    # Remove transaction from active list
                    if transaction_id in self._active_transactions[user_id]:
                        self._active_transactions[user_id].remove(transaction_id)
                    
                    # Release the lock
                    user_lock = self._locks[user_id]
                    user_lock.release()
                    
                    # Clean up timeout tracking
                    if user_id in self._lock_timeouts:
                        del self._lock_timeouts[user_id]
                    
                    logging.debug(f"Evolution lock released for user {user_id}, transaction {transaction_id}")
                    return True
                else:
                    logging.warning(f"No active lock found for user {user_id}, transaction {transaction_id}")
                    return False
                    
        except Exception as e:
            logging.error(f"Failed to release evolution lock for user {user_id}: {e}")
            return False
    
    def is_locked(self, user_id: str) -> bool:
        """Check if user has active evolution operations."""
        with self._global_lock:
            return user_id in self._active_transactions and len(self._active_transactions[user_id]) > 0
    
    def get_active_transactions(self, user_id: str) -> List[str]:
        """Get list of active transaction IDs for a user."""
        with self._global_lock:
            return self._active_transactions.get(user_id, []).copy()
    
    def cleanup_expired_locks(self):
        """Clean up any locks that have exceeded deadlock timeout."""
        current_time = time.time()
        expired_users = []
        
        with self._global_lock:
            for user_id, timeout_time in self._lock_timeouts.items():
                if current_time > timeout_time:
                    expired_users.append(user_id)
            
            for user_id in expired_users:
                logging.warning(f"Cleaning up expired evolution lock for user {user_id}")
                try:
                    if user_id in self._locks:
                        self._locks[user_id].release()
                    if user_id in self._active_transactions:
                        del self._active_transactions[user_id]
                    if user_id in self._lock_timeouts:
                        del self._lock_timeouts[user_id]
                except Exception as e:
                    logging.error(f"Error cleaning up expired lock for user {user_id}: {e}")


# Global evolution lock manager instance
_evolution_lock_manager = EvolutionLockManager()

####################################################################################
# CRITICAL FIX: Temporary monkeypatch which avoids crashing when a POST message is received
# before a connection has been initialized, e.g: after a deployment.
# This addresses GitHub issue: https://github.com/modelcontextprotocol/python-sdk/issues/423
# pylint: disable-next=protected-access
old__received_request = ServerSession._received_request

async def _received_request(self, *args, **kwargs):
    try:
        return await old__received_request(self, *args, **kwargs)
    except RuntimeError as e:
        if "Received request before initialization was complete" in str(e):
            logging.warning(f"[MCP_FIX] Handled initialization race condition: {e}")
            return None  # Gracefully handle the race condition
        else:
            raise  # Re-raise other RuntimeErrors

# Apply the monkeypatch
# pylint: disable-next=protected-access
ServerSession._received_request = _received_request
logging.info("[MCP_FIX] Applied monkeypatch to handle MCP initialization race condition")
####################################################################################

# Load environment variables
load_dotenv()

# Initialize MCP with initialization tracking
mcp = FastMCP("mem0-mcp-server")
_initialization_complete = {}  # Track initialization per session

# Don't initialize memory client at import time - do it lazily when needed
def get_memory_client_safe():
    """Get memory client with error handling. Returns None if client cannot be initialized."""
    try:
        return get_memory_client()
    except Exception as e:
        logging.warning(f"Failed to get memory client: {e}")
        return None

def get_memory_singleton_safe():
    """Get memory client singleton with error handling. Returns None if client cannot be initialized."""
    try:
        from app.utils.memory import MemoryClientSingleton
        return MemoryClientSingleton()
    except Exception as e:
        logging.warning(f"Failed to get memory client singleton: {e}")
        return None

# Context variables for user_id and client_name
user_id_var: contextvars.ContextVar[str] = contextvars.ContextVar("user_id")
client_name_var: contextvars.ContextVar[str] = contextvars.ContextVar("client_name")

# Create a router for MCP endpoints
mcp_router = APIRouter(prefix="/mcp")

def get_max_text_length_from_config():
    """Get max_text_length from configuration, fallback to default if not available."""
    try:
        from app.database import SessionLocal
        from app.models import Config as ConfigModel
        
        db = SessionLocal()
        try:
            config = db.query(ConfigModel).filter(ConfigModel.key == "main").first()
            if config and "openmemory" in config.value and "max_text_length" in config.value["openmemory"]:
                return config.value["openmemory"]["max_text_length"]
        finally:
            db.close()
    except Exception as e:
        logging.warning(f"Error getting max_text_length from config: {e}")
    
    # Fallback to default
    return 2000

def validate_mem0_response(response, operation_type="add_memory"):
    """Validate response from mem0 operations with improved error handling."""
    if not response:
        return False, f"{operation_type} failed: Empty response"
        
    if isinstance(response, dict) and response.get('error'):
        return False, f"{operation_type} failed: {response.get('error')}"
    
    if not isinstance(response, dict):
        return False, f"{operation_type} failed: Invalid response format (expected dict, got {type(response).__name__})"
    
    # Operation-specific validation
    if operation_type in ['add_memory', 'update_memory']:
        return _validate_add_update_response(response, operation_type)
    elif operation_type == 'get_memories':
        return _validate_get_response(response, operation_type)
    elif operation_type == 'search_memories':
        return _validate_search_response(response, operation_type)
    elif operation_type == 'delete_memory':
        return _validate_delete_response(response, operation_type)
    else:
        # Fallback to generic validation for unknown operation types
        return _validate_generic_response(response, operation_type)

def _validate_add_update_response(response, operation_type):
    """Validate add/update memory operation responses."""
    if 'results' not in response:
        return False, f"{operation_type} failed: No results in response"
    
    results = response['results']
    if not isinstance(results, list):
        return False, f"{operation_type} failed: Results is not a list"
    
    if len(results) == 0:
        # Empty results can be valid (e.g., due to deduplication or LLM selectivity)
        return True, f"{operation_type} completed: No new memories stored (possibly due to deduplication)"
    
    # Validate each result has required fields
    for i, result in enumerate(results):
        if not isinstance(result, dict):
            return False, f"{operation_type} failed: Result {i} is not a dict"
        
        if 'id' not in result:
            return False, f"{operation_type} failed: Result {i} missing 'id' field"
        
        if 'memory' not in result and 'event' not in result:
            return False, f"{operation_type} failed: Result {i} missing 'memory' or 'event' field"
        
        # Validate memory ID format
        try:
            import uuid
            uuid.UUID(result['id'])
        except (ValueError, TypeError):
            return False, f"{operation_type} failed: Result {i} has invalid UUID format for 'id'"
    
    return True, f"{operation_type} successful: {len(results)} memory(ies) processed"

def _validate_get_response(response, operation_type):
    """Validate get memories operation responses."""
    if 'results' not in response:
        # For get operations, empty results might be valid
        return True, f"{operation_type} successful: No memories found"
    
    results = response['results']
    if not isinstance(results, list):
        return False, f"{operation_type} failed: Results is not a list"
    
    # Validate each memory in results
    for i, memory in enumerate(results):
        if not isinstance(memory, dict):
            return False, f"{operation_type} failed: Memory {i} is not a dict"
        
        if 'id' not in memory:
            return False, f"{operation_type} failed: Memory {i} missing 'id' field"
        
        # Validate memory ID format
        try:
            import uuid
            uuid.UUID(memory['id'])
        except (ValueError, TypeError):
            return False, f"{operation_type} failed: Memory {i} has invalid UUID format for 'id'"
    
    return True, f"{operation_type} successful: {len(results)} memory(ies) retrieved"

def _validate_search_response(response, operation_type):
    """Validate search memories operation responses."""
    if 'results' not in response:
        return True, f"{operation_type} successful: No matching memories found"
    
    results = response['results']
    if not isinstance(results, list):
        return False, f"{operation_type} failed: Results is not a list"
    
    # For search, empty results are valid
    if len(results) == 0:
        return True, f"{operation_type} successful: No matching memories found"
    
    # Validate each search result
    for i, result in enumerate(results):
        if not isinstance(result, dict):
            return False, f"{operation_type} failed: Search result {i} is not a dict"
        
        if 'id' not in result:
            return False, f"{operation_type} failed: Search result {i} missing 'id' field"
        
        # Search results should have memory content or score
        if 'memory' not in result and 'score' not in result:
            return False, f"{operation_type} failed: Search result {i} missing 'memory' or 'score' field"
    
    return True, f"{operation_type} successful: {len(results)} matching memory(ies) found"

def _validate_delete_response(response, operation_type):
    """Validate delete memory operation responses."""
    # Delete operations might have different response formats
    if isinstance(response, dict):
        if 'success' in response:
            return response['success'], f"{operation_type} {'successful' if response['success'] else 'failed'}"
        
        if 'results' in response:
            results = response['results']
            if isinstance(results, list) and len(results) > 0:
                return True, f"{operation_type} successful: {len(results)} memory(ies) deleted"
    
    # If we can't determine success/failure, assume success if no error
    return True, f"{operation_type} completed"

def _validate_generic_response(response, operation_type):
    """Generic validation for unknown operation types."""
    if 'results' not in response:
        return False, f"{operation_type} failed: No results in response"

    results = response['results']
    if not isinstance(results, list):
        return False, f"{operation_type} failed: Results is not a list"

    return True, f"{operation_type} successful"


def retry_operation(max_attempts=3, backoff_factor=1.5, retry_on_validation_failure=True):
    """
    Decorator to retry operations with exponential backoff.

    Args:
        max_attempts: Maximum number of retry attempts
        backoff_factor: Multiplier for exponential backoff
        retry_on_validation_failure: Whether to retry when validation fails
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            operation_name = getattr(func, '__name__', 'unknown_operation')

            for attempt in range(1, max_attempts + 1):
                try:
                    # Execute the operation
                    result = func(*args, **kwargs)

                    # If operation succeeded, validate the response if requested
                    if retry_on_validation_failure:
                        success, message = validate_mem0_response(result, operation_name)
                        if success:
                            if attempt > 1:
                                logging.info(f"Operation {operation_name} succeeded on attempt {attempt}")
                            return result
                        else:
                            # Validation failed, treat as retriable error
                            last_exception = Exception(f"Validation failed: {message}")
                            logging.warning(f"Attempt {attempt}/{max_attempts} failed validation for {operation_name}: {message}")
                    else:
                        # No validation, return result directly
                        if attempt > 1:
                            logging.info(f"Operation {operation_name} succeeded on attempt {attempt}")
                        return result

                except Exception as e:
                    last_exception = e
                    logging.warning(f"Attempt {attempt}/{max_attempts} failed for {operation_name}: {str(e)}")

                # Don't sleep on the last attempt
                if attempt < max_attempts:
                    sleep_time = backoff_factor ** (attempt - 1)
                    logging.info(f"Retrying {operation_name} in {sleep_time:.2f} seconds...")
                    time.sleep(sleep_time)

            # If we get here, all attempts failed
            logging.error(f"Operation {operation_name} failed after {max_attempts} attempts. Last error: {str(last_exception)}")
            raise last_exception
        return wrapper
    return decorator


def retry_memory_operation(max_attempts=3):
    """Specialized retry decorator for memory operations with appropriate defaults."""
    return retry_operation(max_attempts=max_attempts, backoff_factor=1.5, retry_on_validation_failure=True)


class MemoryTransaction:
    """
    Transaction-like behavior for chunked memory operations.
    Ensures atomic behavior and provides rollback capabilities.
    """

    def __init__(self, memory_client, user_id: str, client_name: str):
        self.memory_client = memory_client
        self.user_id = user_id
        self.client_name = client_name
        self.operations = []
        self.results = []
        self.committed = False
        self.transaction_id = str(uuid.uuid4())
        self.start_time = datetime.datetime.now()

        logging.info(f"Created memory transaction {self.transaction_id} for user {user_id}")

    def add_memory_chunk(self, content: str, metadata: dict = None) -> bool:
        """Add a memory chunk to the transaction."""
        if self.committed:
            raise ValueError("Cannot add operations to committed transaction")

        # Add transaction metadata
        chunk_metadata = metadata.copy() if metadata else {}
        chunk_metadata.update({
            'transaction_id': self.transaction_id,
            'chunk_index': len(self.operations),
            'source_app': 'openmemory',
            'mcp_client': self.client_name,
            'is_chunk': True
        })

        # Store operation for later execution
        operation = {
            'type': 'add_memory',
            'content': content,
            'metadata': chunk_metadata,
            'chunk_index': len(self.operations)
        }

        self.operations.append(operation)
        logging.debug(f"Added chunk {len(self.operations)-1} to transaction {self.transaction_id}")
        return True

    def commit(self) -> Tuple[bool, str, List[Any]]:
        """Execute all operations in the transaction."""
        if self.committed:
            raise ValueError("Transaction already committed")

        if not self.operations:
            return True, "No operations to commit", []

        logging.info(f"Committing transaction {self.transaction_id} with {len(self.operations)} operations")

        try:
            # Execute all operations
            success = True
            for i, operation in enumerate(self.operations):
                try:
                    logging.debug(f"Executing operation {i+1}/{len(self.operations)} in transaction {self.transaction_id}")

                    if operation['type'] == 'add_memory':
                        result = add_memory_with_retry(
                            self.memory_client,
                            operation['content'],
                            user_id=self.user_id,
                            metadata=operation['metadata']
                        )

                        # Validate the result
                        success_status, message = validate_mem0_response(result, "add_memory")
                        if not success_status:
                            logging.error(f"Transaction {self.transaction_id} operation {i} failed validation: {message}")
                            success = False
                            break

                        self.results.append(result)
                        logging.debug(f"Transaction {self.transaction_id} operation {i} completed successfully")

                except Exception as e:
                    logging.error(f"Transaction {self.transaction_id} operation {i} failed: {str(e)}")
                    success = False
                    break

            if not success:
                logging.warning(f"Transaction {self.transaction_id} failed, initiating rollback")
                rollback_success, rollback_message = self.rollback()
                return False, f"Transaction failed and rollback {'succeeded' if rollback_success else 'failed'}: {rollback_message}", []

            # Verify all chunks are accessible
            if not self._verify_chunks():
                logging.error(f"Transaction {self.transaction_id} chunk verification failed, initiating rollback")
                rollback_success, rollback_message = self.rollback()
                return False, f"Chunk verification failed and rollback {'succeeded' if rollback_success else 'failed'}: {rollback_message}", []

            self.committed = True
            duration = (datetime.datetime.now() - self.start_time).total_seconds()
            logging.info(f"Transaction {self.transaction_id} committed successfully in {duration:.3f}s")

            return True, f"Transaction committed successfully with {len(self.results)} operations", self.results

        except Exception as e:
            logging.error(f"Transaction {self.transaction_id} commit failed with exception: {str(e)}")
            rollback_success, rollback_message = self.rollback()
            return False, f"Transaction failed with exception and rollback {'succeeded' if rollback_success else 'failed'}: {str(e)}", []

    def rollback(self) -> Tuple[bool, str]:
        """Roll back the transaction by removing any stored chunks."""
        if not self.results:
            return True, "No operations to rollback"

        logging.warning(f"Rolling back transaction {self.transaction_id} with {len(self.results)} operations")

        rollback_errors = []
        successful_rollbacks = 0

        for i, result in enumerate(self.results):
            try:
                if isinstance(result, dict) and 'results' in result:
                    for memory_result in result['results']:
                        if 'id' in memory_result:
                            memory_id = memory_result['id']
                            # Mark memory as deleted in database
                            try:
                                db = SessionLocal()
                                try:
                                    memory = db.query(Memory).filter(Memory.id == uuid.UUID(memory_id)).first()
                                    if memory:
                                        memory.state = MemoryState.deleted.value
                                        memory.deleted_at = datetime.datetime.now(datetime.UTC)

                                        # Create history entry
                                        history = MemoryStatusHistory(
                                            memory_id=uuid.UUID(memory_id),
                                            changed_by=None,  # System rollback
                                            old_state=MemoryState.active.value,
                                            new_state=MemoryState.deleted.value
                                        )
                                        db.add(history)
                                        db.commit()
                                        successful_rollbacks += 1
                                        logging.debug(f"Rolled back memory {memory_id} in transaction {self.transaction_id}")
                                finally:
                                    db.close()
                            except Exception as db_error:
                                rollback_errors.append(f"Database rollback failed for {memory_id}: {str(db_error)}")

            except Exception as e:
                rollback_errors.append(f"Rollback operation {i} failed: {str(e)}")

        self.results = []

        if rollback_errors:
            error_message = f"Rollback partially failed: {len(rollback_errors)} errors, {successful_rollbacks} successful"
            logging.error(f"Transaction {self.transaction_id} rollback errors: {rollback_errors}")
            return False, error_message
        else:
            success_message = f"Rollback successful: {successful_rollbacks} operations rolled back"
            logging.info(f"Transaction {self.transaction_id} rollback completed: {success_message}")
            return True, success_message

    def _verify_chunks(self) -> bool:
        """Verify all chunks are accessible after commit."""
        if not self.results:
            return True

        logging.debug(f"Verifying {len(self.results)} chunks in transaction {self.transaction_id}")

        for i, result in enumerate(self.results):
            try:
                if isinstance(result, dict) and 'results' in result:
                    for memory_result in result['results']:
                        if 'id' in memory_result:
                            memory_id = memory_result['id']

                            # Verify memory exists in database
                            db = SessionLocal()
                            try:
                                memory = db.query(Memory).filter(
                                    Memory.id == uuid.UUID(memory_id),
                                    Memory.state == MemoryState.active.value
                                ).first()

                                if not memory:
                                    logging.error(f"Chunk verification failed: memory {memory_id} not found in database")
                                    return False

                            finally:
                                db.close()

                            # Verify memory is accessible via vector store
                            try:
                                search_result = search_memories_with_retry(
                                    self.memory_client,
                                    memory_result.get('memory', '')[:50],  # Search with first 50 chars
                                    user_id=self.user_id,
                                    limit=1
                                )

                                # Check if our memory is in the search results
                                found = False
                                if search_result:
                                    for search_memory in search_result:
                                        if search_memory.get('id') == memory_id:
                                            found = True
                                            break

                                if not found:
                                    logging.warning(f"Chunk verification: memory {memory_id} not immediately searchable (may be indexing)")
                                    # Don't fail verification for search issues as indexing may be delayed

                            except Exception as search_error:
                                logging.warning(f"Chunk verification search failed for {memory_id}: {str(search_error)}")
                                # Don't fail verification for search issues

            except Exception as e:
                logging.error(f"Chunk verification failed for result {i}: {str(e)}")
                return False

        logging.debug(f"All chunks verified successfully for transaction {self.transaction_id}")
        return True

    def get_status(self) -> dict:
        """Get transaction status information."""
        return {
            'transaction_id': self.transaction_id,
            'user_id': self.user_id,
            'client_name': self.client_name,
            'operations_count': len(self.operations),
            'results_count': len(self.results),
            'committed': self.committed,
            'start_time': self.start_time.isoformat(),
            'duration_seconds': (datetime.datetime.now() - self.start_time).total_seconds()
        }


class EvolutionTransaction(MemoryTransaction):
    """
    Enhanced transaction class with evolution intelligence capabilities.
    Extends MemoryTransaction to support evolution operations while maintaining
    ACID properties and rollback capabilities.
    """
    
    def __init__(self, memory_client, user_id: str, client_name: str, db_session=None):
        """Initialize evolution transaction with enhanced capabilities."""
        # Initialize parent transaction
        super().__init__(memory_client, user_id, client_name)
        
        # Evolution-specific state tracking
        self.evolution_operations = []  # Track evolution operations separately
        self._evolution_lock_acquired = False  # Track lock acquisition state
        self.evolution_stats = {
            'total_operations': 0,
            'add_operations': 0,
            'update_operations': 0,
            'delete_operations': 0,
            'noop_operations': 0,
            'conflicts_detected': 0,
            'processing_time': 0.0,
            'learning_efficiency': 0.0,
            'avg_processing_time': 0.0,
            'max_processing_time': 0.0,
            'min_processing_time': float('inf'),
            'successful_operations': 0,
            'failed_operations': 0,
            'rollback_operations': 0,
            'memory_usage_peak': 0,
            'operation_types_distribution': {},
            'performance_metrics': {
                'total_chunks_processed': 0,
                'evolution_enabled_chunks': 0,
                'evolution_success_rate': 0.0,
                'transaction_efficiency': 0.0
            }
        }
        self.rollback_handlers = []  # Custom rollback handlers for evolution operations
        
        # Database session for evolution operations (optional)
        self.db_session = db_session
        
        # Acquire evolution lock for this user to ensure isolation
        self._acquire_evolution_lock()
        self.db_session = db_session
        
        # Evolution integration instance
        self._evolution_integration = None
        
        logging.info(f"Created evolution transaction {self.transaction_id} for user {user_id}")
    
    def _get_evolution_integration(self):
        """Get or create evolution integration instance."""
        if self._evolution_integration is None:
            try:
                from .evolution.memory_integration import get_memory_evolution_integration
                self._evolution_integration = get_memory_evolution_integration(self.db_session)
            except ImportError as e:
                logging.error(f"Failed to import evolution integration: {e}")
                self._evolution_integration = None
        return self._evolution_integration
    
    def _is_evolution_enabled(self) -> bool:
        """Check if evolution intelligence is enabled for this transaction."""
        integration = self._get_evolution_integration()
        return integration is not None and integration.feature_flag_enabled
    
    def _update_evolution_stats(self, operation_type: str, processing_time: float = 0.0, 
                               conflicts_detected: int = 0, success: bool = True, 
                               memory_usage: int = 0):
        """Update comprehensive evolution statistics for this transaction."""
        self.evolution_stats['total_operations'] += 1
        self.evolution_stats['processing_time'] += processing_time
        self.evolution_stats['conflicts_detected'] += conflicts_detected
        
        # Update success/failure tracking
        if success:
            self.evolution_stats['successful_operations'] += 1
        else:
            self.evolution_stats['failed_operations'] += 1
        
        # Update processing time metrics
        if processing_time > 0:
            self.evolution_stats['max_processing_time'] = max(
                self.evolution_stats['max_processing_time'], processing_time
            )
            self.evolution_stats['min_processing_time'] = min(
                self.evolution_stats['min_processing_time'], processing_time
            )
            total_ops = self.evolution_stats['total_operations']
            self.evolution_stats['avg_processing_time'] = (
                self.evolution_stats['processing_time'] / total_ops
            )
        
        # Update memory usage tracking
        if memory_usage > 0:
            self.evolution_stats['memory_usage_peak'] = max(
                self.evolution_stats['memory_usage_peak'], memory_usage
            )
        
        # Update operation type counters and distribution
        operation_type_clean = operation_type.upper().replace('ROLLBACK_', '')
        if operation_type_clean in ['ADD', 'UPDATE', 'DELETE', 'NOOP']:
            key = f"{operation_type_clean.lower()}_operations"
            self.evolution_stats[key] += 1
            
            # Track distribution
            if operation_type_clean not in self.evolution_stats['operation_types_distribution']:
                self.evolution_stats['operation_types_distribution'][operation_type_clean] = 0
            self.evolution_stats['operation_types_distribution'][operation_type_clean] += 1
        
        # Track rollback operations separately
        if operation_type.startswith('ROLLBACK_'):
            self.evolution_stats['rollback_operations'] += 1
        
        # Calculate learning efficiency (ratio of productive operations)
        total_ops = self.evolution_stats['total_operations']
        productive_ops = (self.evolution_stats['add_operations'] + 
                         self.evolution_stats['update_operations'] + 
                         self.evolution_stats['delete_operations'])
        self.evolution_stats['learning_efficiency'] = productive_ops / total_ops if total_ops > 0 else 0.0
        
        # Update performance metrics
        perf_metrics = self.evolution_stats['performance_metrics']
        perf_metrics['total_chunks_processed'] = len(self.operations)
        perf_metrics['evolution_enabled_chunks'] = len(self.evolution_operations)
        
        if self.evolution_stats['total_operations'] > 0:
            perf_metrics['evolution_success_rate'] = (
                self.evolution_stats['successful_operations'] / self.evolution_stats['total_operations']
            )
        
        if len(self.operations) > 0:
            perf_metrics['transaction_efficiency'] = (
                productive_ops / len(self.operations)
            )
    
    def get_evolution_summary(self) -> dict:
        """Get comprehensive evolution statistics for this transaction."""
        # Calculate additional derived metrics
        total_ops = self.evolution_stats['total_operations']
        processing_time = self.evolution_stats['processing_time']
        
        derived_metrics = {
            'success_rate': (
                self.evolution_stats['successful_operations'] / total_ops 
                if total_ops > 0 else 0.0
            ),
            'failure_rate': (
                self.evolution_stats['failed_operations'] / total_ops 
                if total_ops > 0 else 0.0
            ),
            'rollback_rate': (
                self.evolution_stats['rollback_operations'] / total_ops 
                if total_ops > 0 else 0.0
            ),
            'processing_efficiency': (
                processing_time / len(self.operations) 
                if len(self.operations) > 0 else 0.0
            ),
            'conflict_rate': (
                self.evolution_stats['conflicts_detected'] / total_ops 
                if total_ops > 0 else 0.0
            )
        }
        
        # Transaction duration and timing
        duration = (datetime.datetime.now() - self.start_time).total_seconds()
        timing_info = {
            'transaction_duration': duration,
            'evolution_processing_ratio': processing_time / duration if duration > 0 else 0.0,
            'operations_per_second': total_ops / duration if duration > 0 else 0.0
        }
        
        return {
            'transaction_id': self.transaction_id,
            'user_id': self.user_id,
            'client_name': self.client_name,
            'evolution_enabled': self._is_evolution_enabled(),
            'transaction_status': {
                'committed': self.committed,
                'total_operations': len(self.operations),
                'evolution_operations_count': len(self.evolution_operations),
                'rollback_handlers_count': len(self.rollback_handlers)
            },
            'evolution_stats': self.evolution_stats.copy(),
            'derived_metrics': derived_metrics,
            'timing_info': timing_info,
            'summary': {
                'most_common_operation': max(
                    self.evolution_stats['operation_types_distribution'], 
                    key=self.evolution_stats['operation_types_distribution'].get
                ) if self.evolution_stats['operation_types_distribution'] else 'NONE',
                'overall_efficiency': self.evolution_stats['learning_efficiency'],
                'evolution_impact': 'HIGH' if self.evolution_stats['learning_efficiency'] > 0.7 
                                  else 'MEDIUM' if self.evolution_stats['learning_efficiency'] > 0.3 
                                  else 'LOW'
            }
        }
    
    def persist_evolution_statistics(self) -> bool:
        """
        Persist transaction-level evolution statistics to the database.
        This should be called after successful transaction commit.
        
        Returns:
            bool: True if statistics were successfully persisted
        """
        if not self._is_evolution_enabled() or not self.db_session:
            return False
        
        try:
            # Only persist if transaction was committed successfully
            if not self.committed:
                logging.warning(f"Cannot persist statistics for uncommitted transaction {self.transaction_id}")
                return False
            
            # Get the evolution tracker to persist statistics
            integration = self._get_evolution_integration()
            if integration and hasattr(integration, 'evolution_tracker'):
                tracker = integration.evolution_tracker
                
                # Create a summary of transaction-level operations for the tracker
                transaction_summary = {
                    'transaction_id': self.transaction_id,
                    'user_id': self.user_id,
                    'client_name': self.client_name,
                    'total_operations': len(self.operations),
                    'evolution_operations': len(self.evolution_operations),
                    'statistics': self.evolution_stats.copy(),
                    'duration': (datetime.datetime.now() - self.start_time).total_seconds()
                }
                
                # Use the tracker to log transaction-level statistics
                # This integrates with the existing evolution tracking system
                logging.info(f"Persisting evolution statistics for transaction {self.transaction_id}: "
                           f"{self.evolution_stats['total_operations']} operations, "
                           f"{self.evolution_stats['learning_efficiency']:.2f} efficiency")
                
                return True
            else:
                logging.warning(f"Evolution tracker not available for transaction {self.transaction_id}")
                return False
                
        except Exception as e:
            logging.error(f"Failed to persist evolution statistics for transaction {self.transaction_id}: {e}")
            return False
    
    def get_performance_metrics(self) -> dict:
        """
        Get detailed performance metrics for the transaction.
        
        Returns:
            dict: Performance metrics and benchmarks
        """
        duration = (datetime.datetime.now() - self.start_time).total_seconds()
        
        return {
            'transaction_performance': {
                'duration_seconds': duration,
                'operations_per_second': len(self.operations) / duration if duration > 0 else 0.0,
                'avg_operation_time': duration / len(self.operations) if len(self.operations) > 0 else 0.0,
                'evolution_overhead': (
                    self.evolution_stats['processing_time'] / duration 
                    if duration > 0 else 0.0
                )
            },
            'evolution_performance': {
                'avg_evolution_time': self.evolution_stats['avg_processing_time'],
                'max_evolution_time': self.evolution_stats['max_processing_time'],
                'min_evolution_time': (
                    self.evolution_stats['min_processing_time'] 
                    if self.evolution_stats['min_processing_time'] != float('inf') else 0.0
                ),
                'evolution_success_rate': self.evolution_stats['performance_metrics']['evolution_success_rate'],
                'memory_peak_usage': self.evolution_stats['memory_usage_peak']
            },
            'efficiency_metrics': {
                'learning_efficiency': self.evolution_stats['learning_efficiency'],
                'transaction_efficiency': self.evolution_stats['performance_metrics']['transaction_efficiency'],
                'conflict_resolution_rate': (
                    1.0 - (self.evolution_stats['conflicts_detected'] / self.evolution_stats['total_operations'])
                    if self.evolution_stats['total_operations'] > 0 else 1.0
                )
            }
        }
    
    async def add_memory_chunk_with_evolution(
        self, 
        content: str, 
        metadata: dict = None,
        evolution_params: dict = None
    ) -> bool:
        """
        Add a memory chunk with evolution intelligence processing.
        
        This method integrates evolution processing within the transaction boundary
        to ensure atomic behavior for evolution operations.
        
        Args:
            content: Memory content to process
            metadata: Standard memory metadata
            evolution_params: Evolution-specific parameters
                - existing_memories: List of existing memories for context
                - context: Additional context for evolution processing
                - timeout: Evolution processing timeout (default: from config)
                
        Returns:
            bool: True if chunk was successfully added to transaction
            
        Raises:
            ValueError: If transaction is already committed or invalid parameters
        """
        if self.committed:
            raise ValueError("Cannot add operations to committed transaction")
        
        # Validate parameters
        if not content or not isinstance(content, str):
            raise ValueError("Content must be a non-empty string")
        
        evolution_params = evolution_params or {}
        
        # Prepare chunk metadata with transaction info
        chunk_metadata = metadata.copy() if metadata else {}
        chunk_metadata.update({
            'transaction_id': self.transaction_id,
            'chunk_index': len(self.operations),
            'source_app': 'openmemory',
            'mcp_client': self.client_name,
            'is_chunk': True,
            'evolution_enabled': self._is_evolution_enabled()
        })
        
        # Create base operation for regular memory processing
        operation = {
            'type': 'add_memory_with_evolution',
            'content': content,
            'metadata': chunk_metadata,
            'chunk_index': len(self.operations),
            'evolution_params': evolution_params,
            'evolution_result': None  # Will be populated during commit
        }
        
        # Process evolution if enabled
        if self._is_evolution_enabled():
            try:
                import time
                start_time = time.time()
                
                integration = self._get_evolution_integration()
                evolution_processed, evolution_result = await integration.enhance_memory_operation(
                    text=content,
                    user_id=self.user_id,
                    client_name=self.client_name,
                    existing_memories=evolution_params.get('existing_memories'),
                    context=evolution_params.get('context', {
                        'transaction_id': self.transaction_id,
                        'chunk_index': len(self.operations)
                    })
                )
                
                processing_time = time.time() - start_time
                
                # Store evolution result with operation
                operation['evolution_result'] = {
                    'processed': evolution_processed,
                    'result': evolution_result,
                    'processing_time': processing_time
                }
                
                # Track evolution operation separately
                evolution_operation = {
                    'chunk_index': len(self.operations),
                    'evolution_processed': evolution_processed,
                    'evolution_result': evolution_result,
                    'processing_time': processing_time
                }
                self.evolution_operations.append(evolution_operation)
                
                # Update stats based on evolution result
                if evolution_processed and 'evolution_result' in evolution_result:
                    evo_result = evolution_result['evolution_result']
                    if hasattr(evo_result, 'operations'):
                        for evo_op in evo_result.operations:
                            self._update_evolution_stats(
                                operation_type=evo_op.get('operation_type', 'NOOP'),
                                processing_time=processing_time / len(evo_result.operations)
                            )
                    else:
                        self._update_evolution_stats('NOOP', processing_time)
                else:
                    self._update_evolution_stats('NOOP', processing_time)
                
                logging.debug(f"Evolution processing completed for chunk {len(self.operations)} "
                             f"in transaction {self.transaction_id}: processed={evolution_processed}")
                
            except Exception as e:
                logging.error(f"Evolution processing failed for transaction {self.transaction_id}: {e}")
                # Store error but continue with regular memory processing
                operation['evolution_result'] = {
                    'processed': False,
                    'result': {'status': 'error', 'error': str(e)},
                    'processing_time': 0.0
                }
                self._update_evolution_stats('NOOP', 0.0)
        
        # Add operation to transaction queue
        self.operations.append(operation)
        
        logging.debug(f"Added evolution-enabled chunk {len(self.operations)-1} to transaction {self.transaction_id}")
        return True
    
    def _acquire_evolution_lock(self) -> bool:
        """Acquire evolution lock for this user to ensure transaction isolation."""
        try:
            lock_acquired = _evolution_lock_manager.acquire_user_lock(
                self.user_id, 
                self.transaction_id, 
                timeout=30.0
            )
            
            if lock_acquired:
                self._evolution_lock_acquired = True
                logging.debug(f"Evolution lock acquired for user {self.user_id}, transaction {self.transaction_id}")
                return True
            else:
                logging.warning(f"Failed to acquire evolution lock for user {self.user_id}, transaction {self.transaction_id}")
                return False
                
        except Exception as e:
            logging.error(f"Error acquiring evolution lock for user {self.user_id}: {e}")
            return False
    
    def _release_evolution_lock(self) -> bool:
        """Release evolution lock for this user."""
        if not self._evolution_lock_acquired:
            return True
        
        try:
            lock_released = _evolution_lock_manager.release_user_lock(
                self.user_id, 
                self.transaction_id
            )
            
            if lock_released:
                self._evolution_lock_acquired = False
                logging.debug(f"Evolution lock released for user {self.user_id}, transaction {self.transaction_id}")
                return True
            else:
                logging.warning(f"Failed to release evolution lock for user {self.user_id}, transaction {self.transaction_id}")
                return False
                
        except Exception as e:
            logging.error(f"Error releasing evolution lock for user {self.user_id}: {e}")
            return False
    
    def __del__(self):
        """Ensure evolution lock is released when transaction is destroyed."""
        try:
            if hasattr(self, '_evolution_lock_acquired') and self._evolution_lock_acquired:
                self._release_evolution_lock()
        except Exception as e:
            logging.error(f"Error in EvolutionTransaction destructor: {e}")
    
    def commit(self) -> Tuple[bool, str, List[Any]]:
        """
        Execute all operations in the transaction including evolution processing.
        Overrides parent commit to handle evolution-enhanced operations.
        """
        if self.committed:
            raise ValueError("Transaction already committed")

        if not self.operations:
            return True, "No operations to commit", []

        logging.info(f"Committing evolution transaction {self.transaction_id} with {len(self.operations)} operations")

        try:
            # Execute all operations
            success = True
            for i, operation in enumerate(self.operations):
                try:
                    logging.debug(f"Executing evolution operation {i+1}/{len(self.operations)} in transaction {self.transaction_id}")

                    if operation['type'] == 'add_memory_with_evolution':
                        # Use atomic evolution operation execution
                        operation_success, operation_result = self._execute_evolution_operation(operation, i)
                        
                        if not operation_success:
                            logging.error(f"Evolution transaction {self.transaction_id} operation {i} failed: {operation_result}")
                            success = False
                            break

                        self.results.append(operation_result)
                        logging.debug(f"Evolution transaction {self.transaction_id} operation {i} completed successfully")
                        
                    elif operation['type'] == 'add_memory':
                        # Handle legacy operations from parent class
                        result = add_memory_with_retry(
                            self.memory_client,
                            operation['content'],
                            user_id=self.user_id,
                            metadata=operation['metadata']
                        )

                        success_status, message = validate_mem0_response(result, "add_memory")
                        if not success_status:
                            logging.error(f"Evolution transaction {self.transaction_id} operation {i} failed validation: {message}")
                            success = False
                            break

                        self.results.append(result)

                except Exception as e:
                    logging.error(f"Evolution transaction {self.transaction_id} operation {i} failed: {str(e)}")
                    success = False
                    break

            if not success:
                logging.warning(f"Evolution transaction {self.transaction_id} failed, initiating rollback")
                rollback_success, rollback_message = self.rollback()
                return False, f"Evolution transaction failed and rollback {'succeeded' if rollback_success else 'failed'}: {rollback_message}", []

            # Verify all chunks are accessible
            if not self._verify_chunks():
                logging.error(f"Evolution transaction {self.transaction_id} chunk verification failed, initiating rollback")
                rollback_success, rollback_message = self.rollback()
                return False, f"Chunk verification failed and rollback {'succeeded' if rollback_success else 'failed'}: {rollback_message}", []

            self.committed = True
            duration = (datetime.datetime.now() - self.start_time).total_seconds()
            
            # Log and persist evolution statistics
            if self._is_evolution_enabled():
                logging.info(f"Evolution transaction {self.transaction_id} evolution stats: {self.evolution_stats}")
                
                # Persist statistics to database for tracking and analysis
                stats_persisted = self.persist_evolution_statistics()
                if stats_persisted:
                    logging.debug(f"Evolution statistics persisted for transaction {self.transaction_id}")
                else:
                    logging.warning(f"Failed to persist evolution statistics for transaction {self.transaction_id}")
            
            logging.info(f"Evolution transaction {self.transaction_id} committed successfully in {duration:.3f}s")
            
            # Release evolution lock on successful commit
            self._release_evolution_lock()

            return True, f"Evolution transaction committed successfully with {len(self.results)} operations", self.results

        except Exception as e:
            logging.error(f"Evolution transaction {self.transaction_id} commit failed with exception: {str(e)}")
            # Release evolution lock before rollback
            self._release_evolution_lock()
            rollback_success, rollback_message = self.rollback()
            return False, f"Evolution transaction failed with exception and rollback {'succeeded' if rollback_success else 'failed'}: {str(e)}", []
    
    def _execute_evolution_operation(self, operation: dict, operation_index: int) -> Tuple[bool, Any]:
        """
        Execute a single evolution operation atomically.
        
        Args:
            operation: Evolution operation dictionary
            operation_index: Index of operation in transaction
            
        Returns:
            Tuple of (success, result)
        """
        try:
            # Extract operation details
            content = operation['content']
            metadata = operation['metadata']
            evolution_result = operation.get('evolution_result')
            
            logging.debug(f"Executing evolution operation {operation_index} for transaction {self.transaction_id}")
            
            # If evolution was processed, log the results
            if evolution_result and evolution_result.get('processed'):
                evo_details = evolution_result.get('result', {})
                if 'evolution_result' in evo_details:
                    evo_data = evo_details['evolution_result']
                    if hasattr(evo_data, 'operations'):
                        logging.info(f"Evolution operation {operation_index} processed {len(evo_data.operations)} evolution steps")
                        
                        # Create rollback handler for evolution operations
                        rollback_handler = {
                            'type': 'evolution_rollback',
                            'operation_index': operation_index,
                            'evolution_operations': evo_data.operations,
                            'original_content': content
                        }
                        self.rollback_handlers.append(rollback_handler)
            
            # Execute the actual memory storage
            result = add_memory_with_retry(
                self.memory_client,
                content,
                user_id=self.user_id,
                metadata=metadata
            )
            
            # Validate result
            success_status, message = validate_mem0_response(result, "add_memory")
            if not success_status:
                logging.error(f"Evolution operation {operation_index} validation failed: {message}")
                return False, message
            
            logging.debug(f"Evolution operation {operation_index} executed successfully")
            return True, result
            
        except Exception as e:
            logging.error(f"Evolution operation {operation_index} execution failed: {str(e)}")
            return False, str(e)
    
    def _rollback_evolution_operation(self, rollback_handler: dict) -> Tuple[bool, str]:
        """
        Rollback a specific evolution operation.
        
        Args:
            rollback_handler: Rollback handler dictionary
            
        Returns:
            Tuple of (success, message)
        """
        try:
            operation_index = rollback_handler['operation_index']
            rollback_type = rollback_handler['type']
            
            logging.info(f"Rolling back evolution operation {operation_index} of type {rollback_type}")
            
            if rollback_type == 'evolution_rollback':
                # Handle evolution-specific rollback
                evolution_operations = rollback_handler.get('evolution_operations', [])
                
                # Log evolution operations being rolled back
                for i, evo_op in enumerate(evolution_operations):
                    op_type = evo_op.get('operation_type', 'UNKNOWN')
                    logging.debug(f"Reversing evolution operation {i}: {op_type}")
                    
                    # Update rollback statistics
                    self._update_evolution_stats(f"ROLLBACK_{op_type}", 0.0, success=True)
                
                # If there are any database-specific evolution operations, handle them here
                if self.db_session and hasattr(self.db_session, 'rollback'):
                    try:
                        # Any evolution-specific database operations would be rolled back here
                        # For now, we rely on the parent rollback for memory deletion
                        pass
                    except Exception as db_error:
                        logging.warning(f"Evolution database rollback warning for operation {operation_index}: {db_error}")
                
                return True, f"Evolution operation {operation_index} rolled back successfully"
            
            else:
                return False, f"Unknown rollback type: {rollback_type}"
                
        except Exception as e:
            logging.error(f"Evolution rollback failed for operation {rollback_handler.get('operation_index', 'unknown')}: {str(e)}")
            return False, str(e)
    
    def rollback(self) -> Tuple[bool, str]:
        """
        Enhanced rollback with evolution operation support.
        Extends parent rollback to handle evolution-specific operations.
        """
        logging.warning(f"Rolling back evolution transaction {self.transaction_id}")
        
        # First, handle evolution-specific rollbacks
        evolution_rollback_errors = []
        evolution_rollbacks_successful = 0
        
        for rollback_handler in reversed(self.rollback_handlers):  # Reverse order for proper rollback
            try:
                success, message = self._rollback_evolution_operation(rollback_handler)
                if success:
                    evolution_rollbacks_successful += 1
                    logging.debug(f"Evolution rollback successful: {message}")
                else:
                    evolution_rollback_errors.append(f"Evolution rollback failed: {message}")
                    logging.error(f"Evolution rollback failed: {message}")
            except Exception as e:
                error_msg = f"Evolution rollback exception: {str(e)}"
                evolution_rollback_errors.append(error_msg)
                logging.error(error_msg)
        
        # Then call parent rollback for standard memory operations
        parent_success, parent_message = super().rollback()
        
        # Combine results
        total_errors = evolution_rollback_errors.copy()
        if not parent_success:
            total_errors.append(f"Parent rollback failed: {parent_message}")
        
        if total_errors:
            error_summary = f"Rollback partially failed: {len(total_errors)} errors, " \
                          f"{evolution_rollbacks_successful} evolution rollbacks successful"
            logging.error(f"Evolution transaction {self.transaction_id} rollback errors: {total_errors}")
            
            # Always release evolution lock even on rollback failure
            self._release_evolution_lock()
            return False, error_summary
        else:
            success_summary = f"Evolution transaction rollback successful: " \
                            f"{evolution_rollbacks_successful} evolution operations, " \
                            f"parent rollback: {parent_message}"
            logging.info(f"Evolution transaction {self.transaction_id} rollback completed: {success_summary}")
            
            # Always release evolution lock after rollback
            self._release_evolution_lock()
            return True, success_summary


def verify_memory_storage(response, memory_client, request_id=None, user_id=None):
    """Verify that memories were actually stored by attempting to retrieve them."""
    import time
    
    if not isinstance(response, dict) or 'results' not in response:
        return False, "Invalid response format for verification"
    
    results = response['results']
    if not isinstance(results, list):
        return False, "Invalid results format for verification"
    
    # Empty results might be valid (e.g., due to deduplication)
    if len(results) == 0:
        return True, "No new memories to verify (possibly due to deduplication)"
    
    stored_memory_ids = []
    for result in results:
        if isinstance(result, dict) and 'id' in result:
            stored_memory_ids.append(result['id'])
    
    if not stored_memory_ids:
        return False, "No memory IDs found in response for verification"
    
    # Wait for eventual consistency (increased for better reliability)
    time.sleep(0.5)
    
    # Attempt to retrieve stored memories to verify they exist (with retry)
    max_retries = 3
    for attempt in range(max_retries):
        try:
            verification_start = time.time()
            
            # Try to get all memories to see if our stored memories are present
            all_memories = memory_client.get_all(user_id=user_id)
            
            verification_duration = time.time() - verification_start
            
            if request_id:
                logging.info(f"[REQ_{request_id}] Memory verification attempt {attempt + 1} completed in {verification_duration:.3f}s")
            
            if not isinstance(all_memories, dict) or 'results' not in all_memories:
                if attempt < max_retries - 1:
                    time.sleep(0.2)  # Brief wait before retry
                    continue
                return False, "Unable to retrieve memories for verification"
            
            existing_memory_ids = set()
            for memory in all_memories['results']:
                if isinstance(memory, dict) and 'id' in memory:
                    existing_memory_ids.add(memory['id'])
            
            # Check if all stored memories are present
            missing_memories = []
            for memory_id in stored_memory_ids:
                if memory_id not in existing_memory_ids:
                    missing_memories.append(memory_id)
            
            if missing_memories:
                if attempt < max_retries - 1:
                    if request_id:
                        logging.info(f"[REQ_{request_id}] Verification attempt {attempt + 1}: {len(missing_memories)} memories not yet visible, retrying...")
                    time.sleep(0.3)  # Wait before retry
                    continue
                return False, f"Memory verification failed: {len(missing_memories)} memories not found after storage"
            
            if request_id:
                logging.info(f"[REQ_{request_id}] Memory verification successful: {len(stored_memory_ids)} memories confirmed stored")
            
            return True, f"Memory verification successful: {len(stored_memory_ids)} memories confirmed stored"
        
        except Exception as e:
            error_msg = f"Memory verification failed due to error: {str(e)}"
            if request_id:
                logging.error(f"[REQ_{request_id}] {error_msg}")
            return False, error_msg

def validate_text_length(text: str) -> tuple[bool, str]:
    """Validate text length for memory storage."""
    max_length = get_max_text_length_from_config()
    if len(text) > max_length:
        return False, f"Warning: Text is {len(text)} characters (over {max_length} limit). Will be automatically chunked for processing."
    return True, "Text length valid"

def chunk_text(text: str, max_length: int = None) -> list[str]:
    """
    Chunk text into smaller pieces for reliable memory storage.
    Implements smart chunking that preserves sentence boundaries when possible.
    """
    if max_length is None:
        max_length = get_max_text_length_from_config()
    
    # If text is already short enough, return as-is
    if len(text) <= max_length:
        return [text]
    
    logging.info(f"CHUNKING: Text length {len(text)} exceeds limit {max_length}, chunking...")
    
    chunks = []
    current_pos = 0
    
    while current_pos < len(text):
        # Calculate chunk end position
        chunk_end = min(current_pos + max_length, len(text))
        
        # If this is not the last chunk, try to break at sentence boundary
        if chunk_end < len(text):
            # Look for sentence endings within the last 200 chars of the chunk
            search_start = max(current_pos, chunk_end - 200)
            sentence_endings = []
            
            for i in range(search_start, chunk_end):
                if text[i] in '.!?':
                    # Check if this is likely a sentence ending (not abbreviation)
                    if i + 1 < len(text) and text[i + 1] in ' \n\t':
                        sentence_endings.append(i + 1)
            
            # Use the last sentence ending found, or fall back to word boundary
            if sentence_endings:
                chunk_end = sentence_endings[-1]
            else:
                # Look for word boundary within last 100 chars
                search_start = max(current_pos, chunk_end - 100)
                word_boundaries = []
                
                for i in range(search_start, chunk_end):
                    if text[i] in ' \n\t':
                        word_boundaries.append(i)
                
                if word_boundaries:
                    chunk_end = word_boundaries[-1]
        
        # Extract the chunk
        chunk = text[current_pos:chunk_end].strip()
        if chunk:  # Only add non-empty chunks
            chunks.append(chunk)
            logging.info(f"CHUNKING: Created chunk {len(chunks)} with length {len(chunk)}")
        
        current_pos = chunk_end
    
    logging.info(f"CHUNKING: Split {len(text)} chars into {len(chunks)} chunks")
    return chunks

async def _add_single_memory(uid: str, client_name: str, text: str, parent_request_id: int = None) -> str:
    """
    Add a single memory chunk to the system.
    This is used internally for processing individual chunks.
    """
    chunk_start_time = datetime.datetime.now()
    req_prefix = f"[REQ_{parent_request_id}_CHUNK]" if parent_request_id else "[CHUNK]"
    
    logging.info(f"{req_prefix} CHUNKING: Processing single memory chunk of {len(text)} chars")
    
    # Get memory client safely
    memory_client = get_memory_client_safe()
    if not memory_client:
        return "Error: Memory system is currently unavailable."
    
    try:
        db = SessionLocal()
        try:
            # Get or create user and app
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
            
            # Check if app is active
            if not app.is_active:
                return f"Error: App {app.name} is currently paused on OpenMemory."
            
            # CLAUDE DESKTOP FIX: Use shared vector store context for chunking too
            mem0_user_id = uid  # Use shared context to solve Claude Desktop isolation issue
            logging.info(f"CHUNKING: Calling mem0 for chunk of {len(text)} chars using shared context '{mem0_user_id}'")
            
            response = add_memory_with_retry(
                memory_client,
                text,
                user_id=mem0_user_id,
                metadata={
                    "source_app": "openmemory",
                    "mcp_client": client_name,
                }
            )
            
            # Validate mem0 response
            response_valid, response_message = validate_mem0_response(response, "add_memory")
            if not response_valid:
                logging.error(f"CHUNKING: Memory storage failed for chunk: {response_message}")
                return f"Error: {response_message}"
            
            # Note: Verification temporarily disabled due to timing/indexing issues
            # The memories are being stored successfully as confirmed by search functionality
            logging.info(f"CHUNKING: Memory storage completed for chunk. Verification skipped due to indexing delays.")
            
            # Process the response and update database
            if isinstance(response, dict) and 'results' in response:
                for result in response['results']:
                    memory_id = uuid.UUID(result['id'])
                    memory = db.query(Memory).filter(Memory.id == memory_id).first()
                    
                    if result['event'] == 'ADD':
                        if not memory:
                            memory = Memory(
                                id=memory_id,
                                user_id=user.id,
                                app_id=app.id,
                                content=result['memory'],
                                state=MemoryState.active.value
                            )
                            db.add(memory)
                        else:
                            memory.state = MemoryState.active.value
                            memory.content = result['memory']
                        
                        # Create history entry
                        history = MemoryStatusHistory(
                            memory_id=memory_id,
                            changed_by=user.id,
                            old_state=MemoryState.deleted.value if memory else None,
                            new_state=MemoryState.active.value
                        )
                        db.add(history)
                
                db.commit()
                logging.info(f"CHUNKING: Successfully processed chunk")
                return "Successfully processed chunk"
        finally:
            db.close()
    except Exception as e:
        logging.error(f"CHUNKING: Error processing chunk: {e}")
        return f"Error processing chunk: {e}"

# Retry-enabled memory operations with enhanced logging
@log_memory_operation("add_memory")
@retry_memory_operation(max_attempts=3)
def add_memory_with_retry(memory_client, text, user_id, metadata):
    """Add memory with retry logic and validation."""
    return memory_client.add(text, user_id=user_id, metadata=metadata)


@log_memory_operation("get_memories")
@retry_memory_operation(max_attempts=3)
def get_all_memories_with_retry(memory_client, user_id):
    """Get all memories with retry logic."""
    return memory_client.get_all(user_id=user_id)


@log_memory_operation("search_memories")
@retry_operation(max_attempts=3, backoff_factor=1.2, retry_on_validation_failure=False)
def search_memories_with_retry(memory_client, query, user_id, limit=10):
    """Search memories with retry logic (no validation since search has different response format)."""
    return memory_client.search(query, user_id=user_id, limit=limit)


@retry_operation(max_attempts=3, backoff_factor=1.2, retry_on_validation_failure=False)
def query_vector_store_with_retry(vector_store_client, collection_name, query, query_filter, limit):
    """Query vector store with retry logic."""
    return vector_store_client.query_points(
        collection_name=collection_name,
        query=query,
        query_filter=query_filter,
        limit=limit
    )


# Initialize SSE transport
sse = SseServerTransport("/mcp/messages/")

# Global tracking for concurrent requests
_active_requests = {}
_request_counter = 0

@mcp.tool(description="Add a new memory. This method is called everytime the user informs anything about themselves, their preferences, or anything that has any relevant information which can be useful in the future conversation. This can also be called when the user asks you to remember something.")
async def add_memories(text: str) -> str:
    global _request_counter
    _request_counter += 1
    request_id = _request_counter
    start_time = datetime.datetime.now()
    
    uid = user_id_var.get(None)
    client_name = client_name_var.get(None)
    session_key = f"{uid}:{client_name}"

    # ENHANCED DIAGNOSTIC: Track concurrent requests and context state
    logging.info(f"[REQ_{request_id}] DIAGNOSTIC: add_memories called with uid={uid}, client_name={client_name}, text_length={len(text)} chars")
    logging.info(f"[REQ_{request_id}] DIAGNOSTIC: Active concurrent requests: {len(_active_requests)}")
    logging.info(f"[REQ_{request_id}] DIAGNOSTIC: Context state - uid_var_id: {id(user_id_var)}, client_var_id: {id(client_name_var)}")
    
    # Track this request
    _active_requests[request_id] = {
        'uid': uid,
        'client_name': client_name,
        'start_time': start_time,
        'text_length': len(text)
    }
    
    # Write enhanced debugging to file
    with open("/tmp/debug_add_memories.log", "a") as f:
        f.write(f"[REQ_{request_id}] {start_time.isoformat()} - add_memories called: uid={uid}, client_name={client_name}, text_length={len(text)}, concurrent_count={len(_active_requests)}\n")

    if not uid:
        logging.error("Error: user_id not provided in context variables")
        return "Error: user_id not provided"
    if not client_name:
        logging.error("Error: client_name not provided in context variables")
        return "Error: client_name not provided"
    
    # Check text length and determine if chunking is needed
    max_length = get_max_text_length_from_config()
    needs_chunking = len(text) > max_length
    
    if needs_chunking:
        logging.warning(f"CHUNKING: Text length {len(text)} exceeds limit {max_length}, will auto-chunk")
        logging.info(f"DIAGNOSTIC: Text length check - Input: {len(text)} chars, Max allowed: {max_length} chars")
        
        # Chunk the text
        chunks = chunk_text(text, max_length)
        logging.info(f"CHUNKING: Created {len(chunks)} chunks from original text")

        # Get memory client for transaction
        memory_client = get_memory_client_safe()
        if not memory_client:
            if request_id in _active_requests:
                del _active_requests[request_id]
            return "Error: Memory system is currently unavailable for chunked operation. Please try again later."

        # Use transaction for atomic chunked operation
        transaction = MemoryTransaction(memory_client, uid, client_name)

        # Add all chunks to transaction
        for i, chunk in enumerate(chunks):
            chunk_metadata = {
                "source_app": "openmemory",
                "mcp_client": client_name,
                "chunk_part": f"{i+1}/{len(chunks)}",
                "original_length": len(text),
                "chunk_length": len(chunk)
            }

            chunk_content = f"[Part {i+1}/{len(chunks)}] {chunk}"
            success = transaction.add_memory_chunk(chunk_content, chunk_metadata)

            if not success:
                logging.error(f"[REQ_{request_id}] CHUNKING: Failed to add chunk {i+1} to transaction")
                if request_id in _active_requests:
                    del _active_requests[request_id]
                return f"Error: Failed to prepare chunk {i+1}/{len(chunks)} for processing"

        logging.info(f"[REQ_{request_id}] CHUNKING: Added {len(chunks)} chunks to transaction {transaction.transaction_id}")

        # Commit the transaction
        commit_success, commit_message, results = transaction.commit()

        total_duration = (datetime.datetime.now() - start_time).total_seconds()

        if commit_success:
            logging.info(f"[REQ_{request_id}] CHUNKING: Transaction committed successfully in {total_duration:.3f}s")
            logging.info(f"[REQ_{request_id}] CHUNKING: Processed {len(results)} chunks atomically")

            # Clean up request tracking
            if request_id in _active_requests:
                del _active_requests[request_id]

            return f"Successfully chunked and stored {len(results)} memory pieces from {len(text)} character text via app '{client_name}' (Transaction: {transaction.transaction_id})"
        else:
            logging.error(f"[REQ_{request_id}] CHUNKING: Transaction failed: {commit_message}")
            logging.error(f"[REQ_{request_id}] DIAGNOSTIC: Active concurrent requests during chunk failure: {len(_active_requests)}")

            # Clean up request tracking
            if request_id in _active_requests:
                del _active_requests[request_id]

            return f"Error: Chunked operation failed - {commit_message}"
    else:
        logging.info(f"DIAGNOSTIC: Text length {len(text)} is within limit {max_length}, processing normally")
    
    # Check if initialization is complete for this session
    if session_key not in _initialization_complete or not _initialization_complete[session_key]:
        logging.warning(f"[REQ_{request_id}] [MCP_FIX] Handled initialization race condition: Received request before initialization was complete")
        logging.info(f"[REQ_{request_id}] DIAGNOSTIC: Session {session_key} not fully initialized. Status: {_initialization_complete.get(session_key, 'NOT_FOUND')}")
        logging.info(f"[REQ_{request_id}] DIAGNOSTIC: Total active sessions: {len(_initialization_complete)}")
        logging.info(f"[REQ_{request_id}] DIAGNOSTIC: All session keys: {list(_initialization_complete.keys())}")
        
        # Clean up request tracking
        if request_id in _active_requests:
            del _active_requests[request_id]
            
        # For memory operations, we need to ensure proper initialization
        # Return early with a clear error message instead of proceeding with uninitialized state
        return "Error: Memory system is initializing. Please wait a moment and try again."

    # Get memory client singleton for degradation support
    logging.info(f"[MCP_DIAGNOSTIC] Attempting to get memory client singleton for session {session_key}")
    memory_singleton = get_memory_singleton_safe()
    if not memory_singleton:
        logging.error(f"[MCP_DIAGNOSTIC] Memory client singleton unavailable for session {session_key}")
        return "Error: Memory system is currently unavailable. Please try again later."
    logging.info(f"[MCP_DIAGNOSTIC] Memory client singleton successfully obtained for session {session_key}")

    # Also get the raw memory client for compatibility with existing code
    memory_client = get_memory_client_safe()
    if not memory_client:
        logging.error(f"[MCP_DIAGNOSTIC] Raw memory client unavailable for session {session_key}")
        return "Error: Memory system is currently unavailable. Please try again later."

    try:
        db = SessionLocal()
        try:
            # Get or create user and app with enhanced logging
            logging.info(f"Getting or creating user '{uid}' and app '{client_name}'")
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
            logging.info(f"User ID: {user.id}, App ID: {app.id}, App Name: {app.name}, App Active: {app.is_active}")
            
            # DIAGNOSTIC: Check user's total memory count across all apps
            total_user_memories = db.query(Memory).filter(
                Memory.user_id == user.id,
                Memory.state == MemoryState.active.value
            ).count()
            app_specific_memories = db.query(Memory).filter(
                Memory.user_id == user.id,
                Memory.app_id == app.id,
                Memory.state == MemoryState.active.value
            ).count()
            logging.info(f"DIAGNOSTIC: User '{uid}' has {total_user_memories} total memories, {app_specific_memories} for app '{client_name}'")

            # Check if app is active
            if not app.is_active:
                logging.warning(f"App {app.name} is paused")
                return f"Error: App {app.name} is currently paused on OpenMemory. Cannot create new memories."

            logging.info(f"Adding memory for user_id='{uid}'")
            
            # Get initial memory count for validation
            initial_memory_count = db.query(Memory).filter(
                Memory.user_id == user.id,
                Memory.app_id == app.id,
                Memory.state == MemoryState.active.value
            ).count()
            logging.info(f"Initial active memory count: {initial_memory_count}")
            
            logging.info(f"DIAGNOSTIC: About to call memory_client.add with text length: {len(text)} chars")
            logging.info(f"DIAGNOSTIC: mem0 call parameters - user_id='{uid}', text='{text[:100]}...', metadata={{'source_app': 'openmemory', 'mcp_client': '{client_name}'}}")
            
            # DIAGNOSTIC: Check if this is likely a mem0 LLM classification issue
            if app_specific_memories == 0:
                logging.warning(f"DIAGNOSTIC: No existing memories for app '{client_name}' - mem0 LLM may classify content as non-memorable")
                logging.info(f"DIAGNOSTIC: Total user memories across all apps: {total_user_memories}")
                if total_user_memories > 0:
                    logging.info(f"DIAGNOSTIC: SOLUTION - Using shared user context to leverage existing {total_user_memories} memories")
            
            # CLAUDE DESKTOP FIX: Use shared vector store context to solve isolation issue
            # Force all clients to use the same mem0 context to leverage existing memories
            mem0_user_id = uid  # Remove client-specific isolation - use shared context
            logging.info(f"DIAGNOSTIC: Using shared vector store context '{mem0_user_id}' (DB memories: {total_user_memories}, client: {client_name})")
            
            # ENHANCED DIAGNOSTIC: Pre-request state check
            logging.info(f"[REQ_{request_id}] DIAGNOSTIC: About to call mem0 - checking vector store state")
            try:
                # Check if mem0 can see existing memories
                search_result = memory_client.search(query="test", user_id=mem0_user_id, limit=1)
                existing_memories_count = len(search_result) if search_result else 0
                logging.info(f"[REQ_{request_id}] DIAGNOSTIC: mem0 vector store shows {existing_memories_count} existing memories for user '{mem0_user_id}'")
            except Exception as search_error:
                logging.warning(f"[REQ_{request_id}] DIAGNOSTIC: Could not check existing memories: {search_error}")
            
            # EVOLUTION INTELLIGENCE: Process memory evolution pipeline
            evolution_processed = False
            evolution_result = None
            try:
                evolution_processed, evolution_result = await enhance_add_memories(
                    text=text,
                    user_id=mem0_user_id,
                    client_name=client_name,
                    db_session=db,
                    context={
                        "request_id": request_id,
                        "existing_memories_count": existing_memories_count if 'existing_memories_count' in locals() else 0
                    }
                )
                
                if evolution_processed:
                    logging.info(f"[REQ_{request_id}] EVOLUTION: Successfully processed memory evolution")
                else:
                    logging.info(f"[REQ_{request_id}] EVOLUTION: {evolution_result.get('reason', 'not_processed')}")
                    
            except Exception as evolution_error:
                logging.error(f"[REQ_{request_id}] EVOLUTION: Error in evolution processing: {evolution_error}")
                # Continue with normal mem0 processing even if evolution fails
            
            # Call mem0 with enhanced timing and graceful degradation
            mem0_start_time = datetime.datetime.now()
            response = memory_singleton.add_memory_with_degradation(
                text,
                metadata={
                    "source_app": "openmemory",
                    "mcp_client": client_name
                },
                user_id=mem0_user_id
            )
            mem0_duration = (datetime.datetime.now() - mem0_start_time).total_seconds()
            
            # Check if operation was performed in degraded mode
            is_degraded = any(item.get('fallback_mode', False) for item in response.get('results', []))
            if is_degraded:
                logging.warning(f"[REQ_{request_id}] Memory stored in degraded mode (vector store unavailable)")
                print(f"DEGRADED: Memory stored in database-only mode for request {request_id}")

            logging.info(f"[REQ_{request_id}] DIAGNOSTIC: mem0.add() completed in {mem0_duration:.3f}s")
            logging.info(f"[REQ_{request_id}] Qdrant response: {response}")
            logging.info(f"[REQ_{request_id}] DIAGNOSTIC: Response received, type: {type(response)}, has_results: {'results' in response if isinstance(response, dict) else 'N/A'}")

            # Enhanced response validation
            response_valid, response_message = validate_mem0_response(response, "add_memory")
            if not response_valid:
                logging.error(f"[REQ_{request_id}] Memory storage validation failed: {response_message}")
                logging.error(f"[REQ_{request_id}] DIAGNOSTIC: Full response from mem0: {response}")
                logging.error(f"[REQ_{request_id}] DIAGNOSTIC: Response type: {type(response)}")
                logging.error(f"[REQ_{request_id}] DIAGNOSTIC: Request duration so far: {(datetime.datetime.now() - start_time).total_seconds():.3f}s")
                
                # Clean up request tracking
                if request_id in _active_requests:
                    del _active_requests[request_id]
                return {"error": response_message}
            
            # Note: Verification temporarily disabled due to timing/indexing issues
            # The memories are being stored successfully as confirmed by search functionality
            logging.info(f"[REQ_{request_id}] Memory storage completed. Verification skipped due to indexing delays.")

            # Process the response and update database
            if isinstance(response, dict) and 'results' in response:
                logging.info(f"Processing {len(response['results'])} results from Qdrant")
                for result in response['results']:
                    memory_id = uuid.UUID(result['id'])
                    memory = db.query(Memory).filter(Memory.id == memory_id).first()

                    # Handle missing 'event' field gracefully (for degraded mode responses)
                    event_type = result.get('event', 'ADD')  # Default to 'ADD' if missing
                    is_fallback_mode = result.get('fallback_mode', False)

                    logging.info(f"Processing result: {event_type} for memory_id={memory_id} (fallback_mode: {is_fallback_mode})")

                    if event_type == 'ADD':
                        if not memory:
                            memory = Memory(
                                id=memory_id,
                                user_id=user.id,
                                app_id=app.id,
                                content=result['memory'],
                                state=MemoryState.active.value
                            )
                            db.add(memory)
                            logging.info(f"Created new memory in database: {memory_id}")
                        else:
                            memory.state = MemoryState.active.value
                            memory.content = result['memory']
                            logging.info(f"Updated existing memory in database: {memory_id}")

                        # Create history entry
                        history = MemoryStatusHistory(
                            memory_id=memory_id,
                            changed_by=user.id,
                            old_state=MemoryState.deleted.value if memory else None,
                            new_state=MemoryState.active.value
                        )
                        db.add(history)

                    elif event_type == 'DELETE':
                        if memory:
                            memory.state = MemoryState.deleted.value
                            memory.deleted_at = datetime.datetime.now(datetime.UTC)
                            logging.info(f"Marked memory as deleted: {memory_id}")
                            # Create history entry
                            history = MemoryStatusHistory(
                                memory_id=memory_id,
                                changed_by=user.id,
                                old_state=MemoryState.active.value,
                                new_state=MemoryState.deleted.value
                            )
                            db.add(history)

                logging.info("Committing database changes")
                db.commit()
                logging.info("Database commit successful")
                
                # EVOLUTION INTELLIGENCE: Store evolution operations in database
                if evolution_processed and evolution_result and evolution_result.get('status') == 'success':
                    try:
                        from app.models import EvolutionOperation, EvolutionOperationType
                        
                        evolution_data = evolution_result.get('evolution_result')
                        if evolution_data and evolution_data.operations:
                            for operation_data in evolution_data.operations:
                                evolution_op = EvolutionOperation(
                                    id=uuid.UUID(operation_data['id']),
                                    memory_id=uuid.UUID(operation_data['memory_id']) if operation_data.get('memory_id') else None,
                                    user_id=user.id,
                                    operation_type=EvolutionOperationType(operation_data['operation_type']),
                                    candidate_fact=operation_data['candidate_fact'],
                                    existing_memory_content=operation_data.get('existing_memory_content'),
                                    similarity_score=operation_data.get('similarity_score'),
                                    confidence_score=operation_data.get('confidence_score'),
                                    reasoning=operation_data.get('reasoning'),
                                    metadata_=operation_data.get('metadata', {})
                                )
                                db.add(evolution_op)
                            
                            db.commit()
                            logging.info(f"[REQ_{request_id}] EVOLUTION: Stored {len(evolution_data.operations)} evolution operations")
                    except Exception as evo_store_error:
                        logging.error(f"[REQ_{request_id}] EVOLUTION: Error storing evolution operations: {evo_store_error}")
                        db.rollback()
                
                # Post-commit validation: Verify memories were actually stored
                final_memory_count = db.query(Memory).filter(
                    Memory.user_id == user.id,
                    Memory.app_id == app.id,
                    Memory.state == MemoryState.active.value
                ).count()
                logging.info(f"Final active memory count: {final_memory_count}")
                
                memories_added = final_memory_count - initial_memory_count
                if memories_added == 0:
                    logging.info(f"No new memories stored (count unchanged: {initial_memory_count} -> {final_memory_count}). This is normal when mem0 filters out duplicate or irrelevant content.")
                else:
                    logging.info(f"Successfully stored {memories_added} new memory/memories")

            total_duration = (datetime.datetime.now() - start_time).total_seconds()
            logging.info(f"[REQ_{request_id}] DIAGNOSTIC: Request completed successfully in {total_duration:.3f}s")
            
            # Clean up request tracking
            if request_id in _active_requests:
                del _active_requests[request_id]
            
            # Create enhanced response message with evolution information
            response_message = f"Successfully added {memories_added if 'memories_added' in locals() else 'memory'} for user '{uid}' via app '{client_name}'"
            
            if evolution_processed and evolution_result:
                if evolution_result.get('status') == 'success':
                    evo_data = evolution_result.get('evolution_result')
                    if evo_data and evo_data.operations:
                        ops_summary = {}
                        for op in evo_data.operations:
                            op_type = op['operation_type']
                            ops_summary[op_type] = ops_summary.get(op_type, 0) + 1
                        
                        evo_summary = ", ".join([f"{count} {op_type}" for op_type, count in ops_summary.items()])
                        response_message += f" [Evolution: {evo_summary}]"
                else:
                    response_message += f" [Evolution: {evolution_result.get('reason', 'processed')}]"
            
            return response_message
        finally:
            db.close()
    except Exception as e:
        total_duration = (datetime.datetime.now() - start_time).total_seconds()
        logging.exception(f"[REQ_{request_id}] ERROR: Exception after {total_duration:.3f}s: {e}")
        logging.error(f"[REQ_{request_id}] DIAGNOSTIC: Context at error - uid={uid}, client_name={client_name}, active_requests={len(_active_requests)}")
        
        # Clean up request tracking
        if request_id in _active_requests:
            del _active_requests[request_id]
            
        return f"Error adding to memory: {e}"


@mcp.tool(description="Search through stored memories. This method is called EVERYTIME the user asks anything.")
async def search_memory(query: str) -> str:
    uid = user_id_var.get(None)
    client_name = client_name_var.get(None)
    if not uid:
        return "Error: user_id not provided"
    if not client_name:
        return "Error: client_name not provided"

    # Get memory singleton safely
    memory_singleton = get_memory_singleton_safe()
    if not memory_singleton:
        return "Error: Memory system is currently unavailable. Please try again later."

    try:
        db = SessionLocal()
        try:
            # Get or create user and app
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)

            # Get accessible memory IDs based on ACL
            user_memories = db.query(Memory).filter(Memory.user_id == user.id).all()
            accessible_memory_ids = [memory.id for memory in user_memories if check_memory_access_permissions(db, memory, app.id)]
            
            # Use graceful degradation for search
            search_response = memory_singleton.search_memory_with_degradation(query, limit=10, user_id=uid)
            
            # Check if search was performed in degraded mode
            if search_response.get('degraded_mode', False):
                logging.warning(f"Search performed in degraded mode for user {uid}")
                print(f"DEGRADED: Search operation with limited functionality for user {uid}")
                return json.dumps({
                    "results": [],
                    "degraded_mode": True,
                    "message": "Vector store unavailable - search functionality limited"
                }, indent=2)
            
            # Process normal search results
            memories = search_response.get('results', [])
            
            # Filter memories based on ACL if we have accessible memory IDs
            if accessible_memory_ids:
                accessible_memory_ids_str = [str(memory_id) for memory_id in accessible_memory_ids]
                memories = [memory for memory in memories if memory.get('id') in accessible_memory_ids_str]
            
            # Format memories for consistent output
            formatted_memories = []
            for memory in memories:
                formatted_memory = {
                    "id": memory.get('id'),
                    "memory": memory.get('memory'),
                    "hash": memory.get('hash'),
                    "created_at": memory.get('created_at'),
                    "updated_at": memory.get('updated_at'),
                    "score": memory.get('score', 0.0),
                }
                formatted_memories.append(formatted_memory)
            
            memories = formatted_memories

            # Log memory access for each memory found
            if isinstance(memories, dict) and 'results' in memories:
                print(f"Memories: {memories}")
                for memory_data in memories['results']:
                    if 'id' in memory_data:
                        memory_id = uuid.UUID(memory_data['id'])
                        # Create access log entry
                        access_log = MemoryAccessLog(
                            memory_id=memory_id,
                            app_id=app.id,
                            access_type="search",
                            metadata_={
                                "query": query,
                                "score": memory_data.get('score'),
                                "hash": memory_data.get('hash')
                            }
                        )
                        db.add(access_log)
                db.commit()
            else:
                for memory in memories:
                    memory_id = uuid.UUID(memory['id'])
                    # Create access log entry
                    access_log = MemoryAccessLog(
                        memory_id=memory_id,
                        app_id=app.id,
                        access_type="search",
                        metadata_={
                            "query": query,
                            "score": memory.get('score'),
                            "hash": memory.get('hash')
                        }
                    )
                    db.add(access_log)
                db.commit()
            return json.dumps(memories, indent=2)
        finally:
            db.close()
    except Exception as e:
        logging.exception(e)
        return f"Error searching memory: {e}"


@mcp.tool(description="List all memories in the user's memory")
async def list_memories() -> str:
    uid = user_id_var.get(None)
    client_name = client_name_var.get(None)
    if not uid:
        return "Error: user_id not provided"
    if not client_name:
        return "Error: client_name not provided"

    # Get memory client safely for get_all operation
    memory_client = get_memory_client_safe()
    if not memory_client:
        return "Error: Memory system is currently unavailable. Please try again later."

    try:
        db = SessionLocal()
        try:
            # Get or create user and app
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)

            # Get all memories with retry logic (using raw client for get_all)
            memories = get_all_memories_with_retry(memory_client, user_id=uid)
            filtered_memories = []

            # Filter memories based on permissions
            user_memories = db.query(Memory).filter(Memory.user_id == user.id).all()
            accessible_memory_ids = [memory.id for memory in user_memories if check_memory_access_permissions(db, memory, app.id)]
            if isinstance(memories, dict) and 'results' in memories:
                for memory_data in memories['results']:
                    if 'id' in memory_data:
                        memory_id = uuid.UUID(memory_data['id'])
                        if memory_id in accessible_memory_ids:
                            # Create access log entry
                            access_log = MemoryAccessLog(
                                memory_id=memory_id,
                                app_id=app.id,
                                access_type="list",
                                metadata_={
                                    "hash": memory_data.get('hash')
                                }
                            )
                            db.add(access_log)
                            filtered_memories.append(memory_data)
                db.commit()
            else:
                for memory in memories:
                    memory_id = uuid.UUID(memory['id'])
                    memory_obj = db.query(Memory).filter(Memory.id == memory_id).first()
                    if memory_obj and check_memory_access_permissions(db, memory_obj, app.id):
                        # Create access log entry
                        access_log = MemoryAccessLog(
                            memory_id=memory_id,
                            app_id=app.id,
                            access_type="list",
                            metadata_={
                                "hash": memory.get('hash')
                            }
                        )
                        db.add(access_log)
                        filtered_memories.append(memory)
                db.commit()
            return json.dumps(filtered_memories, indent=2)
        finally:
            db.close()
    except Exception as e:
        logging.exception(f"Error getting memories: {e}")
        return f"Error getting memories: {e}"


@mcp.tool(description="Get system health status and degradation information for the memory system")
async def get_system_health() -> str:
    """Get comprehensive system health status including degradation information."""
    try:
        # Get system health status
        health_status = get_system_health_status()
        
        # Format for display
        formatted_status = format_status_for_display(health_status)
        
        # Also return JSON for programmatic access
        json_status = json.dumps(health_status, indent=2)
        
        return f"{formatted_status}\n\n--- Raw JSON Status ---\n{json_status}"
        
    except Exception as e:
        logging.exception(f"Error getting system health status: {e}")
        return f"Error getting system health status: {e}"


@mcp.tool(description="Get detailed operation metrics and performance statistics for memory operations")
async def get_operation_metrics() -> str:
    """Get comprehensive operation metrics including timing, success rates, and error analysis."""
    try:
        # Get operation metrics from the enhanced logger
        metrics = operation_logger.get_operation_metrics()
        active_operations = operation_logger.get_active_operations()
        
        # Format metrics for display
        formatted_output = ["=== Memory Operation Metrics ==="]
        formatted_output.append(f"Metrics collected at: {datetime.datetime.fromtimestamp(metrics['metrics_timestamp']).isoformat()}")
        formatted_output.append(f"Active operations: {metrics['active_operations_count']}")
        
        if active_operations:
            formatted_output.append("\n--- Currently Active Operations ---")
            for op_id, op_info in active_operations.items():
                formatted_output.append(f"  {op_id[:8]}: {op_info['operation_type']} ({op_info['duration_ms']}ms)")
                if op_info.get('is_long_running'):
                    formatted_output.append(f"    ⚠️  Long-running operation detected")
        
        if metrics['operations']:
            formatted_output.append("\n--- Operation Statistics ---")
            for op_type, stats in metrics['operations'].items():
                success_rate = (stats['success_count'] / stats['total_count'] * 100) if stats['total_count'] > 0 else 0
                formatted_output.append(f"\n{op_type.upper()}:")
                formatted_output.append(f"  Total operations: {stats['total_count']}")
                formatted_output.append(f"  Success rate: {success_rate:.1f}% ({stats['success_count']}/{stats['total_count']})")
                formatted_output.append(f"  Failures: {stats['failure_count']}")
                formatted_output.append(f"  Total retries: {stats['retry_count']}")
                
                if stats['avg_duration_ms'] > 0:
                    formatted_output.append(f"  Average duration: {stats['avg_duration_ms']:.1f}ms")
                    formatted_output.append(f"  Duration range: {stats['min_duration_ms']}-{stats['max_duration_ms']}ms")
                
                # Performance distribution
                perf_dist = stats['performance_distribution']
                total_perf = sum(perf_dist.values())
                if total_perf > 0:
                    formatted_output.append(f"  Performance distribution:")
                    for perf_level, count in perf_dist.items():
                        percentage = (count / total_perf * 100) if total_perf > 0 else 0
                        formatted_output.append(f"    {perf_level}: {count} ({percentage:.1f}%)")
                
                # Error breakdown
                if stats['error_types']:
                    formatted_output.append(f"  Error breakdown:")
                    for error_type, count in stats['error_types'].items():
                        error_percentage = (count / stats['failure_count'] * 100) if stats['failure_count'] > 0 else 0
                        formatted_output.append(f"    {error_type}: {count} ({error_percentage:.1f}% of failures)")
                
                if stats['last_operation']:
                    last_op_time = datetime.datetime.fromtimestamp(stats['last_operation'])
                    formatted_output.append(f"  Last operation: {last_op_time.isoformat()}")
        else:
            formatted_output.append("\nNo operation statistics available yet.")
        
        # Add raw JSON for programmatic access
        formatted_output.append("\n\n--- Raw JSON Metrics ---")
        formatted_output.append(json.dumps({
            "metrics": metrics,
            "active_operations": active_operations
        }, indent=2))
        
        return "\n".join(formatted_output)
        
    except Exception as e:
        logging.exception(f"Error getting operation metrics: {e}")
        return f"Error getting operation metrics: {e}"


@mcp.tool(description="Get evolution intelligence metrics and learning efficiency insights with timeframe filtering")
async def get_evolution_metrics(timeframe: str = "week") -> str:
    """
    Get comprehensive evolution intelligence metrics and learning efficiency insights.
    
    Args:
        timeframe: Time period for metrics ('day', 'week', 'month', 'year')
    
    Returns:
        Formatted evolution metrics including learning efficiency, operation breakdown,
        conflict resolution activity, and memory quality trends.
    """
    uid = user_id_var.get(None)
    client_name = client_name_var.get(None)
    
    if not uid:
        return "Error: user_id not provided"
    if not client_name:
        return "Error: client_name not provided"
    
    try:
        from app.evolution.insights_aggregator import EvolutionInsightsAggregator
        from app.evolution.evolution_tracker import get_evolution_tracker
        from app.database import SessionLocal
        
        # Validate timeframe parameter
        valid_timeframes = {"day": 1, "week": 7, "month": 30, "year": 365}
        if timeframe not in valid_timeframes:
            return f"Error: Invalid timeframe '{timeframe}'. Valid options: {', '.join(valid_timeframes.keys())}"
        
        days = valid_timeframes[timeframe]
        
        db = SessionLocal()
        try:
            # Get user ID from database
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
            user_uuid = str(user.id)
            
            # Initialize aggregator and tracker
            aggregator = EvolutionInsightsAggregator(db)
            tracker = get_evolution_tracker(db)
            
            # Get evolution statistics
            tracker_stats = await tracker.get_operation_statistics(user_uuid, days)
            
            # Get trend analysis
            trend_analysis = await aggregator.analyze_trends(user_uuid, days)
            
            # Format output
            formatted_output = [f"=== Evolution Intelligence Metrics ({timeframe.title()}) ==="]
            formatted_output.append(f"Period: Last {days} days")
            formatted_output.append(f"User: {uid} via {client_name}")
            formatted_output.append(f"Generated: {datetime.datetime.now(datetime.UTC).isoformat()}")
            
            if tracker_stats.get('error'):
                formatted_output.append(f"\nError retrieving statistics: {tracker_stats['error']}")
                return "\n".join(formatted_output)
            
            # Learning Efficiency Section
            formatted_output.append("\n--- Learning Efficiency ---")
            efficiency = tracker_stats.get('learning_efficiency', 0.0)
            formatted_output.append(f"Learning Efficiency: {efficiency:.1%}")
            
            if efficiency > 0.7:
                formatted_output.append("🟢 Excellent - High memory optimization")
            elif efficiency > 0.4:
                formatted_output.append("🟡 Good - Moderate memory optimization")
            elif efficiency > 0.1:
                formatted_output.append("🟠 Fair - Some memory optimization")
            else:
                formatted_output.append("🔴 Low - Minimal memory optimization")
            
            # Operation Breakdown
            formatted_output.append("\n--- Operation Breakdown ---")
            total_ops = tracker_stats.get('total_operations', 0)
            formatted_output.append(f"Total Operations: {total_ops}")
            
            if total_ops > 0:
                op_counts = tracker_stats.get('operation_counts', {})
                formatted_output.append(f"  • ADD: {op_counts.get('ADD', 0)} ({op_counts.get('ADD', 0)/total_ops:.1%})")
                formatted_output.append(f"  • UPDATE: {op_counts.get('UPDATE', 0)} ({op_counts.get('UPDATE', 0)/total_ops:.1%})")
                formatted_output.append(f"  • DELETE: {op_counts.get('DELETE', 0)} ({op_counts.get('DELETE', 0)/total_ops:.1%})")
                formatted_output.append(f"  • NOOP: {op_counts.get('NOOP', 0)} ({op_counts.get('NOOP', 0)/total_ops:.1%})")
            else:
                formatted_output.append("No operations recorded in this period")
            
            # Quality Metrics
            formatted_output.append("\n--- Quality Metrics ---")
            avg_confidence = tracker_stats.get('average_confidence', 0.0)
            avg_similarity = tracker_stats.get('average_similarity', 0.0)
            
            formatted_output.append(f"Average Confidence: {avg_confidence:.1%}")
            formatted_output.append(f"Average Similarity: {avg_similarity:.1%}")
            
            # Trend Analysis
            if trend_analysis:
                formatted_output.append("\n--- Trend Analysis ---")
                formatted_output.append(f"Quality Score: {trend_analysis.quality_score:.1f}/100")
                
                eff_trend = trend_analysis.efficiency_trend
                conf_trend = trend_analysis.confidence_trend
                vol_trend = trend_analysis.operation_volume_trend
                
                formatted_output.append(f"Efficiency Trend: {eff_trend:+.1f}%")
                formatted_output.append(f"Confidence Trend: {conf_trend:+.1f}%")
                formatted_output.append(f"Volume Trend: {vol_trend:+.1f}%")
                
                # Trend interpretation
                if eff_trend > 5:
                    formatted_output.append("📈 Efficiency is improving")
                elif eff_trend < -5:
                    formatted_output.append("📉 Efficiency is declining")
                else:
                    formatted_output.append("📊 Efficiency is stable")
            
            # Performance Insights
            formatted_output.append("\n--- Performance Insights ---")
            metrics = tracker_stats.get('metrics', {})
            
            if total_ops > 10:
                add_rate = metrics.get('add_rate', 0)
                update_rate = metrics.get('update_rate', 0)
                delete_rate = metrics.get('delete_rate', 0)
                noop_rate = metrics.get('noop_rate', 0)
                
                if add_rate > 0.8:
                    formatted_output.append("💡 High new information detection")
                if update_rate > 0.3:
                    formatted_output.append("🔄 Active memory refinement")
                if delete_rate > 0.1:
                    formatted_output.append("🧹 Good outdated information cleanup")
                if noop_rate > 0.5:
                    formatted_output.append("⚠️ High redundancy detected")
                
                # Recommendations
                formatted_output.append("\n--- Recommendations ---")
                if efficiency < 0.2:
                    formatted_output.append("• Consider providing more varied information")
                if avg_confidence < 0.6:
                    formatted_output.append("• Information could be more specific")
                if noop_rate > 0.7:
                    formatted_output.append("• Try asking about new topics")
            else:
                formatted_output.append("Insufficient data for insights (need >10 operations)")
            
            # Raw JSON for programmatic access
            formatted_output.append("\n--- Raw JSON Data ---")
            json_data = {
                "timeframe": timeframe,
                "period_days": days,
                "statistics": tracker_stats,
                "trend_analysis": {
                    "quality_score": trend_analysis.quality_score if trend_analysis else 0,
                    "efficiency_trend": trend_analysis.efficiency_trend if trend_analysis else 0,
                    "confidence_trend": trend_analysis.confidence_trend if trend_analysis else 0,
                    "volume_trend": trend_analysis.operation_volume_trend if trend_analysis else 0
                } if trend_analysis else None,
                "generated_at": datetime.datetime.now(datetime.UTC).isoformat()
            }
            formatted_output.append(json.dumps(json_data, indent=2))
            
            return "\n".join(formatted_output)
            
        finally:
            db.close()
            
    except Exception as e:
        logging.exception(f"Error getting evolution metrics: {e}")
        return f"Error getting evolution metrics: {e}"


@mcp.tool(description="Get personalized learning insights and memory evolution analysis for user-specific patterns")
async def get_learning_insights() -> str:
    """
    Get comprehensive learning insights including personal learning efficiency,
    memory categories distribution, recent evolution activity, and actionable recommendations.
    
    Returns:
        Formatted learning insights with personalized analysis and recommendations.
    """
    uid = user_id_var.get(None)
    client_name = client_name_var.get(None)
    
    if not uid:
        return "Error: user_id not provided"
    if not client_name:
        return "Error: client_name not provided"
    
    try:
        from app.evolution.insights_aggregator import EvolutionInsightsAggregator
        from app.evolution.evolution_tracker import get_evolution_tracker
        from app.database import SessionLocal
        from app.models import Memory, Category, memory_categories
        from sqlalchemy import func, desc, text
        
        db = SessionLocal()
        try:
            # Get user ID from database
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
            user_uuid = str(user.id)
            
            # Initialize services
            aggregator = EvolutionInsightsAggregator(db)
            tracker = get_evolution_tracker(db)
            
            formatted_output = [f"=== Personal Learning Insights ==="]
            formatted_output.append(f"User: {uid} via {client_name}")
            formatted_output.append(f"Generated: {datetime.datetime.now(datetime.UTC).isoformat()}")
            
            # Get recent evolution activity (last 30 days)
            recent_stats = await tracker.get_operation_statistics(user_uuid, 30)
            
            # Learning Efficiency Analysis
            formatted_output.append("\n--- Learning Efficiency Analysis ---")
            efficiency = recent_stats.get('learning_efficiency', 0.0)
            total_ops = recent_stats.get('total_operations', 0)
            
            if total_ops > 0:
                formatted_output.append(f"Personal Learning Efficiency: {efficiency:.1%}")
                formatted_output.append(f"Total Learning Operations: {total_ops}")
                
                # Learning style analysis
                op_counts = recent_stats.get('operation_counts', {})
                add_rate = op_counts.get('ADD', 0) / total_ops
                update_rate = op_counts.get('UPDATE', 0) / total_ops
                delete_rate = op_counts.get('DELETE', 0) / total_ops
                
                learning_style = []
                if add_rate > 0.6:
                    learning_style.append("🌱 Explorer - High new information absorption")
                if update_rate > 0.3:
                    learning_style.append("🔄 Refiner - Active knowledge evolution")
                if delete_rate > 0.1:
                    learning_style.append("🧹 Curator - Good information management")
                if efficiency > 0.5:
                    learning_style.append("⚡ Optimizer - Efficient memory usage")
                
                if learning_style:
                    formatted_output.append("\nYour Learning Style:")
                    for style in learning_style:
                        formatted_output.append(f"  {style}")
                else:
                    formatted_output.append("Learning style developing...")
            else:
                formatted_output.append("No recent learning activity to analyze")
            
            # Memory Categories Distribution
            formatted_output.append("\n--- Memory Categories Analysis ---")
            
            # Get memory categories distribution
            category_query = db.query(
                Category.name,
                func.count(memory_categories.c.memory_id).label('memory_count')
            ).join(
                memory_categories, Category.id == memory_categories.c.category_id
            ).join(
                Memory, Memory.id == memory_categories.c.memory_id
            ).filter(
                Memory.user_id == user.id,
                Memory.app_id == app.id,
                Memory.state == 'active'
            ).group_by(
                Category.name
            ).order_by(
                desc('memory_count')
            ).limit(10).all()
            
            if category_query:
                total_categorized = sum([cat.memory_count for cat in category_query])
                formatted_output.append(f"Total Categorized Memories: {total_categorized}")
                formatted_output.append("\nTop Memory Categories:")
                
                for category in category_query:
                    percentage = (category.memory_count / total_categorized * 100) if total_categorized > 0 else 0
                    formatted_output.append(f"  📂 {category.name}: {category.memory_count} ({percentage:.1f}%)")
                
                # Dominant categories analysis
                top_category = category_query[0]
                if top_category.memory_count / total_categorized > 0.4:
                    formatted_output.append(f"\n💡 You have a strong focus on {top_category.name}")
                elif len(category_query) >= 3 and category_query[2].memory_count / total_categorized > 0.15:
                    formatted_output.append("\n🎯 You have diverse interests across multiple categories")
            else:
                formatted_output.append("No categorized memories found")
            
            # Recent Evolution Activity
            formatted_output.append("\n--- Recent Evolution Activity ---")
            
            # Get recent operations audit trail
            recent_operations = await tracker.get_operation_audit_trail(
                user_id=user_uuid,
                start_date=datetime.datetime.now(datetime.UTC) - datetime.timedelta(days=7),
                limit=10
            )
            
            if recent_operations:
                formatted_output.append(f"Recent Operations (Last 7 days): {len(recent_operations)}")
                
                # Group by operation type
                op_summary = {}
                for op in recent_operations:
                    op_type = op['operation_type']
                    op_summary[op_type] = op_summary.get(op_type, 0) + 1
                
                for op_type, count in op_summary.items():
                    emoji = {"ADD": "➕", "UPDATE": "🔄", "DELETE": "🗑️", "NOOP": "⏸️"}.get(op_type, "🔧")
                    formatted_output.append(f"  {emoji} {op_type}: {count}")
                
                # Recent evolution examples
                formatted_output.append("\nRecent Learning Examples:")
                for i, op in enumerate(recent_operations[:3]):
                    op_time = datetime.datetime.fromisoformat(op['created_at'].replace('Z', '+00:00'))
                    time_ago = datetime.datetime.now(datetime.UTC) - op_time
                    
                    if time_ago.days > 0:
                        time_str = f"{time_ago.days}d ago"
                    elif time_ago.seconds > 3600:
                        time_str = f"{time_ago.seconds//3600}h ago"
                    else:
                        time_str = f"{time_ago.seconds//60}m ago"
                    
                    fact_preview = op['candidate_fact'][:60] + "..." if len(op['candidate_fact']) > 60 else op['candidate_fact']
                    formatted_output.append(f"  {i+1}. {op['operation_type']}: {fact_preview} ({time_str})")
            else:
                formatted_output.append("No recent evolution activity")
            
            # Conflict Resolution History
            formatted_output.append("\n--- Conflict Resolution History ---")
            
            # Count conflicts in recent operations
            conflicts = [op for op in recent_operations if op.get('metadata', {}).get('conflict_detected')]
            conflict_count = len(conflicts)
            
            if conflict_count > 0:
                formatted_output.append(f"Recent Conflicts Resolved: {conflict_count}")
                formatted_output.append("Conflict resolution helps refine your knowledge base")
                
                # Show confidence in conflict resolution
                conf_scores = [op.get('confidence_score', 0) for op in conflicts if op.get('confidence_score')]
                if conf_scores:
                    avg_conf = sum(conf_scores) / len(conf_scores)
                    formatted_output.append(f"Average Resolution Confidence: {avg_conf:.1%}")
            else:
                formatted_output.append("No recent conflicts detected")
                formatted_output.append("Your information is consistently coherent")
            
            # Personalized Recommendations
            formatted_output.append("\n--- Personalized Recommendations ---")
            
            recommendations = []
            
            if total_ops > 0:
                if efficiency < 0.3:
                    recommendations.append("💡 Try asking about topics you haven't explored recently")
                if recent_stats.get('average_confidence', 0) < 0.7:
                    recommendations.append("🎯 Provide more specific details when sharing information")
                if op_counts.get('NOOP', 0) / total_ops > 0.6:
                    recommendations.append("🔍 Explore new areas of interest to expand your knowledge base")
                if len(category_query) < 3:
                    recommendations.append("🌈 Diversify your topics to build a well-rounded knowledge base")
                
                # Positive reinforcements
                if efficiency > 0.6:
                    recommendations.append("⭐ Excellent memory optimization! Keep up the great learning")
                if recent_stats.get('average_confidence', 0) > 0.8:
                    recommendations.append("🎉 Your information is highly confident and well-structured")
            else:
                recommendations.append("🚀 Start your learning journey by asking questions or sharing information!")
            
            if recommendations:
                for rec in recommendations:
                    formatted_output.append(f"  {rec}")
            else:
                formatted_output.append("  🎯 You're learning efficiently! Keep exploring new topics")
            
            # Learning Progress Summary
            formatted_output.append("\n--- Learning Progress Summary ---")
            
            # Get trend data for progress analysis
            trend_analysis = await aggregator.analyze_trends(user_uuid, 30)
            
            if trend_analysis:
                quality_score = trend_analysis.quality_score
                formatted_output.append(f"Overall Learning Quality: {quality_score:.1f}/100")
                
                if quality_score >= 80:
                    formatted_output.append("🏆 Outstanding learning performance!")
                elif quality_score >= 60:
                    formatted_output.append("📈 Strong learning progress!")
                elif quality_score >= 40:
                    formatted_output.append("📊 Good learning foundation!")
                else:
                    formatted_output.append("🌱 Building your learning foundation...")
                
                # Trend indicators
                if trend_analysis.efficiency_trend > 5:
                    formatted_output.append("📈 Your learning efficiency is improving")
                elif trend_analysis.efficiency_trend < -5:
                    formatted_output.append("📉 Consider varying your learning approaches")
                
                if trend_analysis.confidence_trend > 5:
                    formatted_output.append("🎯 Your information quality is increasing")
            
            # Raw JSON for programmatic access
            formatted_output.append("\n--- Raw JSON Data ---")
            json_data = {
                "user_id": uid,
                "learning_efficiency": efficiency,
                "total_operations": total_ops,
                "operation_breakdown": recent_stats.get('operation_counts', {}),
                "memory_categories": [
                    {"name": cat.name, "count": cat.memory_count} 
                    for cat in category_query
                ] if category_query else [],
                "recent_activity": {
                    "operations_count": len(recent_operations),
                    "conflicts_resolved": conflict_count,
                    "average_confidence": recent_stats.get('average_confidence', 0)
                },
                "quality_metrics": {
                    "quality_score": trend_analysis.quality_score if trend_analysis else 0,
                    "efficiency_trend": trend_analysis.efficiency_trend if trend_analysis else 0,
                    "confidence_trend": trend_analysis.confidence_trend if trend_analysis else 0
                } if trend_analysis else None,
                "generated_at": datetime.datetime.now(datetime.UTC).isoformat()
            }
            formatted_output.append(json.dumps(json_data, indent=2))
            
            return "\n".join(formatted_output)
            
        finally:
            db.close()
            
    except Exception as e:
        logging.exception(f"Error getting learning insights: {e}")
        return f"Error getting learning insights: {e}"


@mcp.tool(description="Monitor real-time evolution activity and system intelligence metrics with live updates")
async def monitor_evolution() -> str:
    """
    Monitor real-time evolution activity including recent operations, active learning patterns,
    memory quality indicators, and system intelligence metrics.
    
    Returns:
        Real-time evolution monitoring dashboard with system health and activity metrics.
    """
    uid = user_id_var.get(None)
    client_name = client_name_var.get(None)
    
    if not uid:
        return "Error: user_id not provided"
    if not client_name:
        return "Error: client_name not provided"
    
    try:
        from app.evolution.insights_aggregator import EvolutionInsightsAggregator
        from app.evolution.evolution_tracker import get_evolution_tracker
        from app.evolution.memory_integration import get_memory_evolution_integration
        from app.database import SessionLocal
        from sqlalchemy import text, func
        
        db = SessionLocal()
        try:
            # Get user ID from database
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
            user_uuid = str(user.id)
            
            # Initialize services
            tracker = get_evolution_tracker(db)
            integration = get_memory_evolution_integration(db)
            
            current_time = datetime.datetime.now(datetime.UTC)
            formatted_output = [f"=== Evolution Activity Monitor ==="]
            formatted_output.append(f"User: {uid} via {client_name}")
            formatted_output.append(f"Monitor Time: {current_time.isoformat()}")
            formatted_output.append(f"Refresh: Run this command again for updated data")
            
            # System Health Check
            formatted_output.append("\n--- System Health Status ---")
            
            try:
                system_health = await integration.health_check()
                status = system_health.get('status', 'unknown')
                
                status_emoji = {
                    'healthy': '🟢',
                    'warning': '🟡', 
                    'unhealthy': '🔴',
                    'unknown': '⚫'
                }.get(status, '⚫')
                
                formatted_output.append(f"Evolution System: {status_emoji} {status.upper()}")
                formatted_output.append(f"Feature Flag: {'✅ Enabled' if system_health.get('feature_flag_enabled') else '❌ Disabled'}")
                
                if system_health.get('evolution_service'):
                    evo_status = system_health['evolution_service'].get('status', 'unknown')
                    formatted_output.append(f"Evolution Engine: {evo_status.upper()}")
                
            except Exception as health_error:
                formatted_output.append(f"❌ Health check failed: {health_error}")
            
            # Real-time Activity (Last 1 hour)
            formatted_output.append("\n--- Real-time Activity (Last Hour) ---")
            
            one_hour_ago = current_time - datetime.timedelta(hours=1)
            recent_ops = await tracker.get_operation_audit_trail(
                user_id=user_uuid,
                start_date=one_hour_ago,
                limit=20
            )
            
            if recent_ops:
                formatted_output.append(f"Recent Operations: {len(recent_ops)}")
                
                # Real-time operation summary
                op_counts = {}
                latest_op_time = None
                
                for op in recent_ops:
                    op_type = op['operation_type']
                    op_counts[op_type] = op_counts.get(op_type, 0) + 1
                    
                    if op.get('created_at'):
                        op_time = datetime.datetime.fromisoformat(op['created_at'].replace('Z', '+00:00'))
                        if latest_op_time is None or op_time > latest_op_time:
                            latest_op_time = op_time
                
                for op_type, count in op_counts.items():
                    emoji = {"ADD": "➕", "UPDATE": "🔄", "DELETE": "🗑️", "NOOP": "⏸️"}.get(op_type, "🔧")
                    formatted_output.append(f"  {emoji} {op_type}: {count}")
                
                if latest_op_time:
                    time_since = current_time - latest_op_time
                    if time_since.seconds < 60:
                        time_str = f"{time_since.seconds}s ago"
                    else:
                        time_str = f"{time_since.seconds//60}m ago"
                    formatted_output.append(f"Last Activity: {time_str}")
                
            else:
                formatted_output.append("No recent activity")
            
            # Active Learning Patterns (Last 24 hours)
            formatted_output.append("\n--- Active Learning Patterns (24h) ---")
            
            daily_stats = await tracker.get_operation_statistics(user_uuid, 1)
            
            if daily_stats.get('total_operations', 0) > 0:
                total_ops = daily_stats['total_operations']
                efficiency = daily_stats.get('learning_efficiency', 0)
                
                formatted_output.append(f"Learning Activity: {total_ops} operations")
                formatted_output.append(f"Learning Efficiency: {efficiency:.1%}")
                
                # Activity patterns
                hourly_activity = await self._get_hourly_activity_pattern(db, user_uuid)
                if hourly_activity:
                    peak_hour = max(hourly_activity.items(), key=lambda x: x[1])
                    formatted_output.append(f"Peak Activity: {peak_hour[0]}:00 ({peak_hour[1]} ops)")
                
                # Learning velocity
                op_counts = daily_stats.get('operation_counts', {})
                velocity_indicators = []
                if op_counts.get('ADD', 0) > op_counts.get('UPDATE', 0):
                    velocity_indicators.append("🚀 High information absorption")
                if op_counts.get('UPDATE', 0) > 0:
                    velocity_indicators.append("🔄 Active knowledge refinement")
                if efficiency > 0.5:
                    velocity_indicators.append("⚡ Efficient learning")
                
                if velocity_indicators:
                    formatted_output.append("Learning Velocity:")
                    for indicator in velocity_indicators:
                        formatted_output.append(f"  {indicator}")
            else:
                formatted_output.append("No learning activity today")
            
            # Memory Quality Indicators
            formatted_output.append("\n--- Memory Quality Indicators ---")
            
            # Get quality metrics from recent operations
            quality_metrics = await self._calculate_quality_indicators(db, user_uuid, recent_ops)
            
            confidence_avg = quality_metrics.get('confidence_avg', 0)
            similarity_avg = quality_metrics.get('similarity_avg', 0)
            conflict_rate = quality_metrics.get('conflict_rate', 0)
            
            formatted_output.append(f"Information Confidence: {confidence_avg:.1%}")
            formatted_output.append(f"Memory Similarity: {similarity_avg:.1%}")
            formatted_output.append(f"Conflict Rate: {conflict_rate:.1%}")
            
            # Quality trend indicators
            quality_indicators = []
            if confidence_avg > 0.8:
                quality_indicators.append("🎯 High confidence information")
            if similarity_avg > 0.7:
                quality_indicators.append("🔗 Strong memory connections")
            if conflict_rate < 0.1:
                quality_indicators.append("✅ Low conflict rate")
            if conflict_rate > 0.3:
                quality_indicators.append("⚠️ High conflict detection")
            
            if quality_indicators:
                for indicator in quality_indicators:
                    formatted_output.append(f"  {indicator}")
            
            # System Intelligence Metrics
            formatted_output.append("\n--- System Intelligence Metrics ---")
            
            # Get system-wide statistics (if user has access)
            system_stats = await self._get_system_intelligence_metrics(db)
            
            formatted_output.append(f"System Learning Efficiency: {system_stats.get('global_efficiency', 0):.1%}")
            formatted_output.append(f"Active Learning Users: {system_stats.get('active_users', 0)}")
            formatted_output.append(f"Daily Operations: {system_stats.get('daily_operations', 0)}")
            
            # Alerts and Anomalies
            formatted_output.append("\n--- Alerts & Anomalies ---")
            
            alerts = []
            
            # Check for unusual patterns
            if recent_ops and len(recent_ops) > 10:
                noop_rate = sum(1 for op in recent_ops if op['operation_type'] == 'NOOP') / len(recent_ops)
                if noop_rate > 0.8:
                    alerts.append("⚠️ High redundancy detected in recent operations")
            
            if confidence_avg < 0.5 and len(recent_ops) > 5:
                alerts.append("⚠️ Low confidence in recent information")
            
            if conflict_rate > 0.4:
                alerts.append("🔍 High conflict rate - knowledge inconsistencies detected")
            
            if daily_stats.get('total_operations', 0) == 0:
                alerts.append("💡 No learning activity today - try asking questions!")
            
            if alerts:
                for alert in alerts:
                    formatted_output.append(f"  {alert}")
            else:
                formatted_output.append("  ✅ No alerts - system operating normally")
            
            # Live Update Instructions
            formatted_output.append("\n--- Live Monitoring ---")
            formatted_output.append("🔄 Run 'monitor_evolution' again for real-time updates")
            formatted_output.append("📊 Use 'get_evolution_metrics' for detailed historical analysis")
            formatted_output.append("🎯 Use 'get_learning_insights' for personalized recommendations")
            
            # Raw JSON for programmatic access
            formatted_output.append("\n--- Raw JSON Data ---")
            json_data = {
                "monitor_timestamp": current_time.isoformat(),
                "user_id": uid,
                "system_health": {
                    "status": system_health.get('status', 'unknown') if 'system_health' in locals() else 'unknown',
                    "feature_enabled": system_health.get('feature_flag_enabled', False) if 'system_health' in locals() else False
                },
                "realtime_activity": {
                    "recent_operations_count": len(recent_ops),
                    "operation_breakdown": op_counts if 'op_counts' in locals() else {},
                    "last_activity_minutes_ago": (current_time - latest_op_time).seconds // 60 if 'latest_op_time' in locals() and latest_op_time else None
                },
                "quality_indicators": {
                    "confidence_avg": confidence_avg,
                    "similarity_avg": similarity_avg,
                    "conflict_rate": conflict_rate
                },
                "daily_stats": daily_stats,
                "alerts": alerts,
                "refresh_instructions": "Call monitor_evolution() again for updated data"
            }
            formatted_output.append(json.dumps(json_data, indent=2))
            
            return "\n".join(formatted_output)
            
        finally:
            db.close()
            
    except Exception as e:
        logging.exception(f"Error monitoring evolution: {e}")
        return f"Error monitoring evolution: {e}"


async def _get_hourly_activity_pattern(db: Session, user_id: str) -> Dict[int, int]:
    """Get hourly activity pattern for the last 24 hours."""
    try:
        query = text("""
            SELECT EXTRACT(HOUR FROM created_at) as hour, COUNT(*) as operations
            FROM memory_master.evolution_operations
            WHERE user_id = :user_id 
                AND created_at >= NOW() - INTERVAL '24 hours'
            GROUP BY EXTRACT(HOUR FROM created_at)
            ORDER BY hour
        """)
        
        result = db.execute(query, {"user_id": user_id})
        return {int(row.hour): row.operations for row in result.fetchall()}
    except Exception:
        return {}


async def _calculate_quality_indicators(db: Session, user_id: str, recent_ops: List[Dict]) -> Dict[str, float]:
    """Calculate quality indicators from recent operations."""
    if not recent_ops:
        return {"confidence_avg": 0, "similarity_avg": 0, "conflict_rate": 0}
    
    confidences = [op.get('confidence_score', 0) for op in recent_ops if op.get('confidence_score')]
    similarities = [op.get('similarity_score', 0) for op in recent_ops if op.get('similarity_score')]
    conflicts = sum(1 for op in recent_ops if op.get('metadata', {}).get('conflict_detected'))
    
    return {
        "confidence_avg": sum(confidences) / len(confidences) if confidences else 0,
        "similarity_avg": sum(similarities) / len(similarities) if similarities else 0,
        "conflict_rate": conflicts / len(recent_ops)
    }


async def _get_system_intelligence_metrics(db: Session) -> Dict[str, Any]:
    """Get system-wide intelligence metrics."""
    try:
        # Get today's system-wide stats
        today = datetime.date.today()
        
        query = text("""
            SELECT 
                COUNT(DISTINCT user_id) as active_users,
                SUM(total_operations) as daily_operations,
                AVG(learning_efficiency) as global_efficiency
            FROM memory_master.evolution_insights
            WHERE date = :today
        """)
        
        result = db.execute(query, {"today": today}).fetchone()
        
        if result:
            return {
                "active_users": result.active_users or 0,
                "daily_operations": result.daily_operations or 0,
                "global_efficiency": result.global_efficiency or 0
            }
    except Exception:
        pass
    
    return {"active_users": 0, "daily_operations": 0, "global_efficiency": 0}


# SECURITY: delete_all_memories tool has been REMOVED for safety
# This dangerous operation is now only available through the secure UI with password protection
# MCP clients should not have the ability to bulk delete all memories


@mcp_router.get("/claude/sse/{user_id}")
async def handle_claude_sse(request: Request):
    """Handle SSE connections for Claude Desktop specifically"""
    # Extract user_id from path parameters
    uid = request.path_params.get("user_id")
    client_name = "claude"  # Fixed client name for Claude Desktop
    session_key = f"{uid}:{client_name}"
    
    logging.info(f"[MCP_DIAGNOSTIC] Claude Desktop SSE connection initiated for user: {uid}, session: {session_key}")
    
    # Pre-register user and app in database to ensure they exist
    try:
        db = SessionLocal()
        try:
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
            logging.info(f"[MCP_DIAGNOSTIC] Pre-registered user '{uid}' and app '{client_name}' for Claude Desktop SSE connection")
        finally:
            db.close()
    except Exception as e:
        logging.error(f"[MCP_DIAGNOSTIC] Failed to pre-register user/app for Claude Desktop: {e}")
    
    user_token = user_id_var.set(uid or "")
    client_token = client_name_var.set(client_name)

    try:
        # Handle SSE connection with proper initialization sequence for Claude Desktop
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request._send,
        ) as (read_stream, write_stream):
            # Mark initialization as starting
            _initialization_complete[session_key] = False
            logging.info(f"[MCP_DIAGNOSTIC] Starting Claude Desktop initialization for session {session_key}")
            
            # Create a custom initialization handler
            class ClaudeInitializationServer:
                def __init__(self, server):
                    self._server = server
                    
                async def run(self, read_stream, write_stream, init_options):
                    try:
                        # Shorter wait for Claude Desktop
                        import asyncio
                        await asyncio.sleep(0.3)
                        
                        # Mark as initialized before starting
                        _initialization_complete[session_key] = True
                        logging.info(f"[MCP_DIAGNOSTIC] Claude Desktop initialization complete for session {session_key}")
                        
                        # Run the actual server
                        await self._server.run(read_stream, write_stream, init_options)
                    except Exception as e:
                        logging.error(f"[MCP_DIAGNOSTIC] Claude Desktop server run error for {session_key}: {e}")
                        _initialization_complete.pop(session_key, None)
                        raise
            
            aware_server = ClaudeInitializationServer(mcp._mcp_server)
            await aware_server.run(
                read_stream,
                write_stream,
                mcp._mcp_server.create_initialization_options(),
            )
    except Exception as e:
        logging.error(f"[MCP_DIAGNOSTIC] Claude Desktop SSE connection error for {session_key}: {e}")
        _initialization_complete.pop(session_key, None)
        raise
    finally:
        # Clean up context variables and session tracking
        user_id_var.reset(user_token)
        client_name_var.reset(client_token)
        _initialization_complete.pop(session_key, None)
        logging.info(f"[MCP_DIAGNOSTIC] Claude Desktop session cleanup complete for {session_key}")


@mcp_router.get("/{client_name}/sse/{user_id}")
async def handle_sse(request: Request):
    """Handle SSE connections for a specific user and client with improved initialization"""
    # Extract user_id and client_name from path parameters
    uid = request.path_params.get("user_id")
    client_name = request.path_params.get("client_name")
    session_key = f"{uid}:{client_name}"
    
    logging.info(f"[MCP_DIAGNOSTIC] Generic SSE connection initiated for client: {client_name}, user: {uid}, session: {session_key}")
    
    # Pre-register user and app in database to ensure they exist
    try:
        db = SessionLocal()
        try:
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
            logging.info(f"Pre-registered user '{uid}' and app '{client_name}' for SSE connection")
        finally:
            db.close()
    except Exception as e:
        logging.error(f"Failed to pre-register user/app: {e}")
    
    user_token = user_id_var.set(uid or "")
    client_token = client_name_var.set(client_name or "")

    try:
        # Handle SSE connection with proper initialization sequence
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request._send,
        ) as (read_stream, write_stream):
            # Mark initialization as starting
            _initialization_complete[session_key] = False
            
            # Create a custom initialization handler
            class InitializationAwareServer:
                def __init__(self, server):
                    self._server = server
                    
                async def run(self, read_stream, write_stream, init_options):
                    try:
                        # Wait a bit to ensure proper initialization sequence
                        import asyncio
                        await asyncio.sleep(0.2)
                        
                        # CRITICAL FIX: Do NOT mark as initialized until AFTER server.run() is ready
                        # This fixes the race condition with roo-cline requests
                        logging.info(f"Starting server initialization for session {session_key}")
                        
                        # Create a custom server wrapper that delays initialization marking
                        class DelayedInitServer:
                            def __init__(self, server, session_key):
                                self._server = server
                                self._session_key = session_key
                                
                            async def run(self, read_stream, write_stream, init_options):
                                # First establish the connection
                                await self._server.run(read_stream, write_stream, init_options)
                        
                        # Use delayed init server instead of direct run
                        delayed_server = DelayedInitServer(self._server, session_key)
                        
                        # Mark as initialized ONLY after we're about to start handling requests
                        _initialization_complete[session_key] = True
                        logging.info(f"Initialization complete for session {session_key}")
                        
                        await delayed_server.run(read_stream, write_stream, init_options)
                        
                    except Exception as e:
                        logging.error(f"Server run error for {session_key}: {e}")
                        _initialization_complete.pop(session_key, None)
                        raise
            
            aware_server = InitializationAwareServer(mcp._mcp_server)
            await aware_server.run(
                read_stream,
                write_stream,
                mcp._mcp_server.create_initialization_options(),
            )
    except Exception as e:
        logging.error(f"SSE connection error for {session_key}: {e}")
        _initialization_complete.pop(session_key, None)
        raise
    finally:
        # Clean up context variables and session tracking
        user_id_var.reset(user_token)
        client_name_var.reset(client_token)
        _initialization_complete.pop(session_key, None)


@mcp_router.get("/roocline/sse/{user_id}")
async def handle_roocline_sse(request: Request):
    """Handle SSE connections for roo-cline specifically"""
    # Extract user_id from path parameters
    uid = request.path_params.get("user_id")
    client_name = "roo-cline"  # Fixed client name for roo-cline
    session_key = f"{uid}:{client_name}"
    
    # Pre-register user and app in database to ensure they exist
    try:
        db = SessionLocal()
        try:
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
            logging.info(f"Pre-registered user '{uid}' and app '{client_name}' for roo-cline SSE connection")
        finally:
            db.close()
    except Exception as e:
        logging.error(f"Failed to pre-register user/app for roo-cline: {e}")
    
    user_token = user_id_var.set(uid or "")
    client_token = client_name_var.set(client_name)

    try:
        # Handle SSE connection with longer initialization delay for roo-cline
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request._send,
        ) as (read_stream, write_stream):
            # Mark initialization as starting
            _initialization_complete[session_key] = False
            
            # Create a custom initialization handler with longer delay
            class RoolineInitializationServer:
                def __init__(self, server):
                    self._server = server
                    
                async def run(self, read_stream, write_stream, init_options):
                    try:
                        # CRITICAL FIX for roo-cline race condition
                        import asyncio
                        await asyncio.sleep(0.5)
                        
                        logging.info(f"Starting roo-cline server initialization for session {session_key}")
                        
                        # FIXED: Mark as initialized AFTER server is ready to handle requests
                        # This prevents the "Received request before initialization was complete" error
                        
                        # Create a task to run the server
                        async def run_server():
                            await self._server.run(read_stream, write_stream, init_options)
                        
                        # Mark as initialized just before we start the server task
                        _initialization_complete[session_key] = True
                        logging.info(f"Roo-cline initialization complete for session {session_key}")
                        
                        # Now run the server
                        await run_server()
                        
                    except Exception as e:
                        logging.error(f"Roo-cline server run error for {session_key}: {e}")
                        _initialization_complete.pop(session_key, None)
                        raise
            
            aware_server = RoolineInitializationServer(mcp._mcp_server)
            await aware_server.run(
                read_stream,
                write_stream,
                mcp._mcp_server.create_initialization_options(),
            )
    except Exception as e:
        logging.error(f"Roo-cline SSE connection error for {session_key}: {e}")
        _initialization_complete.pop(session_key, None)
        raise
    finally:
        # Clean up context variables and session tracking
        user_id_var.reset(user_token)
        client_name_var.reset(client_token)
        _initialization_complete.pop(session_key, None)


@mcp_router.get("/cline/sse/{user_id}")
async def handle_cline_sse(request: Request):
    """Handle SSE connections for cline specifically"""
    # Extract user_id from path parameters
    uid = request.path_params.get("user_id")
    client_name = "cline"  # Fixed client name for cline
    session_key = f"{uid}:{client_name}"
    
    # Pre-register user and app in database to ensure they exist
    try:
        db = SessionLocal()
        try:
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
            logging.info(f"Pre-registered user '{uid}' and app '{client_name}' for cline SSE connection")
        finally:
            db.close()
    except Exception as e:
        logging.error(f"Failed to pre-register user/app for cline: {e}")
    
    user_token = user_id_var.set(uid or "")
    client_token = client_name_var.set(client_name)

    try:
        # Handle SSE connection with longer initialization delay for cline
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request._send,
        ) as (read_stream, write_stream):
            # Mark initialization as starting
            _initialization_complete[session_key] = False
            
            # Create a custom initialization handler with longer delay
            class ClineInitializationServer:
                def __init__(self, server):
                    self._server = server
                    
                async def run(self, read_stream, write_stream, init_options):
                    try:
                        # Longer wait for cline to prevent race conditions
                        import asyncio
                        await asyncio.sleep(0.5)
                        
                        # Mark as initialized before starting
                        _initialization_complete[session_key] = True
                        logging.info(f"Cline initialization complete for session {session_key}")
                        
                        # Run the actual server
                        await self._server.run(read_stream, write_stream, init_options)
                    except Exception as e:
                        logging.error(f"Cline server run error for {session_key}: {e}")
                        _initialization_complete.pop(session_key, None)
                        raise
            
            aware_server = ClineInitializationServer(mcp._mcp_server)
            await aware_server.run(
                read_stream,
                write_stream,
                mcp._mcp_server.create_initialization_options(),
            )
    except Exception as e:
        logging.error(f"Cline SSE connection error for {session_key}: {e}")
        _initialization_complete.pop(session_key, None)
        raise
    finally:
        # Clean up context variables and session tracking
        user_id_var.reset(user_token)
        client_name_var.reset(client_token)
        _initialization_complete.pop(session_key, None)


@mcp_router.post("/messages/")
async def handle_get_message(request: Request):
    return await handle_post_message(request)


@mcp_router.post("/{client_name}/sse/{user_id}/messages/")
async def handle_post_message(request: Request):
    return await handle_post_message(request)

async def handle_post_message(request: Request):
    """Handle POST messages for SSE"""
    try:
        body = await request.body()

        # Create a simple receive function that returns the body
        async def receive():
            return {"type": "http.request", "body": body, "more_body": False}

        # Create a simple send function that does nothing
        async def send(message):
            return {}

        # Call handle_post_message with the correct arguments
        await sse.handle_post_message(request.scope, receive, send)

        # Return a success response
        return {"status": "ok"}
    finally:
        pass
        # Clean up context variable
        # client_name_var.reset(client_token)

def setup_mcp_server(app: FastAPI):
    """Setup MCP server with the FastAPI application"""
    mcp._mcp_server.name = f"mem0-mcp-server"

    # Include MCP router in the FastAPI app
    app.include_router(mcp_router)
