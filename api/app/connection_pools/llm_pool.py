"""
LLM API Connection Pool Manager
Provides connection pooling and session management for LLM API clients
"""
import asyncio
import logging
import time
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from threading import Lock
from typing import Dict, Any, Optional, List, AsyncGenerator, Union
from enum import Enum

import httpx
import openai
from anthropic import AsyncAnthropic

from app.config import get_settings

logger = logging.getLogger(__name__)


class LLMProvider(Enum):
    """Supported LLM providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"


@dataclass
class LLMClientMetrics:
    """Metrics for LLM client connections"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    avg_response_time: float = 0.0
    active_connections: int = 0
    connection_pool_hits: int = 0
    connection_pool_misses: int = 0
    rate_limit_hits: int = 0
    last_request_time: Optional[datetime] = None
    response_times: List[float] = field(default_factory=list)


@dataclass 
class PooledClient:
    """Pooled LLM client with metadata"""
    client: Union[openai.AsyncOpenAI, AsyncAnthropic]
    provider: LLMProvider
    created_at: datetime
    last_used: datetime
    request_count: int = 0
    is_active: bool = True


class LLMPoolManager:
    """Enhanced LLM API connection pool manager"""
    
    def __init__(self):
        self.settings = get_settings()
        self._clients: Dict[LLMProvider, List[PooledClient]] = {
            LLMProvider.OPENAI: [],
            LLMProvider.ANTHROPIC: []
        }
        self._client_lock = Lock()
        self._metrics: Dict[LLMProvider, LLMClientMetrics] = {
            LLMProvider.OPENAI: LLMClientMetrics(),
            LLMProvider.ANTHROPIC: LLMClientMetrics()
        }
        
        # HTTP client for connection pooling
        self._http_client = httpx.AsyncClient(
            limits=httpx.Limits(
                max_connections=self.settings.llm_max_connections,
                max_keepalive_connections=self.settings.llm_max_keepalive_connections,
                keepalive_expiry=self.settings.llm_keepalive_expiry
            ),
            timeout=httpx.Timeout(self.settings.llm_timeout)
        )
        
        logger.info(f"LLM pool manager initialized with {self.settings.llm_max_connections} max connections")
    
    def _create_openai_client(self, api_key: str) -> openai.AsyncOpenAI:
        """Create OpenAI client with connection pooling"""
        return openai.AsyncOpenAI(
            api_key=api_key,
            http_client=self._http_client,
            max_retries=self.settings.llm_retry_max_attempts,
            timeout=self.settings.llm_timeout
        )
    
    def _create_anthropic_client(self, api_key: str) -> AsyncAnthropic:
        """Create Anthropic client with connection pooling"""
        return AsyncAnthropic(
            api_key=api_key,
            http_client=self._http_client,
            max_retries=self.settings.llm_retry_max_attempts,
            timeout=self.settings.llm_timeout
        )
    
    def _get_or_create_client(self, provider: LLMProvider, api_key: str) -> PooledClient:
        """Get existing client from pool or create new one"""
        with self._client_lock:
            clients = self._clients[provider]
            
            # Find available client
            now = datetime.now()
            for client in clients:
                if (client.is_active and 
                    now - client.last_used < timedelta(seconds=self.settings.llm_keepalive_expiry)):
                    client.last_used = now
                    client.request_count += 1
                    self._metrics[provider].connection_pool_hits += 1
                    return client
            
            # Create new client if pool not full
            if len(clients) < self.settings.llm_max_connections:
                if provider == LLMProvider.OPENAI:
                    client = self._create_openai_client(api_key)
                elif provider == LLMProvider.ANTHROPIC:
                    client = self._create_anthropic_client(api_key)
                else:
                    raise ValueError(f"Unsupported provider: {provider}")
                
                pooled_client = PooledClient(
                    client=client,
                    provider=provider,
                    created_at=now,
                    last_used=now,
                    request_count=1
                )
                clients.append(pooled_client)
                self._metrics[provider].connection_pool_misses += 1
                logger.debug(f"Created new {provider.value} client in pool")
                return pooled_client
            
            # Pool is full, reuse least recently used client
            oldest_client = min(clients, key=lambda c: c.last_used)
            oldest_client.last_used = now
            oldest_client.request_count += 1
            self._metrics[provider].connection_pool_hits += 1
            return oldest_client
    
    @asynccontextmanager
    async def get_openai_client(self, api_key: str) -> AsyncGenerator[openai.AsyncOpenAI, None]:
        """Get OpenAI client from pool with context management"""
        if not api_key:
            raise ValueError("OpenAI API key is required")
        
        start_time = time.time()
        pooled_client = self._get_or_create_client(LLMProvider.OPENAI, api_key)
        
        try:
            with self._client_lock:
                self._metrics[LLMProvider.OPENAI].active_connections += 1
            
            yield pooled_client.client
            
            # Record successful request
            response_time = time.time() - start_time
            with self._client_lock:
                metrics = self._metrics[LLMProvider.OPENAI]
                metrics.successful_requests += 1
                metrics.total_requests += 1
                metrics.response_times.append(response_time)
                
                # Keep only last 100 response times for average calculation
                if len(metrics.response_times) > 100:
                    metrics.response_times = metrics.response_times[-100:]
                
                metrics.avg_response_time = sum(metrics.response_times) / len(metrics.response_times)
                metrics.last_request_time = datetime.now()
                
        except Exception as e:
            # Record failed request
            with self._client_lock:
                metrics = self._metrics[LLMProvider.OPENAI]
                metrics.failed_requests += 1
                metrics.total_requests += 1
                
                # Check for rate limiting
                if "rate_limit" in str(e).lower() or "429" in str(e):
                    metrics.rate_limit_hits += 1
                    
            logger.error(f"OpenAI client error: {e}")
            raise
        finally:
            with self._client_lock:
                self._metrics[LLMProvider.OPENAI].active_connections -= 1
    
    @asynccontextmanager
    async def get_anthropic_client(self, api_key: str) -> AsyncGenerator[AsyncAnthropic, None]:
        """Get Anthropic client from pool with context management"""
        if not api_key:
            raise ValueError("Anthropic API key is required")
        
        start_time = time.time()
        pooled_client = self._get_or_create_client(LLMProvider.ANTHROPIC, api_key)
        
        try:
            with self._client_lock:
                self._metrics[LLMProvider.ANTHROPIC].active_connections += 1
            
            yield pooled_client.client
            
            # Record successful request
            response_time = time.time() - start_time
            with self._client_lock:
                metrics = self._metrics[LLMProvider.ANTHROPIC]
                metrics.successful_requests += 1
                metrics.total_requests += 1
                metrics.response_times.append(response_time)
                
                # Keep only last 100 response times for average calculation
                if len(metrics.response_times) > 100:
                    metrics.response_times = metrics.response_times[-100:]
                
                metrics.avg_response_time = sum(metrics.response_times) / len(metrics.response_times)
                metrics.last_request_time = datetime.now()
                
        except Exception as e:
            # Record failed request
            with self._client_lock:
                metrics = self._metrics[LLMProvider.ANTHROPIC]
                metrics.failed_requests += 1
                metrics.total_requests += 1
                
                # Check for rate limiting
                if "rate_limit" in str(e).lower() or "429" in str(e):
                    metrics.rate_limit_hits += 1
                    
            logger.error(f"Anthropic client error: {e}")
            raise
        finally:
            with self._client_lock:
                self._metrics[LLMProvider.ANTHROPIC].active_connections -= 1
    
    def get_pool_metrics(self) -> Dict[str, Any]:
        """Get current pool metrics for all providers"""
        with self._client_lock:
            metrics = {}
            for provider, client_metrics in self._metrics.items():
                metrics[provider.value] = {
                    'total_requests': client_metrics.total_requests,
                    'successful_requests': client_metrics.successful_requests,
                    'failed_requests': client_metrics.failed_requests,
                    'success_rate': (
                        (client_metrics.successful_requests / client_metrics.total_requests * 100)
                        if client_metrics.total_requests > 0 else 0
                    ),
                    'avg_response_time_ms': round(client_metrics.avg_response_time * 1000, 2),
                    'active_connections': client_metrics.active_connections,
                    'pool_size': len(self._clients[provider]),
                    'connection_pool_hits': client_metrics.connection_pool_hits,
                    'connection_pool_misses': client_metrics.connection_pool_misses,
                    'pool_hit_rate': (
                        (client_metrics.connection_pool_hits / 
                         (client_metrics.connection_pool_hits + client_metrics.connection_pool_misses) * 100)
                        if (client_metrics.connection_pool_hits + client_metrics.connection_pool_misses) > 0 else 0
                    ),
                    'rate_limit_hits': client_metrics.rate_limit_hits,
                    'last_request_time': (
                        client_metrics.last_request_time.isoformat() 
                        if client_metrics.last_request_time else None
                    )
                }
            
            return {
                'providers': metrics,
                'pool_config': {
                    'max_connections': self.settings.llm_max_connections,
                    'max_keepalive_connections': self.settings.llm_max_keepalive_connections,
                    'keepalive_expiry_seconds': self.settings.llm_keepalive_expiry,
                    'timeout_seconds': self.settings.llm_timeout
                },
                'last_updated': datetime.now().isoformat()
            }
    
    def cleanup_expired_clients(self):
        """Remove expired clients from pool"""
        with self._client_lock:
            now = datetime.now()
            expiry_threshold = timedelta(seconds=self.settings.llm_keepalive_expiry * 2)
            
            for provider, clients in self._clients.items():
                expired_clients = [
                    client for client in clients
                    if now - client.last_used > expiry_threshold
                ]
                
                for client in expired_clients:
                    clients.remove(client)
                    logger.debug(f"Removed expired {provider.value} client from pool")
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on LLM API connections"""
        health_status = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'providers': {},
            'pool_metrics': self.get_pool_metrics()
        }
        
        # Check if HTTP client is healthy
        try:
            # Simple connectivity check
            await asyncio.wait_for(
                self._http_client.get("https://httpbin.org/status/200"),
                timeout=5.0
            )
            health_status['http_client'] = 'healthy'
        except Exception as e:
            health_status['http_client'] = 'unhealthy'
            health_status['status'] = 'degraded'
            logger.warning(f"HTTP client health check failed: {e}")
        
        return health_status
    
    async def close(self):
        """Clean shutdown of connection pools"""
        await self._http_client.aclose()
        
        with self._client_lock:
            for provider, clients in self._clients.items():
                clients.clear()
                logger.info(f"Closed {provider.value} client pool")
        
        logger.info("LLM connection pool manager closed")


# Global LLM pool manager instance
_llm_pool_manager: Optional[LLMPoolManager] = None
_llm_pool_lock = Lock()


def get_llm_pool_manager() -> LLMPoolManager:
    """Get global LLM pool manager instance"""
    global _llm_pool_manager
    
    if _llm_pool_manager is None:
        with _llm_pool_lock:
            if _llm_pool_manager is None:
                _llm_pool_manager = LLMPoolManager()
    
    return _llm_pool_manager


async def close_llm_pool():
    """Close the global LLM connection pool"""
    global _llm_pool_manager
    if _llm_pool_manager:
        await _llm_pool_manager.close()
        _llm_pool_manager = None