"""
Enhanced Database Connection Pool Manager
Provides connection pooling with monitoring, health checks, and metrics
"""
import logging
import time
from contextlib import contextmanager
from dataclasses import dataclass
from threading import Lock
from typing import Dict, Any, Generator, Optional
from datetime import datetime

from sqlalchemy import create_engine, event, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from sqlalchemy.engine import Engine

from app.config import get_settings

logger = logging.getLogger(__name__)


@dataclass
class PoolMetrics:
    """Connection pool metrics"""
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    pool_overflow: int = 0
    connection_acquisitions: int = 0
    connection_failures: int = 0
    avg_acquisition_time: float = 0.0
    peak_connections: int = 0
    last_updated: datetime = None


class DatabasePoolManager:
    """Enhanced database connection pool manager with monitoring"""
    
    def __init__(self, database_url: str):
        self.settings = get_settings()
        self.database_url = database_url
        self._engine: Optional[Engine] = None
        self._session_factory: Optional[sessionmaker] = None
        self._metrics = PoolMetrics()
        self._metrics_lock = Lock()
        self._acquisition_times = []
        
        self._initialize_engine()
        self._setup_event_listeners()
        
    def _initialize_engine(self):
        """Initialize SQLAlchemy engine with enhanced pool configuration"""
        try:
            self._engine = create_engine(
                self.database_url,
                # Pool configuration
                poolclass=QueuePool,
                pool_size=self.settings.db_pool_size,
                max_overflow=self.settings.db_max_overflow,
                pool_timeout=self.settings.db_pool_timeout,
                pool_recycle=self.settings.db_pool_recycle,
                pool_pre_ping=self.settings.db_pool_pre_ping,
                
                # Connection configuration
                echo=self.settings.db_echo,
                future=True,
                
                # Additional pool settings for reliability
                pool_reset_on_return='commit',
                connect_args={
                    'connect_timeout': 10,
                    'application_name': 'memory_master_api'
                }
            )
            
            self._session_factory = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self._engine
            )
            
            logger.info(f"Database pool initialized: size={self.settings.db_pool_size}, "
                       f"max_overflow={self.settings.db_max_overflow}")
                       
        except Exception as e:
            logger.error(f"Failed to initialize database pool: {e}")
            raise
    
    def _setup_event_listeners(self):
        """Setup SQLAlchemy event listeners for pool monitoring"""
        if not self._engine:
            return
            
        @event.listens_for(self._engine, "connect")
        def on_connect(dbapi_connection, connection_record):
            """Track new connections"""
            with self._metrics_lock:
                self._metrics.total_connections += 1
                self._metrics.peak_connections = max(
                    self._metrics.peak_connections,
                    self._metrics.total_connections
                )
                
        @event.listens_for(self._engine, "checkout")
        def on_checkout(dbapi_connection, connection_record, connection_proxy):
            """Track connection acquisitions"""
            start_time = getattr(connection_record, '_checkout_start_time', None)
            if start_time:
                acquisition_time = time.time() - start_time
                with self._metrics_lock:
                    self._acquisition_times.append(acquisition_time)
                    # Keep only last 100 measurements for avg calculation
                    if len(self._acquisition_times) > 100:
                        self._acquisition_times = self._acquisition_times[-100:]
                    self._metrics.avg_acquisition_time = sum(self._acquisition_times) / len(self._acquisition_times)
                    self._metrics.connection_acquisitions += 1
                    
        @event.listens_for(self._engine, "checkin")
        def on_checkin(dbapi_connection, connection_record):
            """Track connection returns"""
            # Reset checkout start time
            if hasattr(connection_record, '_checkout_start_time'):
                delattr(connection_record, '_checkout_start_time')
                
        @event.listens_for(self._engine, "before_cursor_execute")
        def on_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            """Track connection checkout start time"""
            if hasattr(conn, 'connection'):
                conn.connection._checkout_start_time = time.time()
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """Get database session with automatic cleanup and error handling"""
        if not self._session_factory:
            raise RuntimeError("Database pool not initialized")
            
        session = self._session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            with self._metrics_lock:
                self._metrics.connection_failures += 1
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def get_session_factory(self) -> sessionmaker:
        """Get the session factory for dependency injection"""
        if not self._session_factory:
            raise RuntimeError("Database pool not initialized")
        return self._session_factory
    
    def get_engine(self) -> Engine:
        """Get the SQLAlchemy engine"""
        if not self._engine:
            raise RuntimeError("Database pool not initialized")
        return self._engine
    
    def get_pool_metrics(self) -> Dict[str, Any]:
        """Get current pool metrics"""
        if not self._engine or not hasattr(self._engine.pool, 'status'):
            return {}
            
        pool = self._engine.pool
        with self._metrics_lock:
            # Update real-time pool status
            self._metrics.active_connections = pool.checkedout()
            self._metrics.idle_connections = pool.checkedin()
            self._metrics.pool_overflow = pool.overflow()
            self._metrics.last_updated = datetime.now()
            
            return {
                'total_connections': self._metrics.total_connections,
                'active_connections': self._metrics.active_connections,
                'idle_connections': self._metrics.idle_connections,
                'pool_overflow': self._metrics.pool_overflow,
                'connection_acquisitions': self._metrics.connection_acquisitions,
                'connection_failures': self._metrics.connection_failures,
                'avg_acquisition_time_ms': round(self._metrics.avg_acquisition_time * 1000, 2),
                'peak_connections': self._metrics.peak_connections,
                'pool_size': self.settings.db_pool_size,
                'max_overflow': self.settings.db_max_overflow,
                'last_updated': self._metrics.last_updated.isoformat() if self._metrics.last_updated else None
            }
    
    def health_check(self) -> Dict[str, Any]:
        """Perform database connectivity health check"""
        try:
            with self.get_session() as session:
                result = session.execute(text("SELECT 1 as health_check"))
                row = result.fetchone()
                
                health_status = {
                    'status': 'healthy' if row and row[0] == 1 else 'unhealthy',
                    'timestamp': datetime.now().isoformat(),
                    'pool_metrics': self.get_pool_metrics()
                }
                
                # Check for pool exhaustion warning
                metrics = self.get_pool_metrics()
                if metrics.get('active_connections', 0) > self.settings.db_pool_size * 0.8:
                    health_status['warnings'] = ['High connection pool utilization']
                
                return health_status
                
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def close(self):
        """Clean shutdown of connection pool"""
        if self._engine:
            self._engine.dispose()
            logger.info("Database connection pool closed")


# Global pool manager instance
_pool_manager: Optional[DatabasePoolManager] = None
_pool_lock = Lock()


def get_pool_manager() -> DatabasePoolManager:
    """Get global database pool manager instance"""
    global _pool_manager
    
    if _pool_manager is None:
        with _pool_lock:
            if _pool_manager is None:
                import os
                database_url = os.getenv("SUPABASE_DATABASE_URL")
                if not database_url:
                    raise RuntimeError("SUPABASE_DATABASE_URL not configured")
                _pool_manager = DatabasePoolManager(database_url)
    
    return _pool_manager


def get_db() -> Generator[Session, None, None]:
    """FastAPI dependency for database sessions"""
    pool_manager = get_pool_manager()
    with pool_manager.get_session() as session:
        yield session


def close_pool():
    """Close the global connection pool"""
    global _pool_manager
    if _pool_manager:
        _pool_manager.close()
        _pool_manager = None