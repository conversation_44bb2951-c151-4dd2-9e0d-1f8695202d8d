import os
from functools import lru_cache
from typing import Optional

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Legacy settings
    user_id: str = os.getenv("USER", "default_user")
    default_app_id: str = "openmemory"
    
    # Database settings
    database_url: str = os.getenv("DATABASE_URL", "")
    
    # Redis settings
    redis_host: str = os.getenv("REDIS_HOST", "localhost")
    redis_port: int = int(os.getenv("REDIS_PORT", "6379"))
    redis_password: Optional[str] = os.getenv("REDIS_PASSWORD")
    redis_db: int = int(os.getenv("REDIS_DB", "0"))
    redis_max_connections: int = int(os.getenv("REDIS_MAX_CONNECTIONS", "20"))
    
    # Cache settings
    cache_ttl_default: int = int(os.getenv("CACHE_TTL_DEFAULT", "3600"))  # 1 hour
    cache_ttl_embeddings: int = int(os.getenv("CACHE_TTL_EMBEDDINGS", "86400"))  # 24 hours
    cache_ttl_similarities: int = int(os.getenv("CACHE_TTL_SIMILARITIES", "3600"))  # 1 hour
    cache_ttl_queries: int = int(os.getenv("CACHE_TTL_QUERIES", "1800"))  # 30 minutes
    
    # Performance settings
    max_concurrent_operations: int = int(os.getenv("MAX_CONCURRENT_OPERATIONS", "10"))
    pool_monitoring_enabled: bool = bool(os.getenv("POOL_MONITORING_ENABLED", "true").lower() == "true")
    
    # Circuit breaker settings (replacing old duplicate settings)
    circuit_breaker_failure_threshold: int = int(os.getenv("CIRCUIT_BREAKER_FAILURE_THRESHOLD", "5"))
    circuit_breaker_recovery_timeout: int = int(os.getenv("CIRCUIT_BREAKER_RECOVERY_TIMEOUT", "60"))
    circuit_breaker_half_open_max_calls: int = int(os.getenv("CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS", "3"))
    
    CONCURRENT_PROCESSING_LIMIT: int = 50
    
    # Database connection pool settings
    db_pool_size: int = int(os.getenv("DB_POOL_SIZE", "20"))
    db_max_overflow: int = int(os.getenv("DB_MAX_OVERFLOW", "30"))
    db_pool_timeout: int = int(os.getenv("DB_POOL_TIMEOUT", "60"))
    db_pool_recycle: int = int(os.getenv("DB_POOL_RECYCLE", "3600"))
    db_pool_pre_ping: bool = os.getenv("DB_POOL_PRE_PING", "true").lower() == "true"
    db_echo: bool = os.getenv("DB_ECHO", "false").lower() == "true"
    
    # LLM API connection pool settings
    llm_max_connections: int = int(os.getenv("LLM_MAX_CONNECTIONS", "10"))
    llm_max_keepalive_connections: int = int(os.getenv("LLM_MAX_KEEPALIVE_CONNECTIONS", "5"))
    llm_keepalive_expiry: int = int(os.getenv("LLM_KEEPALIVE_EXPIRY", "300"))  # 5 minutes
    llm_timeout: int = int(os.getenv("LLM_TIMEOUT", "60"))
    llm_retry_max_attempts: int = int(os.getenv("LLM_RETRY_MAX_ATTEMPTS", "3"))
    llm_retry_base_delay: float = float(os.getenv("LLM_RETRY_BASE_DELAY", "1.0"))
    
    # Connection pool monitoring
    pool_metrics_interval: int = int(os.getenv("POOL_METRICS_INTERVAL", "30"))  # seconds
    
    # AI API keys
    anthropic_api_key: Optional[str] = os.getenv("ANTHROPIC_API_KEY")
    openai_api_key: Optional[str] = os.getenv("OPENAI_API_KEY")
    
    # Authentication settings
    next_public_auth_enabled: str = os.getenv("NEXT_PUBLIC_AUTH_ENABLED", "false")
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "allow"


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()


# Legacy compatibility
USER_ID = get_settings().user_id
DEFAULT_APP_ID = get_settings().default_app_id