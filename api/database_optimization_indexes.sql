-- Database Optimization: Evolution Tables Indexing
-- Performance optimization for evolution_operations and evolution_insights tables
-- Created for Task 13.2: Database Query and Index Optimization

-- =======================================================================================
-- ANALYSIS RESULTS:
-- - evolution_operations: Missing composite indexes for high-frequency query patterns
-- - Daily aggregation queries need covering indexes
-- - JSONB metadata queries require GIN indexing
-- - Analytics queries need performance indexes for confidence/similarity scores
-- =======================================================================================

BEGIN;

-- Performance measurement before optimization
SELECT 'BEFORE OPTIMIZATION - Current index usage:' as status;

-- Show current indexes on evolution tables
SELECT 
    schemaname, 
    tablename, 
    indexname, 
    indexdef 
FROM pg_indexes 
WHERE schemaname = 'memory_master' 
    AND tablename IN ('evolution_operations', 'evolution_insights')
ORDER BY tablename, indexname;

-- ===============================
-- 1. EVOLUTION_OPERATIONS OPTIMIZATIONS
-- ===============================

-- Index 1: Composite index for daily aggregation queries
-- Covers the high-frequency query pattern in insights_aggregator.py (line ~160):
-- WHERE user_id = ? AND DATE(created_at) = ? GROUP BY operation_type
CREATE INDEX IF NOT EXISTS idx_evolution_ops_daily_aggregation 
ON memory_master.evolution_operations (user_id, created_at, operation_type);

-- Index 2: GIN index for JSONB metadata queries
-- Enables efficient queries on metadata fields (conflict_detected, client_name, etc.)
CREATE INDEX IF NOT EXISTS idx_evolution_ops_metadata_gin 
ON memory_master.evolution_operations USING GIN (metadata);

-- Index 3: Composite index for analytics queries
-- Optimizes queries that filter/sort by confidence and similarity scores
CREATE INDEX IF NOT EXISTS idx_evolution_ops_analytics 
ON memory_master.evolution_operations (confidence_score, similarity_score, created_at);

-- Index 4: Covering index for user operation statistics
-- Covers common queries that need operation type, confidence, and similarity by user
CREATE INDEX IF NOT EXISTS idx_evolution_ops_user_stats 
ON memory_master.evolution_operations (user_id, operation_type) 
INCLUDE (confidence_score, similarity_score, created_at);

-- Index 5: Partial index for recent operations (last 30 days)
-- Optimizes queries on recent data which are most frequently accessed
CREATE INDEX IF NOT EXISTS idx_evolution_ops_recent 
ON memory_master.evolution_operations (user_id, created_at) 
WHERE created_at >= (CURRENT_DATE - INTERVAL '30 days');

-- ===============================
-- 2. EVOLUTION_INSIGHTS OPTIMIZATIONS
-- ===============================

-- Index 6: Composite index for trend analysis queries
-- Optimizes window function queries in insights_aggregator.py (line ~320)
CREATE INDEX IF NOT EXISTS idx_evolution_insights_trends 
ON memory_master.evolution_insights (user_id, date, learning_efficiency, average_confidence);

-- Index 7: Covering index for aggregation summaries
-- Covers queries that sum operations and calculate averages by date ranges
CREATE INDEX IF NOT EXISTS idx_evolution_insights_summary 
ON memory_master.evolution_insights (date) 
INCLUDE (total_operations, learning_efficiency, average_confidence, conflict_resolution_count);

-- Index 8: Partial index for active users (recent activity)
-- Optimizes queries for users with recent evolution activity
CREATE INDEX IF NOT EXISTS idx_evolution_insights_active_users 
ON memory_master.evolution_insights (user_id, updated_at) 
WHERE updated_at >= (CURRENT_DATE - INTERVAL '7 days');

-- ===============================
-- 3. QUERY OPTIMIZATION HINTS
-- ===============================

-- Update table statistics for better query planning
ANALYZE memory_master.evolution_operations;
ANALYZE memory_master.evolution_insights;

-- Show new indexes created
SELECT 'AFTER OPTIMIZATION - New indexes created:' as status;

SELECT 
    schemaname, 
    tablename, 
    indexname, 
    indexdef 
FROM pg_indexes 
WHERE schemaname = 'memory_master' 
    AND tablename IN ('evolution_operations', 'evolution_insights')
    AND indexname LIKE 'idx_evolution_%'
ORDER BY tablename, indexname;

-- ===============================
-- 4. PERFORMANCE VERIFICATION QUERIES
-- ===============================

-- Test query 1: Daily aggregation performance (should use idx_evolution_ops_daily_aggregation)
EXPLAIN (ANALYZE, BUFFERS) 
SELECT 
    operation_type,
    COUNT(*) as operation_count,
    AVG(confidence_score) as avg_confidence,
    AVG(similarity_score) as avg_similarity
FROM memory_master.evolution_operations
WHERE user_id = (SELECT user_id FROM memory_master.evolution_operations LIMIT 1)
    AND DATE(created_at) = CURRENT_DATE - INTERVAL '1 day'
GROUP BY operation_type;

-- Test query 2: Metadata search performance (should use idx_evolution_ops_metadata_gin)
EXPLAIN (ANALYZE, BUFFERS)
SELECT COUNT(*)
FROM memory_master.evolution_operations
WHERE metadata->>'conflict_detected' = 'true'
    AND created_at >= CURRENT_DATE - INTERVAL '7 days';

-- Test query 3: Trend analysis performance (should use idx_evolution_insights_trends)
EXPLAIN (ANALYZE, BUFFERS)
SELECT 
    date,
    learning_efficiency,
    LAG(learning_efficiency) OVER (ORDER BY date) as prev_efficiency
FROM memory_master.evolution_insights
WHERE user_id = (SELECT user_id FROM memory_master.evolution_insights LIMIT 1)
    AND date >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY date;

COMMIT;

-- ===============================
-- 5. MONITORING AND MAINTENANCE
-- ===============================

-- Query to monitor index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE schemaname = 'memory_master' 
    AND tablename IN ('evolution_operations', 'evolution_insights')
ORDER BY idx_scan DESC;

-- Query to monitor table size and bloat
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size
FROM pg_tables 
WHERE schemaname = 'memory_master' 
    AND tablename IN ('evolution_operations', 'evolution_insights');

-- Expected Performance Improvements:
-- - Daily aggregation queries: 50-80% faster due to covering indexes
-- - Metadata searches: 90%+ faster with GIN indexing
-- - User statistics queries: 60-70% faster with composite indexes
-- - Recent data queries: 40-60% faster with partial indexes
-- - Trend analysis: 30-50% faster with optimized window function support

SELECT 'Database optimization completed successfully!' as status;