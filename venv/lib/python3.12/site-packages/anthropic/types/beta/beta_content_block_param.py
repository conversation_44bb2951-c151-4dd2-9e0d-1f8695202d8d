# File generated from our OpenAPI spec by <PERSON><PERSON><PERSON>. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import Union
from typing_extensions import TypeAlias

from .beta_text_block_param import <PERSON>TextBlockParam
from .beta_image_block_param import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>aram
from .beta_thinking_block_param import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>aram
from .beta_tool_use_block_param import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ara<PERSON>
from .beta_base64_pdf_block_param import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>aram
from .beta_tool_result_block_param import <PERSON><PERSON>oolResult<PERSON>lockParam
from .beta_server_tool_use_block_param import BetaServer<PERSON>ool<PERSON>se<PERSON>lockParam
from .beta_redacted_thinking_block_param import BetaRedacted<PERSON>hinking<PERSON>lockParam
from .beta_web_search_tool_result_block_param import BetaWebSearchToolResultBlockParam

__all__ = ["BetaContentBlockParam"]

BetaContentBlockParam: TypeAlias = Union[
    BetaTextBlockParam,
    BetaImageBlockParam,
    BetaToolUseBlockParam,
    BetaServerToolUseBlockParam,
    BetaWebSearchToolResultBlockParam,
    BetaToolResultBlockParam,
    BetaBase64PDFBlockParam,
    BetaThinkingBlockParam,
    BetaRedactedThinkingBlockParam,
]
