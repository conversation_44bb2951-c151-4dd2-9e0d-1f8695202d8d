# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from typing import Union
from typing_extensions import Literal, Annotated, TypeAlias

from ..._utils import PropertyInfo
from ..._models import BaseModel
from .beta_text_block import <PERSON>Text<PERSON>lock
from .beta_thinking_block import <PERSON><PERSON>hinking<PERSON><PERSON>
from .beta_tool_use_block import BetaT<PERSON><PERSON><PERSON><PERSON><PERSON>
from .beta_server_tool_use_block import <PERSON><PERSON><PERSON>r<PERSON>ool<PERSON><PERSON><PERSON><PERSON>
from .beta_redacted_thinking_block import BetaRedactedThinking<PERSON>lock
from .beta_web_search_tool_result_block import BetaWebSearchToolResultBlock

__all__ = ["BetaRawContentBlockStartEvent", "ContentBlock"]

ContentBlock: TypeAlias = Annotated[
    Union[
        BetaTextBlock,
        <PERSON>ToolUseBlock,
        BetaServerToolUseBlock,
        BetaWebSearchToolResultBlock,
        <PERSON>Thinking<PERSON>lock,
        BetaRedactedThinkingBlock,
    ],
    PropertyInfo(discriminator="type"),
]


class BetaRawContentBlockStartEvent(BaseModel):
    content_block: ContentBlock

    index: int

    type: Literal["content_block_start"]
