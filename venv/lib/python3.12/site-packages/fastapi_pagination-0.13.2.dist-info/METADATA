Metadata-Version: 2.4
Name: fastapi-pagination
Version: 0.13.2
Summary: FastAPI pagination
Project-URL: Repository, https://github.com/uriyyo/fastapi-pagination
Author-email: <PERSON><PERSON> <<EMAIL>>
License-Expression: MIT
License-File: LICENSE
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: <4.0,>=3.9
Requires-Dist: fastapi>=0.93.0
Requires-Dist: pydantic>=1.9.1
Requires-Dist: typing-extensions<5,>=4.8.0
Provides-Extra: all
Requires-Dist: asyncpg>=0.24.0; extra == 'all'
Requires-Dist: beanie>=1.25.0; extra == 'all'
Requires-Dist: bunnet<2,>=1.1.0; extra == 'all'
Requires-Dist: databases>=0.6.0; extra == 'all'
Requires-Dist: django<6.0.0; extra == 'all'
Requires-Dist: elasticsearch-dsl<9,>=8.13.0; extra == 'all'
Requires-Dist: google-cloud-firestore<3,>=2.19.0; extra == 'all'
Requires-Dist: mongoengine<0.30.0,>=0.23.1; extra == 'all'
Requires-Dist: motor<4.0.0,>=3.6.0; extra == 'all'
Requires-Dist: odmantic<2,>=1.0.2; extra == 'all'
Requires-Dist: orm>=0.3.1; extra == 'all'
Requires-Dist: piccolo<1.25,>=0.89; extra == 'all'
Requires-Dist: pony<0.8,>=0.7.16; extra == 'all'
Requires-Dist: scylla-driver<4,>=3.25.6; extra == 'all'
Requires-Dist: sqlakeyset<3,>=2.0.**********; extra == 'all'
Requires-Dist: sqlalchemy>=1.3.20; extra == 'all'
Requires-Dist: sqlmodel>=0.0.22; extra == 'all'
Requires-Dist: tortoise-orm>=0.22.0; extra == 'all'
Provides-Extra: asyncpg
Requires-Dist: asyncpg>=0.24.0; extra == 'asyncpg'
Requires-Dist: sqlalchemy>=1.3.20; extra == 'asyncpg'
Provides-Extra: beanie
Requires-Dist: beanie>=1.25.0; extra == 'beanie'
Provides-Extra: bunnet
Requires-Dist: bunnet<2,>=1.1.0; extra == 'bunnet'
Provides-Extra: databases
Requires-Dist: databases>=0.6.0; extra == 'databases'
Provides-Extra: django
Requires-Dist: databases>=0.6.0; extra == 'django'
Requires-Dist: django<6.0.0; extra == 'django'
Provides-Extra: elasticsearch
Requires-Dist: elasticsearch-dsl<9,>=8.13.0; extra == 'elasticsearch'
Provides-Extra: firestore
Requires-Dist: google-cloud-firestore<3,>=2.19.0; extra == 'firestore'
Provides-Extra: mongoengine
Requires-Dist: mongoengine<0.30.0,>=0.23.1; extra == 'mongoengine'
Provides-Extra: motor
Requires-Dist: motor<4.0.0,>=3.6.0; extra == 'motor'
Provides-Extra: odmantic
Requires-Dist: odmantic<2,>=1.0.2; extra == 'odmantic'
Provides-Extra: orm
Requires-Dist: databases>=0.6.0; extra == 'orm'
Requires-Dist: orm>=0.3.1; extra == 'orm'
Provides-Extra: ormar
Provides-Extra: piccolo
Requires-Dist: piccolo<1.25,>=0.89; extra == 'piccolo'
Provides-Extra: scylla-driver
Requires-Dist: scylla-driver<4,>=3.25.6; extra == 'scylla-driver'
Provides-Extra: sqlalchemy
Requires-Dist: sqlakeyset<3,>=2.0.**********; extra == 'sqlalchemy'
Requires-Dist: sqlalchemy>=1.3.20; extra == 'sqlalchemy'
Provides-Extra: sqlmodel
Requires-Dist: sqlakeyset<3,>=2.0.**********; extra == 'sqlmodel'
Requires-Dist: sqlmodel>=0.0.22; extra == 'sqlmodel'
Provides-Extra: tortoise
Requires-Dist: tortoise-orm>=0.22.0; extra == 'tortoise'
Description-Content-Type: text/markdown

<h1 align="center">
<img alt="logo" src="https://raw.githubusercontent.com/uriyyo/fastapi-pagination/main/docs/img/logo.png">
</h1>

<div align="center">
<img alt="license" src="https://img.shields.io/badge/License-MIT-lightgrey">
<img alt="test" src="https://github.com/uriyyo/fastapi-pagination/workflows/Test/badge.svg">
<img alt="codecov" src="https://codecov.io/gh/uriyyo/fastapi-pagination/branch/main/graph/badge.svg?token=QqIqDQ7FZi">
<a href="https://pepy.tech/project/fastapi-pagination"><img alt="downloads" src="https://pepy.tech/badge/fastapi-pagination"></a>
<a href="https://pypi.org/project/fastapi-pagination"><img alt="pypi" src="https://img.shields.io/pypi/v/fastapi-pagination"></a>
</div>

## Introduction

`fastapi-pagination` is a Python library designed to simplify pagination in FastAPI applications. 
It provides a set of utility functions and data models to help you paginate your database queries 
and return paginated responses to your clients.

With `fastapi-pagination`, you can easily define pagination parameters in your FastAPI endpoint functions,
and use them to generate paginated responses that include the requested subset of your data.
The library supports a variety of pagination strategies, including cursor-based pagination and page-based pagination.

`fastapi-pagination` is built on top of the popular `fastapi` library, and it works with a wide range 
of SQL and NoSQL databases frameworks. It also supports async/await syntax and is compatible with Python 3.9 and higher.

Features:

* Simplifies pagination in FastAPI applications.
* Supports a variety of pagination strategies, including cursor-based pagination and page-based pagination
* Works with a wide range of SQL and NoSQL databases frameworks, including `SQLAlchemy`, `Tortoise ORM`, and `PyMongo`.
* Supports async/await syntax.
* Compatible with Python 3.9 and higher.

----

For more information on how to use fastapi-pagination, please refer to the 
[official documentation](https://uriyyo-fastapi-pagination.netlify.app/).

---

## Installation

```bash
pip install fastapi-pagination
```

## Quickstart

All you need to do is to use `Page` class as a return type for your endpoint and call `paginate` function
on data you want to paginate.

```py
from fastapi import FastAPI
from pydantic import BaseModel, Field

# import all you need from fastapi-pagination
from fastapi_pagination import Page, add_pagination, paginate

app = FastAPI()  # create FastAPI app
add_pagination(app)  # important! add pagination to your app


class UserOut(BaseModel):  # define your model
    name: str = Field(..., example="Steve")
    surname: str = Field(..., example="Rogers")


users = [  # create some data
    UserOut(name="Steve", surname="Rogers"),
    # ...
]


# req: GET /users
@app.get("/users")
async def get_users() -> Page[UserOut]:
    # use Page[UserOut] as return type annotation
    return paginate(users)  # use paginate function to paginate your data
```

Please, be careful when you work with databases, because default `paginate` will require to load all data in memory.

For instance, if you use `SQLAlchemy` you can use `paginate` from `fastapi_pagination.ext.sqlalchemy` module.

```python
from sqlalchemy import select
from fastapi_pagination.ext.sqlalchemy import paginate


@app.get("/users")
def get_users(db: Session = Depends(get_db)) -> Page[UserOut]:
    return paginate(db, select(User).order_by(User.created_at))
```

---

Code from `Quickstart` will generate OpenAPI schema as bellow:

<div align="center">
<img alt="app-example" src="https://raw.githubusercontent.com/uriyyo/fastapi-pagination/main/docs/img/example.png">
</div>
