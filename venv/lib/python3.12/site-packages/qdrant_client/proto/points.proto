syntax = "proto3";

package qdrant;
option csharp_namespace = "Qdrant.Client.Grpc";

import "collections.proto";
import "google/protobuf/timestamp.proto";
import "json_with_int.proto";


enum WriteOrderingType {
  Weak = 0; // Write operations may be reordered, works faster, default
  Medium = 1; // Write operations go through dynamically selected leader, may be inconsistent for a short period of time in case of leader change
  Strong = 2; // Write operations go through the permanent leader, consistent, but may be unavailable if leader is down
}

message WriteOrdering {
  WriteOrderingType type = 1; // Write ordering guarantees
}

enum ReadConsistencyType {
  All = 0; // Send request to all nodes and return points which are present on all of them
  Majority = 1; // Send requests to all nodes and return points which are present on majority of them
  Quorum = 2; // Send requests to half + 1 nodes, return points which are present on all of them
}

message ReadConsistency {
  oneof value {
    ReadConsistencyType type = 1; // Common read consistency configurations
    uint64 factor = 2; // Send request to a specified number of nodes, and return points which are present on all of them
  }
}

// ---------------------------------------------
// ------------- Point Id Requests -------------
// ---------------------------------------------

message PointId {
  oneof point_id_options {
    uint64 num = 1; // Numerical ID of the point
    string uuid = 2; // UUID
  }
}

message SparseIndices {
  repeated uint32 data = 1;
}

message Document {
  string text = 1; // Text of the document
  string model = 3; // Model name
  map<string, Value> options = 4; // Model options
}

message Image {
  Value image = 1; // Image data, either base64 encoded or URL
  string model = 2; // Model name
  map<string, Value> options = 3; // Model options
}

message InferenceObject {
  Value object = 1; // Object to infer
  string model = 2; // Model name
  map<string, Value> options = 3; // Model options
}

// Legacy vector format, which determines the vector type by the configuration of its fields.
message Vector {
  repeated float data = 1; // Vector data (flatten for multi vectors), deprecated
  optional SparseIndices indices = 2; // Sparse indices for sparse vectors, deprecated
  optional uint32 vectors_count = 3; // Number of vectors per multi vector, deprecated
  oneof vector {
    DenseVector dense = 101; // Dense vector
    SparseVector sparse = 102; // Sparse vector
    MultiDenseVector multi_dense = 103; // Multi dense vector
    Document document = 104;
    Image image = 105;
    InferenceObject object = 106;
  }
}

message VectorOutput {
  repeated float data = 1; // Vector data (flatten for multi vectors), deprecated
  optional SparseIndices indices = 2; // Sparse indices for sparse vectors, deprecated
  optional uint32 vectors_count = 3; // Number of vectors per multi vector, deprecated
  oneof vector {
    DenseVector dense = 101; // Dense vector
    SparseVector sparse = 102; // Sparse vector
    MultiDenseVector multi_dense = 103; // Multi dense vector
  }
}

message DenseVector {
  repeated float data = 1;
}

message SparseVector {
  repeated float values = 1;
  repeated uint32 indices = 2;
}

message MultiDenseVector {
  repeated DenseVector vectors = 1;
}

// Vector type to be used in queries. Ids will be substituted with their corresponding vectors from the collection.
message VectorInput {
  oneof variant {
    PointId id = 1;
    DenseVector dense = 2;
    SparseVector sparse = 3;
    MultiDenseVector multi_dense = 4;
    Document document = 5;
    Image image = 6;
    InferenceObject object = 7;
  }
}

// ---------------------------------------------
// ----------------- ShardKeySelector ----------
// ---------------------------------------------

message ShardKeySelector {
  repeated ShardKey shard_keys = 1; // List of shard keys which should be used in the request
}


// ---------------------------------------------
// ---------------- RPC Requests ---------------
// ---------------------------------------------

message UpsertPoints {
  string collection_name = 1; // name of the collection
  optional bool wait = 2; // Wait until the changes have been applied?
  repeated PointStruct points = 3;
  optional WriteOrdering ordering = 4; // Write ordering guarantees
  optional ShardKeySelector shard_key_selector = 5; // Option for custom sharding to specify used shard keys
}

message DeletePoints {
  string collection_name = 1; // name of the collection
  optional bool wait = 2; // Wait until the changes have been applied?
  PointsSelector points = 3; // Affected points
  optional WriteOrdering ordering = 4; // Write ordering guarantees
  optional ShardKeySelector shard_key_selector = 5; // Option for custom sharding to specify used shard keys
}

message GetPoints {
  string collection_name = 1; // name of the collection
  repeated PointId ids = 2; // List of points to retrieve
  reserved 3; // deprecated "with_vector" field
  WithPayloadSelector with_payload = 4; // Options for specifying which payload to include or not
  optional WithVectorsSelector with_vectors = 5; // Options for specifying which vectors to include into response
  optional ReadConsistency read_consistency = 6; // Options for specifying read consistency guarantees
  optional ShardKeySelector shard_key_selector = 7; // Specify in which shards to look for the points, if not specified - look in all shards
  optional uint64 timeout = 8; // If set, overrides global timeout setting for this request. Unit is seconds.
}

message UpdatePointVectors {
  string collection_name = 1; // name of the collection
  optional bool wait = 2; // Wait until the changes have been applied?
  repeated PointVectors points = 3; // List of points and vectors to update
  optional WriteOrdering ordering = 4; // Write ordering guarantees
  optional ShardKeySelector shard_key_selector = 5; // Option for custom sharding to specify used shard keys
}

message PointVectors {
  PointId id = 1; // ID to update vectors for
  Vectors vectors = 2; // Named vectors to update, leave others intact
}

message DeletePointVectors {
  string collection_name = 1; // name of the collection
  optional bool wait = 2; // Wait until the changes have been applied?
  PointsSelector points_selector = 3; // Affected points
  VectorsSelector vectors = 4; // List of vector names to delete
  optional WriteOrdering ordering = 5; // Write ordering guarantees
  optional ShardKeySelector shard_key_selector = 6; // Option for custom sharding to specify used shard keys
}

message SetPayloadPoints {
  string collection_name = 1; // name of the collection
  optional bool wait = 2; // Wait until the changes have been applied?
  map<string, Value> payload = 3; // New payload values
  reserved 4; // List of point to modify, deprecated
  optional PointsSelector points_selector = 5; // Affected points
  optional WriteOrdering ordering = 6; // Write ordering guarantees
  optional ShardKeySelector shard_key_selector = 7; // Option for custom sharding to specify used shard keys
  optional string key = 8; // Option for indicate property of payload
}

message DeletePayloadPoints {
  string collection_name = 1; // name of the collection
  optional bool wait = 2; // Wait until the changes have been applied?
  repeated string keys = 3; // List of keys to delete
  reserved 4; // Affected points, deprecated
  optional PointsSelector points_selector = 5; // Affected points
  optional WriteOrdering ordering = 6; // Write ordering guarantees
  optional ShardKeySelector shard_key_selector = 7; // Option for custom sharding to specify used shard keys
}

message ClearPayloadPoints {
  string collection_name = 1; // name of the collection
  optional bool wait = 2; // Wait until the changes have been applied?
  PointsSelector points = 3; // Affected points
  optional WriteOrdering ordering = 4; // Write ordering guarantees
  optional ShardKeySelector shard_key_selector = 5; // Option for custom sharding to specify used shard keys
}

enum FieldType {
  FieldTypeKeyword = 0;
  FieldTypeInteger = 1;
  FieldTypeFloat = 2;
  FieldTypeGeo = 3;
  FieldTypeText = 4;
  FieldTypeBool = 5;
  FieldTypeDatetime = 6;
  FieldTypeUuid = 7;
}

message CreateFieldIndexCollection {
  string collection_name = 1; // name of the collection
  optional bool wait = 2; // Wait until the changes have been applied?
  string field_name = 3; // Field name to index
  optional FieldType field_type = 4; // Field type.
  optional PayloadIndexParams field_index_params = 5; // Payload index params.
  optional WriteOrdering ordering = 6; // Write ordering guarantees
}

message DeleteFieldIndexCollection {
  string collection_name = 1; // name of the collection
  optional bool wait = 2; // Wait until the changes have been applied?
  string field_name = 3; // Field name to delete
  optional WriteOrdering ordering = 4; // Write ordering guarantees
}

message PayloadIncludeSelector {
  repeated string fields = 1; // List of payload keys to include into result
}

message PayloadExcludeSelector {
  repeated string fields = 1; // List of payload keys to exclude from the result
}

message WithPayloadSelector {
  oneof selector_options {
    bool enable = 1; // If `true` - return all payload, if `false` - none
    PayloadIncludeSelector include = 2;
    PayloadExcludeSelector exclude = 3;
  }
}

message NamedVectors {
  map<string, Vector> vectors = 1;
}

message NamedVectorsOutput {
  map<string, VectorOutput> vectors = 1;
}

message Vectors {
  oneof vectors_options {
    Vector vector = 1;
    NamedVectors vectors = 2;
  }
}

message VectorsOutput {
  oneof vectors_options {
    VectorOutput vector = 1;
    NamedVectorsOutput vectors = 2;
  }
}

message VectorsSelector {
  repeated string names = 1; // List of vectors to include into result
}

message WithVectorsSelector {
  oneof selector_options {
    bool enable = 1; // If `true` - return all vectors, if `false` - none
    VectorsSelector include = 2; // List of payload keys to include into result
  }
}

message QuantizationSearchParams {
  /*
  If set to true, search will ignore quantized vector data
  */
  optional bool ignore = 1;

  /*
  If true, use original vectors to re-score top-k results. If ignored, qdrant decides automatically does rescore enabled or not.
  */
  optional bool rescore = 2;

  /*
  Oversampling factor for quantization.

  Defines how many extra vectors should be pre-selected using quantized index,
  and then re-scored using original vectors.

  For example, if `oversampling` is 2.4 and `limit` is 100, then 240 vectors will be pre-selected using quantized index,
  and then top-100 will be returned after re-scoring.
  */
  optional double oversampling = 3;
}

message SearchParams {
  /*
  Params relevant to HNSW index. Size of the beam in a beam-search.
  Larger the value - more accurate the result, more time required for search.
  */
  optional uint64 hnsw_ef = 1;

  /*
  Search without approximation. If set to true, search may run long but with exact results.
  */
  optional bool exact = 2;

  /*
  If set to true, search will ignore quantized vector data
  */
  optional QuantizationSearchParams quantization = 3;
  /*
  If enabled, the engine will only perform search among indexed or small segments.
  Using this option prevents slow searches in case of delayed index, but does not
  guarantee that all uploaded vectors will be included in search results
  */
  optional bool indexed_only = 4;
}

message SearchPoints {
  string collection_name = 1; // name of the collection
  repeated float vector = 2; // vector
  Filter filter = 3; // Filter conditions - return only those points that satisfy the specified conditions
  uint64 limit = 4; // Max number of result
  reserved 5; // deprecated "with_vector" field
  WithPayloadSelector with_payload = 6; // Options for specifying which payload to include or not
  SearchParams params = 7; // Search config
  optional float score_threshold = 8; // If provided - cut off results with worse scores
  optional uint64 offset = 9; // Offset of the result
  optional string vector_name = 10; // Which vector to use for search, if not specified - use default vector
  optional WithVectorsSelector with_vectors = 11; // Options for specifying which vectors to include into response
  optional ReadConsistency read_consistency = 12; // Options for specifying read consistency guarantees
  optional uint64 timeout = 13; // If set, overrides global timeout setting for this request. Unit is seconds.
  optional ShardKeySelector shard_key_selector = 14; // Specify in which shards to look for the points, if not specified - look in all shards
  optional SparseIndices sparse_indices = 15;
}

message SearchBatchPoints {
  string collection_name = 1; // Name of the collection
  repeated SearchPoints search_points = 2;
  optional ReadConsistency read_consistency = 3; // Options for specifying read consistency guarantees
  optional uint64 timeout = 4; // If set, overrides global timeout setting for this request. Unit is seconds.
}

message WithLookup {
  string collection = 1; // Name of the collection to use for points lookup
  optional WithPayloadSelector with_payload = 2; // Options for specifying which payload to include (or not)
  optional WithVectorsSelector with_vectors = 3; // Options for specifying which vectors to include (or not)
}


message SearchPointGroups {
  string collection_name = 1; // Name of the collection
  repeated float vector = 2; // Vector to compare against
  Filter filter = 3; // Filter conditions - return only those points that satisfy the specified conditions
  uint32 limit = 4; // Max number of result
  WithPayloadSelector with_payload = 5; // Options for specifying which payload to include or not
  SearchParams params = 6; // Search config
  optional float score_threshold = 7; // If provided - cut off results with worse scores
  optional string vector_name = 8; // Which vector to use for search, if not specified - use default vector
  optional WithVectorsSelector with_vectors = 9; // Options for specifying which vectors to include into response
  string group_by = 10; // Payload field to group by, must be a string or number field. If there are multiple values for the field, all of them will be used. One point can be in multiple groups.
  uint32 group_size = 11; // Maximum amount of points to return per group
  optional ReadConsistency read_consistency = 12; // Options for specifying read consistency guarantees
  optional WithLookup with_lookup = 13; // Options for specifying how to use the group id to lookup points in another collection
  optional uint64 timeout = 14; // If set, overrides global timeout setting for this request. Unit is seconds.
  optional ShardKeySelector shard_key_selector = 15; // Specify in which shards to look for the points, if not specified - look in all shards
  optional SparseIndices sparse_indices = 16;
}

enum Direction {
  Asc = 0;
  Desc = 1;
}

message StartFrom {
  oneof value {
    double float = 1;
    int64 integer = 2;
    google.protobuf.Timestamp timestamp = 3;
    string datetime = 4;
  }
}

message OrderBy {
  string key = 1; // Payload key to order by
  optional Direction direction = 2; // Ascending or descending order
  optional StartFrom start_from = 3; // Start from this value
}

message ScrollPoints {
  string collection_name = 1;
  Filter filter = 2; // Filter conditions - return only those points that satisfy the specified conditions
  optional PointId offset = 3; // Start with this ID
  optional uint32 limit = 4; // Max number of result
  reserved 5; // deprecated "with_vector" field
  WithPayloadSelector with_payload = 6; // Options for specifying which payload to include or not
  optional WithVectorsSelector with_vectors = 7; // Options for specifying which vectors to include into response
  optional ReadConsistency read_consistency = 8; // Options for specifying read consistency guarantees
  optional ShardKeySelector shard_key_selector = 9; // Specify in which shards to look for the points, if not specified - look in all shards
  optional OrderBy order_by = 10; // Order the records by a payload field
  optional uint64 timeout = 11; // If set, overrides global timeout setting for this request. Unit is seconds.
}

// How to use positive and negative vectors to find the results, default is `AverageVector`.
enum RecommendStrategy {
  // Average positive and negative vectors and create a single query with the formula
  // `query = avg_pos + avg_pos - avg_neg`. Then performs normal search.
  AverageVector = 0;

  // Uses custom search objective. Each candidate is compared against all
  // examples, its score is then chosen from the `max(max_pos_score, max_neg_score)`.
  // If the `max_neg_score` is chosen then it is squared and negated.
  BestScore = 1;

  // Uses custom search objective. Compares against all inputs, sums all the scores.
  // Scores against positive vectors are added, against negatives are subtracted.
  SumScores = 2;
}

message LookupLocation {
  string collection_name = 1;
  optional string vector_name = 2; // Which vector to use for search, if not specified - use default vector
  optional ShardKeySelector shard_key_selector = 3; // Specify in which shards to look for the points, if not specified - look in all shards
}

message RecommendPoints {
  string collection_name = 1; // name of the collection
  repeated PointId positive = 2; // Look for vectors closest to the vectors from these points
  repeated PointId negative = 3; // Try to avoid vectors like the vector from these points
  Filter filter = 4; // Filter conditions - return only those points that satisfy the specified conditions
  uint64 limit = 5; // Max number of result
  reserved 6; // deprecated "with_vector" field
  WithPayloadSelector with_payload = 7; // Options for specifying which payload to include or not
  SearchParams params = 8; // Search config
  optional float score_threshold = 9; // If provided - cut off results with worse scores
  optional uint64 offset = 10; // Offset of the result
  optional string using = 11; // Define which vector to use for recommendation, if not specified - default vector
  optional WithVectorsSelector with_vectors = 12; // Options for specifying which vectors to include into response
  optional LookupLocation lookup_from = 13; // Name of the collection to use for points lookup, if not specified - use current collection
  optional ReadConsistency read_consistency = 14; // Options for specifying read consistency guarantees
  optional RecommendStrategy strategy = 16; // How to use the example vectors to find the results
  repeated Vector positive_vectors = 17; // Look for vectors closest to those
  repeated Vector negative_vectors = 18; // Try to avoid vectors like this
  optional uint64 timeout = 19; // If set, overrides global timeout setting for this request. Unit is seconds.
  optional ShardKeySelector shard_key_selector = 20; // Specify in which shards to look for the points, if not specified - look in all shards
}

message RecommendBatchPoints {
  string collection_name = 1; // Name of the collection
  repeated RecommendPoints recommend_points = 2;
  optional ReadConsistency read_consistency = 3; // Options for specifying read consistency guarantees
  optional uint64 timeout = 4; // If set, overrides global timeout setting for this request. Unit is seconds.
}

message RecommendPointGroups {
  string collection_name = 1; // Name of the collection
  repeated PointId positive = 2; // Look for vectors closest to the vectors from these points
  repeated PointId negative = 3; // Try to avoid vectors like the vector from these points
  Filter filter = 4; // Filter conditions - return only those points that satisfy the specified conditions
  uint32 limit = 5; // Max number of groups in result
  WithPayloadSelector with_payload = 6; // Options for specifying which payload to include or not
  SearchParams params = 7; // Search config
  optional float score_threshold = 8; // If provided - cut off results with worse scores
  optional string using = 9; // Define which vector to use for recommendation, if not specified - default vector
  optional WithVectorsSelector with_vectors = 10; // Options for specifying which vectors to include into response
  optional LookupLocation lookup_from = 11; // Name of the collection to use for points lookup, if not specified - use current collection
  string group_by = 12; // Payload field to group by, must be a string or number field. If there are multiple values for the field, all of them will be used. One point can be in multiple groups.
  uint32 group_size = 13; // Maximum amount of points to return per group
  optional ReadConsistency read_consistency = 14; // Options for specifying read consistency guarantees
  optional WithLookup with_lookup = 15; // Options for specifying how to use the group id to lookup points in another collection
  optional RecommendStrategy strategy = 17; // How to use the example vectors to find the results
  repeated Vector positive_vectors = 18; // Look for vectors closest to those
  repeated Vector negative_vectors = 19; // Try to avoid vectors like this
  optional uint64 timeout = 20; // If set, overrides global timeout setting for this request. Unit is seconds.
  optional ShardKeySelector shard_key_selector = 21; // Specify in which shards to look for the points, if not specified - look in all shards
}

message TargetVector {
  oneof target {
    VectorExample single = 1;

    // leaving extensibility for possibly adding multi-target
  }
}

message VectorExample {
  oneof example {
    PointId id = 1;
    Vector vector = 2;
  }
}

message ContextExamplePair {
  VectorExample positive = 1;
  VectorExample negative = 2;
}

message DiscoverPoints {
  string collection_name = 1; // name of the collection
  TargetVector target = 2; // Use this as the primary search objective
  repeated ContextExamplePair context = 3; // Search will be constrained by these pairs of examples
  Filter filter = 4; // Filter conditions - return only those points that satisfy the specified conditions
  uint64 limit = 5; // Max number of result
  WithPayloadSelector with_payload = 6; // Options for specifying which payload to include or not
  SearchParams params = 7; // Search config
  optional uint64 offset = 8; // Offset of the result
  optional string using = 9; // Define which vector to use for recommendation, if not specified - default vector
  optional WithVectorsSelector with_vectors = 10; // Options for specifying which vectors to include into response
  optional LookupLocation lookup_from = 11; // Name of the collection to use for points lookup, if not specified - use current collection
  optional ReadConsistency read_consistency = 12; // Options for specifying read consistency guarantees
  optional uint64 timeout = 13; // If set, overrides global timeout setting for this request. Unit is seconds.
  optional ShardKeySelector shard_key_selector = 14; // Specify in which shards to look for the points, if not specified - look in all shards
}

message DiscoverBatchPoints {
  string collection_name = 1; // Name of the collection
  repeated DiscoverPoints discover_points = 2;
  optional ReadConsistency read_consistency = 3; // Options for specifying read consistency guarantees
  optional uint64 timeout = 4; // If set, overrides global timeout setting for this request. Unit is seconds.
}

message CountPoints {
  string collection_name = 1; // Name of the collection
  Filter filter = 2; // Filter conditions - return only those points that satisfy the specified conditions
  optional bool exact = 3; // If `true` - return exact count, if `false` - return approximate count
  optional ReadConsistency read_consistency = 4; // Options for specifying read consistency guarantees
  optional ShardKeySelector shard_key_selector = 5; // Specify in which shards to look for the points, if not specified - look in all shards
   optional uint64 timeout = 6; // If set, overrides global timeout setting for this request. Unit is seconds.
}

message RecommendInput {
  repeated VectorInput positive = 1; // Look for vectors closest to the vectors from these points
  repeated VectorInput negative = 2; // Try to avoid vectors like the vector from these points
  optional RecommendStrategy strategy = 3; // How to use the provided vectors to find the results
}

message ContextInputPair {
  VectorInput positive = 1; // A positive vector
  VectorInput negative = 2; // Repel from this vector
}

message DiscoverInput {
  VectorInput target = 1; // Use this as the primary search objective
  ContextInput context = 2; // Search space will be constrained by these pairs of vectors
}

message ContextInput {
    repeated ContextInputPair pairs = 1; // Search space will be constrained by these pairs of vectors
}

enum Fusion {
    RRF = 0; // Reciprocal Rank Fusion
    DBSF = 1; // Distribution-Based Score Fusion
}

// Sample points from the collection
//
// Available sampling methods:
//
// * `random` - Random sampling
enum Sample {
    Random = 0;
}

message Formula {
    Expression expression = 1;
    map<string, Value> defaults = 2;
}

message Expression {
    oneof variant {
        float constant = 1;
        string variable = 2; // Payload key or reference to score.
        Condition condition = 3; // Payload condition. If true, becomes 1.0; otherwise 0.0
        GeoDistance geo_distance = 4; // Geographic distance in meters
        string datetime = 5; // Date-time constant
        string datetime_key = 6; // Payload key with date-time values
        MultExpression mult = 7; // Multiply
        SumExpression sum = 8; // Sum
        DivExpression div = 9; // Divide
        Expression neg = 10; // Negate
        Expression abs = 11; // Absolute value
        Expression sqrt = 12; // Square root
        PowExpression pow = 13; // Power
        Expression exp = 14; // Exponential
        Expression log10 = 15; // Logarithm
        Expression ln = 16; // Natural logarithm
        DecayParamsExpression exp_decay = 17; // Exponential decay
        DecayParamsExpression gauss_decay = 18; // Gaussian decay
        DecayParamsExpression lin_decay = 19; // Linear decay
    }
}

message GeoDistance {
    GeoPoint origin = 1;
    string to = 2;
}

message MultExpression {
    repeated Expression mult = 1;
}

message SumExpression {
    repeated Expression sum = 1;
}

message DivExpression {
    Expression left = 1;
    Expression right = 2;
    optional float by_zero_default = 3;
}

message PowExpression {
    Expression base = 1;
    Expression exponent = 2;
}

message DecayParamsExpression {
    // The variable to decay
    Expression x = 1;
    // The target value to start decaying from. Defaults to 0.
    optional Expression target = 2;
    // The scale factor of the decay, in terms of `x`. Defaults to 1.0. Must be a non-zero positive number.
    optional float scale = 3;
    // The midpoint of the decay. Defaults to 0.5. Output will be this value when `|x - target| == scale`.
    optional float midpoint = 4;
}

message Query {
  oneof variant {
    VectorInput nearest = 1; // Find the nearest neighbors to this vector.
    RecommendInput recommend = 2; // Use multiple positive and negative vectors to find the results.
    DiscoverInput discover = 3; // Search for nearest points, but constrain the search space with context
    ContextInput context = 4; // Return points that live in positive areas.
    OrderBy order_by = 5; // Order the points by a payload field.
    Fusion fusion = 6; // Fuse the results of multiple prefetches.
    Sample sample = 7; // Sample points from the collection.
    Formula formula = 8; // Score boosting via an arbitrary formula
  }
}

message PrefetchQuery {
  repeated PrefetchQuery prefetch = 1; // Sub-requests to perform first. If present, the query will be performed on the results of the prefetches.
  optional Query query = 2; // Query to perform. If missing, returns points ordered by their IDs.
  optional string using = 3; // Define which vector to use for querying. If missing, the default vector is is used.
  optional Filter filter = 4; // Filter conditions - return only those points that satisfy the specified conditions.
  optional SearchParams params = 5; // Search params for when there is no prefetch.
  optional float score_threshold = 6; // Return points with scores better than this threshold.
  optional uint64 limit = 7; // Max number of points. Default is 10
  optional LookupLocation lookup_from = 8; // The location to use for IDs lookup, if not specified - use the current collection and the 'using' vector
}

message QueryPoints {
  string collection_name = 1; // Name of the collection
  repeated PrefetchQuery prefetch = 2; // Sub-requests to perform first. If present, the query will be performed on the results of the prefetches.
  optional Query query = 3; // Query to perform. If missing, returns points ordered by their IDs.
  optional string using = 4; // Define which vector to use for querying. If missing, the default vector is used.
  optional Filter filter = 5; // Filter conditions - return only those points that satisfy the specified conditions.
  optional SearchParams params = 6; // Search params for when there is no prefetch.
  optional float score_threshold = 7; // Return points with scores better than this threshold.
  optional uint64 limit = 8; // Max number of points. Default is 10.
  optional uint64 offset = 9; // Offset of the result. Skip this many points. Default is 0.
  optional WithVectorsSelector with_vectors = 10; // Options for specifying which vectors to include into the response.
  optional WithPayloadSelector with_payload = 11; // Options for specifying which payload to include or not.
  optional ReadConsistency read_consistency = 12; // Options for specifying read consistency guarantees.
  optional ShardKeySelector shard_key_selector = 13; // Specify in which shards to look for the points, if not specified - look in all shards.
  optional LookupLocation lookup_from = 14; // The location to use for IDs lookup, if not specified - use the current collection and the 'using' vector
  optional uint64 timeout = 15; // If set, overrides global timeout setting for this request. Unit is seconds.
}

message QueryBatchPoints {
  string collection_name = 1;
  repeated QueryPoints query_points = 2;
  optional ReadConsistency read_consistency = 3; // Options for specifying read consistency guarantees
  optional uint64 timeout = 4; // If set, overrides global timeout setting for this request. Unit is seconds.
}

message QueryPointGroups {
  string collection_name = 1; // Name of the collection
  repeated PrefetchQuery prefetch = 2; // Sub-requests to perform first. If present, the query will be performed on the results of the prefetches.
  optional Query query = 3; // Query to perform. If missing, returns points ordered by their IDs.
  optional string using = 4; // Define which vector to use for querying. If missing, the default vector is used.
  optional Filter filter = 5; // Filter conditions - return only those points that satisfy the specified conditions.
  optional SearchParams params = 6; // Search params for when there is no prefetch.
  optional float score_threshold = 7; // Return points with scores better than this threshold.
  WithPayloadSelector with_payload = 8; // Options for specifying which payload to include or not
  optional WithVectorsSelector with_vectors = 9; // Options for specifying which vectors to include into response
  optional LookupLocation lookup_from = 10; // The location to use for IDs lookup, if not specified - use the current collection and the 'using' vector
  optional uint64 limit = 11; // Max number of points. Default is 3.
  optional uint64 group_size = 12; // Maximum amount of points to return per group. Default to 10.
  string group_by = 13; // Payload field to group by, must be a string or number field. If there are multiple values for the field, all of them will be used. One point can be in multiple groups.
  optional ReadConsistency read_consistency = 14; // Options for specifying read consistency guarantees
  optional WithLookup with_lookup = 15; // Options for specifying how to use the group id to lookup points in another collection
  optional uint64 timeout = 16; // If set, overrides global timeout setting for this request. Unit is seconds.
  optional ShardKeySelector shard_key_selector = 17; // Specify in which shards to look for the points, if not specified - look in all shards
}

message FacetCounts {
    string collection_name = 1; // Name of the collection
    string key = 2; // Payload key of the facet
    optional Filter filter = 3; // Filter conditions - return only those points that satisfy the specified conditions.
    optional uint64 limit = 4; // Max number of facets. Default is 10.
    optional bool exact = 5; // If true, return exact counts, slower but useful for debugging purposes. Default is false.
    optional uint64 timeout = 6; // If set, overrides global timeout setting for this request. Unit is seconds.
    optional ReadConsistency read_consistency = 7; // Options for specifying read consistency guarantees
    optional ShardKeySelector shard_key_selector = 8; // Specify in which shards to look for the points, if not specified - look in all shards
}

message FacetValue {
    oneof variant {
        string string_value = 1; // String value from the facet
        int64 integer_value = 2; // Integer value from the facet
        bool bool_value = 3; // Boolean value from the facet
    }
}

message FacetHit {
    FacetValue value = 1; // Value from the facet
    uint64 count = 2; // Number of points with this value
}

message SearchMatrixPoints {
  string collection_name = 1; // Name of the collection
  optional Filter filter = 2; // Filter conditions - return only those points that satisfy the specified conditions.
  optional uint64 sample = 3; // How many points to select and search within. Default is 10.
  optional uint64 limit = 4; // How many neighbours per sample to find. Default is 3.
  optional string using = 5; // Define which vector to use for querying. If missing, the default vector is is used.
  optional uint64 timeout = 6; // If set, overrides global timeout setting for this request. Unit is seconds.
  optional ReadConsistency read_consistency = 7; // Options for specifying read consistency guarantees
  optional ShardKeySelector shard_key_selector = 8; // Specify in which shards to look for the points, if not specified - look in all shards
}

message SearchMatrixPairs {
  repeated SearchMatrixPair pairs = 1; // List of pairs of points with scores
}

message SearchMatrixPair {
  PointId a = 1; // first id of the pair
  PointId b = 2; // second id of the pair
  float score = 3; // score of the pair
}

message SearchMatrixOffsets {
  repeated uint64 offsets_row = 1; // Row indices of the matrix
  repeated uint64 offsets_col = 2; // Column indices of the matrix
  repeated float scores = 3; // Scores associated with matrix coordinates
  repeated PointId ids = 4; // Ids of the points in order
}


message PointsUpdateOperation {
  message PointStructList {
    repeated PointStruct points = 1;
    optional ShardKeySelector shard_key_selector = 2; // Option for custom sharding to specify used shard keys
  }
  message SetPayload {
      map<string, Value> payload = 1;
      optional PointsSelector points_selector = 2; // Affected points
      optional ShardKeySelector shard_key_selector = 3; // Option for custom sharding to specify used shard keys
      optional string key = 4; // Option for indicate property of payload
  }
  message OverwritePayload {
      map<string, Value> payload = 1;
      optional PointsSelector points_selector = 2; // Affected points
      optional ShardKeySelector shard_key_selector = 3; // Option for custom sharding to specify used shard keys
      optional string key = 4; // Option for indicate property of payload
  }
  message DeletePayload {
      repeated string keys = 1;
      optional PointsSelector points_selector = 2; // Affected points
      optional ShardKeySelector shard_key_selector = 3; // Option for custom sharding to specify used shard keys
  }
  message UpdateVectors {
    repeated PointVectors points = 1; // List of points and vectors to update
    optional ShardKeySelector shard_key_selector = 2; // Option for custom sharding to specify used shard keys
  }
  message DeleteVectors {
    PointsSelector points_selector = 1; // Affected points
    VectorsSelector vectors = 2; // List of vector names to delete
    optional ShardKeySelector shard_key_selector = 3; // Option for custom sharding to specify used shard keys
  }
  message DeletePoints {
    PointsSelector points = 1; // Affected points
    optional ShardKeySelector shard_key_selector = 2; // Option for custom sharding to specify used shard keys
  }
  message ClearPayload {
    PointsSelector points = 1; // Affected points
    optional ShardKeySelector shard_key_selector = 2; // Option for custom sharding to specify used shard keys
  }

  oneof operation {
    PointStructList upsert = 1;
    PointsSelector delete_deprecated = 2 [deprecated=true];
    SetPayload set_payload = 3;
    OverwritePayload overwrite_payload = 4;
    DeletePayload delete_payload = 5;
    PointsSelector clear_payload_deprecated = 6 [deprecated=true];
    UpdateVectors update_vectors = 7;
    DeleteVectors delete_vectors = 8;
    DeletePoints delete_points = 9;
    ClearPayload clear_payload = 10;
  }
}

message UpdateBatchPoints {
  string collection_name = 1; // name of the collection
  optional bool wait = 2; // Wait until the changes have been applied?
  repeated PointsUpdateOperation operations = 3;
  optional WriteOrdering ordering = 4; // Write ordering guarantees
}

// ---------------------------------------------
// ---------------- RPC Response ---------------
// ---------------------------------------------

message PointsOperationResponse {
  UpdateResult result = 1;
  double time = 2; // Time spent to process
  optional HardwareUsage usage = 3;
}

message UpdateResult {
  optional uint64 operation_id = 1; // Number of operation
  UpdateStatus status = 2; // Operation status
}

enum UpdateStatus {
  UnknownUpdateStatus = 0;
  Acknowledged = 1; // Update is received, but not processed yet
  Completed = 2; // Update is applied and ready for search
  ClockRejected = 3; // Internal: update is rejected due to an outdated clock
}

message OrderValue {
  oneof variant {
    int64 int = 1;
    double float = 2;
  }
}

message ScoredPoint {
  PointId id = 1; // Point id
  map<string, Value> payload = 2; // Payload
  float score = 3; // Similarity score
  reserved 4; // deprecated "vector" field
  uint64 version = 5; // Last update operation applied to this point
  optional VectorsOutput vectors = 6; // Vectors to search
  optional ShardKey shard_key = 7; // Shard key
  optional OrderValue order_value = 8; // Order by value
}

message GroupId {
  oneof kind {
    // Represents a double value.
    uint64 unsigned_value = 1;
    // Represents an integer value
    int64 integer_value = 2;
    // Represents a string value.
    string string_value = 3;
  }
}

message PointGroup {
  GroupId id = 1; // Group id
  repeated ScoredPoint hits = 2; // Points in the group
  RetrievedPoint lookup = 3; // Point(s) from the lookup collection that matches the group id
}

message GroupsResult {
  repeated PointGroup groups = 1; // Groups
}

message SearchResponse {
  repeated ScoredPoint result = 1;
  double time = 2; // Time spent to process
  optional HardwareUsage usage = 3;
}

message QueryResponse {
  repeated ScoredPoint result = 1;
  double time = 2; // Time spent to process
  optional HardwareUsage usage = 3;
}

message QueryBatchResponse {
  repeated BatchResult result = 1;
  double time = 2; // Time spent to process
  optional HardwareUsage usage = 3;
}

message QueryGroupsResponse {
  GroupsResult result = 1;
  double time = 2; // Time spent to process
  optional HardwareUsage usage = 3;
}

message BatchResult {
  repeated ScoredPoint result = 1;
}

message SearchBatchResponse {
  repeated BatchResult result = 1;
  double time = 2; // Time spent to process
  optional HardwareUsage usage = 3;
}

message SearchGroupsResponse {
  GroupsResult result = 1;
  double time = 2; // Time spent to process
  optional HardwareUsage usage = 3;
}

message CountResponse {
  CountResult result = 1;
  double time = 2; // Time spent to process
  optional HardwareUsage usage = 3;
}

message ScrollResponse {
  optional PointId next_page_offset = 1; // Use this offset for the next query
  repeated RetrievedPoint result = 2;
  double time = 3; // Time spent to process
  optional HardwareUsage usage = 4;
}

message CountResult {
  uint64 count = 1;
}

message RetrievedPoint {
  PointId id = 1;
  map<string, Value> payload = 2;
  reserved 3; // deprecated "vector" field
  optional VectorsOutput vectors = 4;
  optional ShardKey shard_key = 5; // Shard key
  optional OrderValue order_value = 6; // Order-by value
}

message GetResponse {
  repeated RetrievedPoint result = 1;
  double time = 2; // Time spent to process
  optional HardwareUsage usage = 3;
}

message RecommendResponse {
  repeated ScoredPoint result = 1;
  double time = 2; // Time spent to process
  optional HardwareUsage usage = 3;
}

message RecommendBatchResponse {
  repeated BatchResult result = 1;
  double time = 2; // Time spent to process
  optional HardwareUsage usage = 3;
}

message DiscoverResponse {
  repeated ScoredPoint result = 1;
  double time = 2; // Time spent to process
  optional HardwareUsage usage = 3;
}

message DiscoverBatchResponse {
  repeated BatchResult result = 1;
  double time = 2; // Time spent to process
  optional HardwareUsage usage = 3;
}

message RecommendGroupsResponse {
  GroupsResult result = 1;
  double time = 2; // Time spent to process
  optional HardwareUsage usage = 3;
}

message UpdateBatchResponse {
  repeated UpdateResult result = 1;
  double time = 2; // Time spent to process
}

message FacetResponse {
    repeated FacetHit hits = 1;
    double time = 2; // Time spent to process
}

message SearchMatrixPairsResponse {
  SearchMatrixPairs result = 1;
  double time = 2; // Time spent to process
  optional HardwareUsage usage = 3;
}

message SearchMatrixOffsetsResponse {
  SearchMatrixOffsets result = 1;
  double time = 2; // Time spent to process
  optional HardwareUsage usage = 3;
}

// ---------------------------------------------
// ------------- Filter Conditions -------------
// ---------------------------------------------

message Filter {
  repeated Condition should = 1; // At least one of those conditions should match
  repeated Condition must = 2; // All conditions must match
  repeated Condition must_not = 3; // All conditions must NOT match
  optional MinShould min_should = 4; // At least minimum amount of given conditions should match
}

message MinShould {
  repeated Condition conditions = 1;
  uint64 min_count = 2;
}

message Condition {
  oneof condition_one_of {
    FieldCondition field = 1;
    IsEmptyCondition is_empty = 2;
    HasIdCondition has_id = 3;
    Filter filter = 4;
    IsNullCondition is_null = 5;
    NestedCondition nested = 6;
    HasVectorCondition has_vector = 7;
  }
}

message IsEmptyCondition {
  string key = 1;
}

message IsNullCondition {
    string key = 1;
}

message HasIdCondition {
  repeated PointId has_id = 1;
}

message HasVectorCondition {
  string has_vector = 1;
}

message NestedCondition {
  string key = 1; // Path to nested object
  Filter filter = 2; // Filter condition
}

message FieldCondition {
  string key = 1;
  Match match = 2; // Check if point has field with a given value
  Range range = 3; // Check if points value lies in a given range
  GeoBoundingBox geo_bounding_box = 4; // Check if points geolocation lies in a given area
  GeoRadius geo_radius = 5; // Check if geo point is within a given radius
  ValuesCount values_count = 6; // Check number of values for a specific field
  GeoPolygon geo_polygon = 7; // Check if geo point is within a given polygon
  DatetimeRange datetime_range = 8; // Check if datetime is within a given range
  optional bool is_empty = 9; // Check if field is empty
  optional bool is_null = 10; // Check if field is null
}

message Match {
  oneof match_value {
    string keyword = 1; // Match string keyword
    int64 integer = 2; // Match integer
    bool boolean = 3; // Match boolean
    string text = 4; // Match text
    RepeatedStrings keywords = 5; // Match multiple keywords
    RepeatedIntegers integers = 6; // Match multiple integers
    RepeatedIntegers except_integers = 7; // Match any other value except those integers
    RepeatedStrings except_keywords = 8; // Match any other value except those keywords
  }
}

message RepeatedStrings {
  repeated string strings = 1;
}

message RepeatedIntegers {
  repeated int64 integers = 1;
}

message Range {
  optional double lt = 1;
  optional double gt = 2;
  optional double gte = 3;
  optional double lte = 4;
}

message DatetimeRange {
  optional google.protobuf.Timestamp lt = 1;
  optional google.protobuf.Timestamp gt = 2;
  optional google.protobuf.Timestamp gte = 3;
  optional google.protobuf.Timestamp lte = 4;
}

message GeoBoundingBox {
  GeoPoint top_left = 1; // north-west corner
  GeoPoint bottom_right = 2; // south-east corner
}

message GeoRadius {
  GeoPoint center = 1; // Center of the circle
  float radius = 2; // In meters
}

message GeoLineString {
  repeated GeoPoint points = 1;  // Ordered sequence of GeoPoints representing the line
}

// For a valid GeoPolygon, both the exterior and interior GeoLineStrings must consist of a minimum of 4 points.
// Additionally, the first and last points of each GeoLineString must be the same.
message GeoPolygon {
  GeoLineString exterior = 1; // The exterior line bounds the surface
  repeated GeoLineString interiors = 2; // Interior lines (if present) bound holes within the surface
}

message ValuesCount {
  optional uint64 lt = 1;
  optional uint64 gt = 2;
  optional uint64 gte = 3;
  optional uint64 lte = 4;
}

// ---------------------------------------------
// -------------- Points Selector --------------
// ---------------------------------------------

message PointsSelector {
  oneof points_selector_one_of {
    PointsIdsList points = 1;
    Filter filter = 2;
  }
}

message PointsIdsList {
  repeated PointId ids = 1;
}

// ---------------------------------------------
// ------------------- Point -------------------
// ---------------------------------------------


message PointStruct {
  PointId id = 1;
  reserved 2; // deprecated "vector" field
  map<string, Value> payload = 3;
  optional Vectors vectors = 4;
}


message GeoPoint {
  double lon = 1;
  double lat = 2;
}

// ---------------------------------------------
// ------------ Hardware measurements ----------
// ---------------------------------------------

message HardwareUsage {
  uint64 cpu = 1;
  uint64 payload_io_read = 2;
  uint64 payload_io_write = 3;
  uint64 payload_index_io_read = 4;
  uint64 payload_index_io_write = 5;
  uint64 vector_io_read = 6;
  uint64 vector_io_write = 7;
}
