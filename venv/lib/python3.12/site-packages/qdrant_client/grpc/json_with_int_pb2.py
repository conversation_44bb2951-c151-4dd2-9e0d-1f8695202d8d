# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: json_with_int.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13json_with_int.proto\x12\x06qdrant\"r\n\x06Struct\x12*\n\x06\x66ields\x18\x01 \x03(\x0b\x32\x1a.qdrant.Struct.FieldsEntry\x1a<\n\x0b\x46ieldsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x1c\n\x05value\x18\x02 \x01(\x0b\x32\r.qdrant.Value:\x02\x38\x01\"\xe8\x01\n\x05Value\x12\'\n\nnull_value\x18\x01 \x01(\x0e\x32\x11.qdrant.NullValueH\x00\x12\x16\n\x0c\x64ouble_value\x18\x02 \x01(\x01H\x00\x12\x17\n\rinteger_value\x18\x03 \x01(\x03H\x00\x12\x16\n\x0cstring_value\x18\x04 \x01(\tH\x00\x12\x14\n\nbool_value\x18\x05 \x01(\x08H\x00\x12&\n\x0cstruct_value\x18\x06 \x01(\x0b\x32\x0e.qdrant.StructH\x00\x12\'\n\nlist_value\x18\x07 \x01(\x0b\x32\x11.qdrant.ListValueH\x00\x42\x06\n\x04kind\"*\n\tListValue\x12\x1d\n\x06values\x18\x01 \x03(\x0b\x32\r.qdrant.Value*\x1b\n\tNullValue\x12\x0e\n\nNULL_VALUE\x10\x00\x42\x15\xaa\x02\x12Qdrant.Client.Grpcb\x06proto3')

_NULLVALUE = DESCRIPTOR.enum_types_by_name['NullValue']
NullValue = enum_type_wrapper.EnumTypeWrapper(_NULLVALUE)
NULL_VALUE = 0


_STRUCT = DESCRIPTOR.message_types_by_name['Struct']
_STRUCT_FIELDSENTRY = _STRUCT.nested_types_by_name['FieldsEntry']
_VALUE = DESCRIPTOR.message_types_by_name['Value']
_LISTVALUE = DESCRIPTOR.message_types_by_name['ListValue']
Struct = _reflection.GeneratedProtocolMessageType('Struct', (_message.Message,), {

  'FieldsEntry' : _reflection.GeneratedProtocolMessageType('FieldsEntry', (_message.Message,), {
    'DESCRIPTOR' : _STRUCT_FIELDSENTRY,
    '__module__' : 'json_with_int_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.Struct.FieldsEntry)
    })
  ,
  'DESCRIPTOR' : _STRUCT,
  '__module__' : 'json_with_int_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.Struct)
  })
_sym_db.RegisterMessage(Struct)
_sym_db.RegisterMessage(Struct.FieldsEntry)

Value = _reflection.GeneratedProtocolMessageType('Value', (_message.Message,), {
  'DESCRIPTOR' : _VALUE,
  '__module__' : 'json_with_int_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.Value)
  })
_sym_db.RegisterMessage(Value)

ListValue = _reflection.GeneratedProtocolMessageType('ListValue', (_message.Message,), {
  'DESCRIPTOR' : _LISTVALUE,
  '__module__' : 'json_with_int_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ListValue)
  })
_sym_db.RegisterMessage(ListValue)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\252\002\022Qdrant.Client.Grpc'
  _STRUCT_FIELDSENTRY._options = None
  _STRUCT_FIELDSENTRY._serialized_options = b'8\001'
  _NULLVALUE._serialized_start=426
  _NULLVALUE._serialized_end=453
  _STRUCT._serialized_start=31
  _STRUCT._serialized_end=145
  _STRUCT_FIELDSENTRY._serialized_start=85
  _STRUCT_FIELDSENTRY._serialized_end=145
  _VALUE._serialized_start=148
  _VALUE._serialized_end=380
  _LISTVALUE._serialized_start=382
  _LISTVALUE._serialized_end=424
# @@protoc_insertion_point(module_scope)
