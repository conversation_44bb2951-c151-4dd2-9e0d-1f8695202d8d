# PRD: MCP Server Evolution Intelligence Enhancement - Feature 1

## Executive Summary

Enhance Memory Master v2's existing production MCP server (`api/app/mcp_server.py`) with mem0's intelligent two-phase memory pipeline to enable automatic conflict resolution, memory updates, and intelligent filtering. This transforms the current basic memory storage into an adaptive system that learns and evolves user memories intelligently.

## Current State Analysis

### Existing MCP Server Capabilities

- **Basic Memory Operations**: `add_memories()`, `search_memory()`, `list_memories()`, `delete_all_memories()`
- **Production Features**: Transaction-based chunking, retry logic, database integration, access controls
- **Current add_memories() Behavior**: Simple storage - all text gets stored as new memories without intelligence

### Current Limitation

```python
# Current behavior (basic storage):
User says: "I love Python programming"
→ System: Creates new memory "I love Python programming"

User says: "I work with Python daily for machine learning" 
→ System: Creates separate memory "I work with Python daily for machine learning"

User says: "Actually, I hate Python now, I prefer JavaScript"
→ System: Creates third memory "Actually, I hate Python now, I prefer JavaScript"

# Result: 3 contradictory memories with no conflict resolution
```

## Feature Overview: Self-Evolving Memory Intelligence

### What is the Two-Phase Pipeline?

#### Phase 1: Extraction Phase

**Purpose**: Extract meaningful, lasting facts from conversational input **Process**: LLM analyzes conversation context and identifies memorable information

```
Input: Raw conversation text
Context: {
  "latest_exchange": current_message,
  "rolling_summary": background_context,
  "recent_messages": last_10_messages
}
Output: List of candidate facts
```

**Example Extraction**:

```
Input: "Hey, I've been working on a React project lately, but I'm thinking of switching to Vue.js for my next one"

Extracted Facts:
- "Currently working on React project" 
- "Considering Vue.js for next project"
- "Prefers frontend frameworks for development"
```

#### Phase 2: Update Phase (The Intelligence Core)

**Purpose**: Decide what to do with each extracted fact based on existing memories **Process**: For each candidate fact, LLM compares against similar existing memories and chooses an operation

## The Four Evolution Operations (ADD/UPDATE/DELETE/NOOP)

### 1. ADD Operation

**When**: New information that doesn't exist in memory **Logic**: No semantically similar memory found OR similarity score below threshold (typically 0.85) **Example**:

```
Candidate Fact: "Uses VS Code as primary editor"
Existing Memories: ["Loves Python programming", "Works in machine learning"]
Decision: ADD
Reason: No existing memory about code editors
Result: New memory created
```

### 2. UPDATE Operation

**When**: New information enhances or provides more detail to existing memory **Logic**: Semantically similar memory exists AND new information complements/enhances it **Example**:

```
Candidate Fact: "Uses Python for data science and web development"
Existing Memory: "Loves Python programming"
Similarity Score: 0.92 (high similarity)
Decision: UPDATE
Reason: New fact adds specific details about Python usage
Result: Memory updated to "Loves Python programming, specifically for data science and web development"
```

### 3. DELETE Operation

**When**: New information contradicts and supersedes existing memory **Logic**: Semantically similar memory exists AND new information contradicts it **Example**:

```
Candidate Fact: "Switched from Python to JavaScript as primary language"
Existing Memory: "Loves Python programming"
Similarity Score: 0.89 (high similarity, same topic)
Decision: DELETE + ADD
Reason: Direct contradiction detected
Result: Old Python memory deleted, new JavaScript preference added
```

### 4. NOOP Operation

**When**: Information is redundant, irrelevant, or too trivial to store **Logic**: Information already exists OR not worth storing **Example**:

```
Candidate Fact: "Likes programming"
Existing Memory: "Loves Python programming, uses it for data science"
Decision: NOOP
Reason: Information is already covered by existing memory
Result: No change to memory store
```

## Decision Logic Flow

### Similarity-Based Decision Tree

```
For each candidate_fact:
  1. Find semantically similar memories (vector similarity search)
  2. If no similar memories found OR max_similarity < 0.85:
     → ADD (store as new memory)
  
  3. If similar memory found AND max_similarity >= 0.85:
     a. Analyze semantic relationship:
        - If complementary/enhancing: → UPDATE
        - If contradictory/conflicting: → DELETE old + ADD new  
        - If redundant/duplicate: → NOOP
        - If unclear: → ADD (conservative approach)
```

### Conflict Detection Algorithm

```
Conflict Detection Criteria:
- Temporal conflicts: "loved X yesterday" vs "hate X now"
- Preference reversals: "prefers A" vs "prefers B" (where A≠B)
- Factual contradictions: "works at Company A" vs "works at Company B"
- Capability changes: "doesn't know X" vs "expert in X"

Resolution Strategy:
- More recent information takes precedence
- More specific information takes precedence  
- Explicit contradictions trigger DELETE + ADD
```

## Enhanced MCP Server Implementation Plan

### 1. Enhanced add_memories() Function

#### Current Function Signature

```python
@mcp.tool(description="Add a new memory...")
async def add_memories(text: str) -> str:
    # Current: Basic storage with chunking
```

#### Enhanced Function Behavior

```python
@mcp.tool(description="Add memory with intelligent evolution...")
async def add_memories(text: str) -> str:
    # Phase 1: Extract facts using custom prompts
    # Phase 2: For each fact, decide ADD/UPDATE/DELETE/NOOP
    # Phase 3: Execute operations with existing transaction logic
    # Phase 4: Track evolution statistics
```

### 2. Evolution Decision Engine

#### Core Components

1. **Fact Extraction Module**: Uses custom LLM prompts to extract meaningful facts
2. **Similarity Analysis Module**: Compares candidate facts against existing memories
3. **Decision Logic Module**: Implements ADD/UPDATE/DELETE/NOOP logic
4. **Conflict Resolution Module**: Handles contradictions and preference changes
5. **Statistics Tracking Module**: Records evolution operations and learning metrics

#### Custom Prompts for Technical Domain

##### Fact Extraction Prompt

```
You are an expert at extracting important, memorable facts from technical conversations.

Context:
- Latest message: {latest_message}
- Recent conversation: {recent_messages}
- User background: {user_context}

Extract only lasting facts about:
1. Technical preferences (languages, frameworks, tools)
2. Project decisions and requirements  
3. Learning goals and skill development
4. Work context and professional information
5. Problem-solving approaches and methodologies

Ignore:
- Casual greetings and acknowledgments
- Temporary troubleshooting steps
- Procedural conversation elements
- Obvious or widely-known information

Return facts as complete, standalone statements.
Example: "Prefers TypeScript over JavaScript for large projects"
```

##### Memory Update Decision Prompt

```
You are an expert memory manager deciding how to handle new technical information.

Given:
- New fact: {candidate_fact}
- Existing memory: {existing_memory}
- Similarity score: {similarity_score}

Analyze the relationship and choose ONE action:

ADD: If new fact covers different topic or adds new domain
UPDATE: If new fact enhances/specifies existing information  
DELETE: If new fact contradicts/supersedes existing information
NOOP: If new fact is redundant or already covered

Technical Decision Rules:
- Technology migrations: "switched from X to Y" → DELETE X, ADD Y
- Skill progression: "learning X" → "expert in X" → UPDATE
- Project evolution: "working on X" → "completed X" → UPDATE
- Preference changes: "likes X" → "prefers Y over X" → DELETE X, ADD Y

Response format:
{
  "action": "ADD|UPDATE|DELETE|NOOP",
  "reasoning": "Detailed explanation",
  "confidence": 0.95
}
```

### 3. Database Schema Enhancement

#### New Tables

```sql
-- Evolution operations tracking
CREATE TABLE evolution_operations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    memory_id UUID REFERENCES memories(id),
    user_id UUID REFERENCES users(id),
    operation_type evolution_operation_enum NOT NULL,
    candidate_fact TEXT NOT NULL,
    existing_memory_content TEXT,
    similarity_score FLOAT,
    confidence_score FLOAT,
    reasoning TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB
);

-- Evolution operation types
CREATE TYPE evolution_operation_enum AS ENUM ('ADD', 'UPDATE', 'DELETE', 'NOOP');

-- Evolution insights aggregation
CREATE TABLE evolution_insights (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    date DATE NOT NULL,
    total_operations INTEGER DEFAULT 0,
    add_operations INTEGER DEFAULT 0,
    update_operations INTEGER DEFAULT 0,
    delete_operations INTEGER DEFAULT 0,
    noop_operations INTEGER DEFAULT 0,
    learning_efficiency FLOAT, -- (UPDATE + DELETE) / total_operations
    conflict_resolution_count INTEGER DEFAULT 0,
    average_confidence FLOAT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, date)
);
```

### 4. New MCP Tools

#### Evolution Metrics Tool

```python
@mcp.tool(description="Get evolution statistics and learning efficiency")
async def get_evolution_metrics(timeframe: str = "week") -> str:
    """
    Returns:
    - Learning efficiency percentage
    - Operation breakdown (ADD/UPDATE/DELETE/NOOP)
    - Conflict resolution activity
    - Memory quality trends
    """
```

#### Learning Insights Tool

```python
@mcp.tool(description="Get user-specific learning insights")
async def get_learning_insights() -> str:
    """
    Returns:
    - Personal learning efficiency
    - Memory categories and distribution
    - Recent evolution activity
    - Conflict resolution history
    """
```

#### Evolution Monitor Tool

```python
@mcp.tool(description="Monitor real-time evolution activity")
async def monitor_evolution() -> str:
    """
    Returns:
    - Recent evolution operations
    - Active learning patterns
    - Memory quality indicators
    - System intelligence metrics
    """
```

## Integration with Existing Infrastructure

### Preserve Existing Functionality

- **Chunking Logic**: Evolution intelligence applied to each chunk
- **Transaction System**: Evolution operations wrapped in existing transactions
- **Retry Logic**: Evolution decisions retry on failure with existing backoff
- **Access Controls**: Evolution statistics respect existing user/app permissions
- **Database Integration**: New tables integrate with existing schema relationships

### Enhanced Transaction Flow

```python
# Enhanced transaction with evolution intelligence
class EvolutionTransaction(MemoryTransaction):
    def add_memory_chunk_with_evolution(self, content: str, metadata: dict):
        # 1. Extract facts using custom prompts
        # 2. For each fact, find similar memories
        # 3. Apply evolution logic (ADD/UPDATE/DELETE/NOOP)
        # 4. Track evolution statistics
        # 5. Use existing transaction commit logic
```

## Success Criteria

### Functional Requirements

- [ ] Enhanced `add_memories()` executes intelligent evolution logic
- [ ] All four operations (ADD/UPDATE/DELETE/NOOP) work correctly
- [ ] Custom prompts extract relevant technical facts
- [ ] Conflict resolution handles contradictory information
- [ ] Evolution statistics tracked accurately in database
- [ ] New MCP tools provide evolution insights
- [ ] Backward compatibility with existing MCP functionality maintained

### Performance Requirements

- [ ] Evolution processing adds <1s to existing `add_memories()` response time
- [ ] Database queries optimized with proper indexing
- [ ] Memory usage impact <10% of current system resources
- [ ] All existing MCP performance standards maintained

### Quality Requirements

- [ ] Learning efficiency >40% (intelligent operations vs basic ADD)
- [ ] Conflict detection accuracy >85% for obvious contradictions
- [ ] Fact extraction relevance >90% for technical conversations
- [ ] Evolution decision confidence >80% average

## Risk Mitigation

### Technical Risks

- **LLM Prompt Reliability**: Extensive testing with varied inputs, fallback to ADD operation
- **Performance Impact**: Caching of similarity searches, optimized database queries
- **Evolution Logic Errors**: Comprehensive unit tests, gradual rollout with monitoring
- **Integration Complexity**: Build on existing patterns, preserve all current functionality

### Operational Risks

- **Production Stability**: Feature flags for evolution intelligence, rollback capabilities
- **User Experience**: Enhanced functionality is additive, existing workflows unchanged
- **Data Integrity**: Evolution operations logged for audit trail, reversible operations

## Testing Strategy

### Unit Testing

- [ ] Fact extraction with various input types
- [ ] Similarity analysis with known memory pairs
- [ ] Evolution decision logic for each operation type
- [ ] Custom prompt effectiveness validation
- [ ] Database schema and operations testing

### Integration Testing

- [ ] End-to-end evolution workflows
- [ ] Compatibility with existing MCP functionality
- [ ] Transaction rollback with evolution operations
- [ ] Performance impact on existing operations
- [ ] Multi-user evolution isolation

### User Acceptance Testing

- [ ] Technical conversation evolution accuracy
- [ ] Conflict resolution effectiveness
- [ ] Learning efficiency improvements
- [ ] MCP tool usability and insights value

## Definition of Done

- [ ] Enhanced `add_memories()` implements full two-phase pipeline
- [ ] All four evolution operations (ADD/UPDATE/DELETE/NOOP) functional
- [ ] Database schema deployed with proper relationships and indexes
- [ ] Custom prompts tuned for technical domain effectiveness
- [ ] New MCP tools provide actionable evolution insights
- [ ] Comprehensive testing suite passing (unit + integration + performance)
- [ ] Documentation updated with evolution intelligence capabilities
- [ ] Backward compatibility verified with existing MCP clients
- [ ] Performance benchmarks met without degrading existing functionality
- [ ] Production deployment successful with feature flag controls

This enhancement transforms Memory Master v2's MCP server from a basic storage system into an intelligent, adaptive memory system that learns and evolves user knowledge automatically.